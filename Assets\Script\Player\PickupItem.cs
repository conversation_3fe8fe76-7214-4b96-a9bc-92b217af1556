using UnityEngine;

namespace Player
{
    /// <summary>
    /// Component cho vật phẩm có thể nhặt
    /// Ch<PERSON>a thông tin vật phẩm và logic pickup
    /// Hỗ trợ highlight effects và hover text
    /// </summary>
    [RequireComponent(typeof(Collider))]
    public class PickupItem : MonoBehaviour
    {
        #region Serialized Fields
        [Header("🎯 Thông Tin Vật Phẩm")]
        [SerializeField] private string m_ItemId = "item_001";
        [SerializeField] private int m_SoLuong = 1;
        [SerializeField] private string m_TenHienThi = "Vật Phẩm";
        [SerializeField] private string m_MoTa = "Một vật phẩm có thể nhặt";
        [SerializeField] private Sprite m_Icon;

        [Header("🎮 Cài Đặt Pickup")]
        [SerializeField] private string m_PickupTag = "Pickupable";
        [SerializeField] private bool m_CoTheNhat = true;
        [SerializeField] private bool m_TuDongThemVaoInventory = true;
        [SerializeField] private bool m_XoaSauKhiNhat = true;

        [Header("🎨 Hiệu Ứng Visual")]
        [SerializeField] private Material m_HighlightMaterial;
        [SerializeField] private Color m_OutlineColor = Color.yellow;
        [SerializeField] private float m_OutlineWidth = 0.1f;
        [SerializeField] private bool m_CoBobbing = true;
        [SerializeField] private float m_TocDoBobbing = 1.0f;
        [SerializeField] private float m_DoCaoBobbing = 0.1f;

        [Header("🔊 Hiệu Ứng Âm Thanh")]
        [SerializeField] private AudioClip m_SoundHighlight;
        [SerializeField] private AudioClip m_SoundPickup;
        [SerializeField] private AudioClip m_SoundCannotPickup;

        [Header("✨ Particle Effects")]
        [SerializeField] private ParticleSystem m_EffectHighlight;
        [SerializeField] private ParticleSystem m_EffectPickup;

        [Header("🐛 Debug")]
        [SerializeField] private bool m_EnableDebugLogs = true;
        #endregion

        #region Private Fields
        private Renderer m_Renderer;
        private Material m_OriginalMaterial;
        private Vector3 m_OriginalPosition;
        private bool m_IsHighlighted;
        private bool m_IsPickedUp;
        private AudioSource m_AudioSource;
        
        // Bobbing animation
        private float m_BobbingTime;
        #endregion

        #region Properties
        public string ItemId => m_ItemId;
        public int SoLuong => m_SoLuong;
        public string TenHienThi => m_TenHienThi;
        public string MoTa => m_MoTa;
        public Sprite Icon => m_Icon;
        public bool CoTheNhat => m_CoTheNhat && !m_IsPickedUp;
        public bool IsHighlighted => m_IsHighlighted;
        public bool IsPickedUp => m_IsPickedUp;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            KhoiTaoComponents();
        }

        private void Start()
        {
            KhoiTaoVatPham();
        }

        private void Update()
        {
            if (m_CoBobbing && !m_IsPickedUp)
            {
                ThucHienBobbingAnimation();
            }
        }

        private void OnValidate()
        {
            // Đảm bảo tag đúng
            if (!string.IsNullOrEmpty(m_PickupTag) && !gameObject.CompareTag(m_PickupTag))
            {
                try
                {
                    gameObject.tag = m_PickupTag;
                }
                catch (UnityException)
                {
                    Debug.LogWarning($"Tag '{m_PickupTag}' không tồn tại. Vui lòng tạo tag này trong Tag Manager.");
                }
            }
        }
        #endregion

        #region Initialization
        private void KhoiTaoComponents()
        {
            // Lấy renderer
            m_Renderer = GetComponent<Renderer>();
            if (m_Renderer != null)
            {
                m_OriginalMaterial = m_Renderer.material;
            }

            // Lấy hoặc tạo AudioSource
            m_AudioSource = GetComponent<AudioSource>();
            if (m_AudioSource == null)
            {
                m_AudioSource = gameObject.AddComponent<AudioSource>();
                m_AudioSource.playOnAwake = false;
                m_AudioSource.spatialBlend = 1f; // 3D sound
            }

            // Lưu vị trí gốc
            m_OriginalPosition = transform.position;
        }

        private void KhoiTaoVatPham()
        {
            // Đảm bảo có Collider
            Collider col = GetComponent<Collider>();
            if (col == null)
            {
                gameObject.AddComponent<BoxCollider>();
                Log("Đã thêm BoxCollider tự động");
            }

            // Thiết lập tag
            if (!string.IsNullOrEmpty(m_PickupTag))
            {
                gameObject.tag = m_PickupTag;
            }

            Log($"Vật phẩm '{m_TenHienThi}' đã sẵn sàng");
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Thiết lập thông tin vật phẩm
        /// </summary>
        public void ThietLapVatPham(string itemId, int soLuong, string tenHienThi, string moTa = "")
        {
            m_ItemId = itemId;
            m_SoLuong = soLuong;
            m_TenHienThi = tenHienThi;
            m_MoTa = string.IsNullOrEmpty(moTa) ? tenHienThi : moTa;
            
            Log($"Đã thiết lập vật phẩm: {m_TenHienThi} x{m_SoLuong}");
        }

        /// <summary>
        /// Highlight/unhighlight vật phẩm
        /// </summary>
        public void Highlight(bool highlight)
        {
            if (m_IsHighlighted == highlight) return;

            m_IsHighlighted = highlight;

            if (highlight)
            {
                ApplyHighlightEffect();
                PlaySound(m_SoundHighlight);
                PlayParticleEffect(m_EffectHighlight);
                Log($"Highlight: {m_TenHienThi}");
            }
            else
            {
                RemoveHighlightEffect();
                StopParticleEffect(m_EffectHighlight);
            }
        }

        /// <summary>
        /// Thử pickup vật phẩm
        /// </summary>
        public bool TryPickup(PlayerInventory inventory)
        {
            if (!m_CoTheNhat || m_IsPickedUp)
            {
                PlaySound(m_SoundCannotPickup);
                Log($"Không thể nhặt {m_TenHienThi}");
                return false;
            }

            // Thêm vào inventory nếu cần
            if (m_TuDongThemVaoInventory && inventory != null)
            {
                bool added = inventory.ThemVatPham(m_ItemId, m_SoLuong);
                if (!added)
                {
                    PlaySound(m_SoundCannotPickup);
                    Log($"Inventory đầy, không thể nhặt {m_TenHienThi}");
                    return false;
                }
            }

            // Thực hiện pickup
            m_IsPickedUp = true;
            PlaySound(m_SoundPickup);
            PlayParticleEffect(m_EffectPickup);
            
            // Xóa vật phẩm nếu cần
            if (m_XoaSauKhiNhat)
            {
                Destroy(gameObject, 0.1f); // Delay nhỏ để effects chạy
            }

            Log($"Đã nhặt: {m_TenHienThi} x{m_SoLuong}");
            return true;
        }

        /// <summary>
        /// Force pickup không cần kiểm tra điều kiện
        /// </summary>
        public void ForcePickup()
        {
            m_IsPickedUp = true;
            PlaySound(m_SoundPickup);
            PlayParticleEffect(m_EffectPickup);
            
            if (m_XoaSauKhiNhat)
            {
                Destroy(gameObject, 0.1f);
            }

            Log($"Force pickup: {m_TenHienThi}");
        }
        #endregion

        #region Visual Effects
        private void ApplyHighlightEffect()
        {
            if (m_Renderer == null) return;

            // Sử dụng highlight material nếu có
            if (m_HighlightMaterial != null)
            {
                m_Renderer.material = m_HighlightMaterial;
            }
            else
            {
                // Tạo outline effect đơn giản
                Material highlightMat = new Material(m_OriginalMaterial);
                highlightMat.color = m_OutlineColor;
                highlightMat.SetFloat("_Metallic", 0.5f);
                highlightMat.SetFloat("_Smoothness", 0.8f);
                m_Renderer.material = highlightMat;
            }
        }

        private void RemoveHighlightEffect()
        {
            if (m_Renderer != null && m_OriginalMaterial != null)
            {
                m_Renderer.material = m_OriginalMaterial;
            }
        }

        private void ThucHienBobbingAnimation()
        {
            m_BobbingTime += Time.deltaTime * m_TocDoBobbing;
            float yOffset = Mathf.Sin(m_BobbingTime) * m_DoCaoBobbing;
            transform.position = m_OriginalPosition + Vector3.up * yOffset;
        }
        #endregion

        #region Audio Effects
        private void PlaySound(AudioClip clip)
        {
            if (clip != null && m_AudioSource != null)
            {
                m_AudioSource.PlayOneShot(clip);
            }
        }
        #endregion

        #region Particle Effects
        private void PlayParticleEffect(ParticleSystem effect)
        {
            if (effect != null)
            {
                effect.Play();
            }
        }

        private void StopParticleEffect(ParticleSystem effect)
        {
            if (effect != null)
            {
                effect.Stop();
            }
        }
        #endregion

        #region Utility
        private void Log(string message)
        {
            if (m_EnableDebugLogs)
                Debug.Log($"[PickupItem] {message}");
        }

        /// <summary>
        /// Lấy thông tin vật phẩm dưới dạng string
        /// </summary>
        public override string ToString()
        {
            return $"{m_TenHienThi} x{m_SoLuong} ({m_ItemId})";
        }
        #endregion

        #region Editor Helpers
#if UNITY_EDITOR
        [ContextMenu("Test Highlight")]
        private void TestHighlight()
        {
            Highlight(!m_IsHighlighted);
        }

        [ContextMenu("Test Pickup")]
        private void TestPickup()
        {
            PlayerInventory inventory = FindObjectOfType<PlayerInventory>();
            TryPickup(inventory);
        }
#endif
        #endregion
    }
}
