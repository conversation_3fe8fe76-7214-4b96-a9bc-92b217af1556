using UnityEngine;
using EconomySystem;

namespace Player
{
    /// <summary>
    /// Adapter để tích hợp Pickup System với PlayerInventory hiện có
    /// Chuyển đổi giữa namespace Player và EconomySystem
    /// </summary>
    public class PickupInventoryAdapter : MonoBehaviour
    {
        #region Serialized Fields
        [Header("🔧 References")]
        [SerializeField] private EconomySystem.PlayerInventory m_EconomyInventory;
        [SerializeField] private PickupSystemManager m_PickupManager;

        [Header("🐛 Debug")]
        [SerializeField] private bool m_EnableDebugLogs = true;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            TimReferences();
        }

        private void Start()
        {
            ThietLapAdapter();
        }
        #endregion

        #region Initialization
        private void TimReferences()
        {
            // Tìm EconomySystem PlayerInventory
            if (m_EconomyInventory == null)
            {
                m_EconomyInventory = FindObjectOfType<EconomySystem.PlayerInventory>();
            }

            // Tìm PickupSystemManager
            if (m_PickupManager == null)
            {
                m_PickupManager = FindObjectOfType<PickupSystemManager>();
            }
        }

        private void ThietLapAdapter()
        {
            if (m_EconomyInventory == null)
            {
                LogLoi("Không tìm thấy EconomySystem.PlayerInventory!");
                return;
            }

            if (m_PickupManager == null)
            {
                LogLoi("Không tìm thấy PickupSystemManager!");
                return;
            }

            Log("Pickup Inventory Adapter đã sẵn sàng!");
        }
        #endregion

        #region Public API
        /// <summary>
        /// Thêm vật phẩm vào inventory (wrapper cho EconomySystem)
        /// </summary>
        public bool ThemVatPham(string itemId, int soLuong = 1)
        {
            if (m_EconomyInventory == null)
            {
                LogLoi("EconomySystem PlayerInventory không có sẵn!");
                return false;
            }

            bool success = m_EconomyInventory.ThemVatPham(itemId, soLuong);
            
            if (success)
            {
                Log($"Đã thêm vào inventory: {itemId} x{soLuong}");
            }
            else
            {
                LogLoi($"Không thể thêm vào inventory: {itemId} x{soLuong}");
            }

            return success;
        }

        /// <summary>
        /// Xóa vật phẩm khỏi inventory
        /// </summary>
        public bool XoaVatPham(string itemId, int soLuong = 1)
        {
            if (m_EconomyInventory == null) return false;

            bool success = m_EconomyInventory.XoaVatPham(itemId, soLuong);
            
            if (success)
            {
                Log($"Đã xóa khỏi inventory: {itemId} x{soLuong}");
            }

            return success;
        }

        /// <summary>
        /// Kiểm tra có vật phẩm trong inventory
        /// </summary>
        public bool CoVatPham(string itemId, int soLuongCanThiet = 1)
        {
            if (m_EconomyInventory == null) return false;
            return m_EconomyInventory.CoVatPham(itemId, soLuongCanThiet);
        }

        /// <summary>
        /// Lấy số lượng vật phẩm trong inventory
        /// </summary>
        public int LaySoLuongVatPham(string itemId)
        {
            if (m_EconomyInventory == null) return 0;
            return m_EconomyInventory.LaySoLuongVatPham(itemId);
        }

        /// <summary>
        /// Kiểm tra inventory có đầy không
        /// </summary>
        public bool KhoDay => m_EconomyInventory != null && m_EconomyInventory.KhoDay;

        /// <summary>
        /// Lấy số slot còn lại
        /// </summary>
        public int SoSlotConLai => m_EconomyInventory?.SoSlotConLai ?? 0;
        #endregion

        #region Utility
        private void Log(string message)
        {
            if (m_EnableDebugLogs)
                Debug.Log($"[PickupInventoryAdapter] {message}");
        }

        private void LogLoi(string message)
        {
            if (m_EnableDebugLogs)
                Debug.LogError($"[PickupInventoryAdapter] {message}");
        }
        #endregion
    }

    /// <summary>
    /// PlayerInventory wrapper cho Pickup System
    /// Tương thích với namespace Player
    /// </summary>
    public class PlayerInventory : MonoBehaviour
    {
        #region Private Fields
        private PickupInventoryAdapter m_Adapter;
        private EconomySystem.PlayerInventory m_EconomyInventory;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            // Tìm adapter hoặc tạo mới
            m_Adapter = GetComponent<PickupInventoryAdapter>();
            if (m_Adapter == null)
            {
                m_Adapter = gameObject.AddComponent<PickupInventoryAdapter>();
            }

            // Tìm EconomySystem inventory
            m_EconomyInventory = FindObjectOfType<EconomySystem.PlayerInventory>();
        }
        #endregion

        #region Public API
        /// <summary>
        /// Thêm vật phẩm vào inventory
        /// </summary>
        public bool ThemVatPham(string itemId, int soLuong = 1)
        {
            if (m_Adapter != null)
            {
                return m_Adapter.ThemVatPham(itemId, soLuong);
            }
            else if (m_EconomyInventory != null)
            {
                return m_EconomyInventory.ThemVatPham(itemId, soLuong);
            }
            
            Debug.LogError("[PlayerInventory] Không tìm thấy inventory system!");
            return false;
        }

        /// <summary>
        /// Xóa vật phẩm khỏi inventory
        /// </summary>
        public bool XoaVatPham(string itemId, int soLuong = 1)
        {
            if (m_Adapter != null)
            {
                return m_Adapter.XoaVatPham(itemId, soLuong);
            }
            else if (m_EconomyInventory != null)
            {
                return m_EconomyInventory.XoaVatPham(itemId, soLuong);
            }
            
            return false;
        }

        /// <summary>
        /// Kiểm tra có vật phẩm
        /// </summary>
        public bool CoVatPham(string itemId, int soLuongCanThiet = 1)
        {
            if (m_Adapter != null)
            {
                return m_Adapter.CoVatPham(itemId, soLuongCanThiet);
            }
            else if (m_EconomyInventory != null)
            {
                return m_EconomyInventory.CoVatPham(itemId, soLuongCanThiet);
            }
            
            return false;
        }

        /// <summary>
        /// Lấy số lượng vật phẩm
        /// </summary>
        public int LaySoLuongVatPham(string itemId)
        {
            if (m_Adapter != null)
            {
                return m_Adapter.LaySoLuongVatPham(itemId);
            }
            else if (m_EconomyInventory != null)
            {
                return m_EconomyInventory.LaySoLuongVatPham(itemId);
            }
            
            return 0;
        }

        /// <summary>
        /// Kiểm tra inventory đầy
        /// </summary>
        public bool KhoDay
        {
            get
            {
                if (m_Adapter != null)
                {
                    return m_Adapter.KhoDay;
                }
                else if (m_EconomyInventory != null)
                {
                    return m_EconomyInventory.KhoDay;
                }
                
                return false;
            }
        }

        /// <summary>
        /// Lấy số slot còn lại
        /// </summary>
        public int SoSlotConLai
        {
            get
            {
                if (m_Adapter != null)
                {
                    return m_Adapter.SoSlotConLai;
                }
                else if (m_EconomyInventory != null)
                {
                    return m_EconomyInventory.SoSlotConLai;
                }
                
                return 0;
            }
        }
        #endregion

        #region Static Helper
        /// <summary>
        /// Tìm PlayerInventory trong scene (ưu tiên EconomySystem)
        /// </summary>
        public static PlayerInventory FindPlayerInventory()
        {
            // Tìm trong Player namespace trước
            PlayerInventory playerInventory = FindObjectOfType<PlayerInventory>();
            if (playerInventory != null)
            {
                return playerInventory;
            }

            // Nếu không có, tạo wrapper cho EconomySystem
            EconomySystem.PlayerInventory economyInventory = FindObjectOfType<EconomySystem.PlayerInventory>();
            if (economyInventory != null)
            {
                GameObject wrapperObj = new GameObject("PlayerInventory_Wrapper");
                PlayerInventory wrapper = wrapperObj.AddComponent<PlayerInventory>();
                return wrapper;
            }

            return null;
        }
        #endregion
    }
}
