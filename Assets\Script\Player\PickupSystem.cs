using UnityEngine;
using TMPro;
using PlayerSystem;

namespace Player
{
    /// <summary>
    /// <PERSON><PERSON> thống pickup chính - phát hiện và tương tác với vật phẩm
    /// Sử dụng raycast từ camera để phát hiện vật phẩm có tag
    /// Hỗ trợ phím E để pickup và hover text
    /// </summary>
    public class PickupSystem : MonoBehaviour
    {
        #region Serialized Fields
        [Header("🎯 Detection Settings")]
        [SerializeField] private float m_KhoangCachToiDa = 3.0f;
        [SerializeField] private LayerMask m_LayerMask = -1;
        [SerializeField] private string m_PickupTag = "Pickupable";
        [SerializeField] private float m_TanSuatCheck = 10f; // Hz

        [Header("🎮 Input Settings")]
        [SerializeField] private KeyCode m_PhimPickup = KeyCode.E;
        [SerializeField] private bool m_SuDungInputSystem = true;

        [Header("📦 Carrying Settings")]
        [SerializeField] private Transform m_CarryPoint;
        [SerializeField] private float m_KhoangCachMang = 2.0f;
        [SerializeField] private float m_TocDoSmooth = 10.0f;
        [SerializeField] private bool m_CoTheMangNhieu = false;

        [Header("🎨 UI References")]
        [SerializeField] private Canvas m_PickupCanvas;
        [SerializeField] private GameObject m_PromptPanel;
        [SerializeField] private TextMeshProUGUI m_TextTenVatPham;
        [SerializeField] private TextMeshProUGUI m_TextHuongDan;
        [SerializeField] private TextMeshProUGUI m_TextCarryingStatus;

        [Header("🔧 System References")]
        [SerializeField] private PlayerInventory m_PlayerInventory;
        [SerializeField] private PlayerSystem.PlayerInputHandler m_InputHandler;
        [SerializeField] private Camera m_PlayerCamera;

        [Header("🐛 Debug")]
        [SerializeField] private bool m_EnableDebugLogs = true;
        [SerializeField] private bool m_ShowDebugRaycast = true;
        #endregion

        #region Private Fields
        private PickupItem m_CurrentDetectedItem;
        private PickupItem m_CarriedItem;
        private float m_NextCheckTime;
        private bool m_IsInitialized;
        #endregion

        #region Properties
        public bool IsCarryingItems => m_CarriedItem != null;
        public int CarriedItemsCount => m_CarriedItem != null ? 1 : 0;
        public PickupItem CurrentDetectedItem => m_CurrentDetectedItem;
        public PickupItem CarriedItem => m_CarriedItem;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            KhoiTaoReferences();
        }

        private void Start()
        {
            KhoiTaoHeThong();
        }

        private void Update()
        {
            if (!m_IsInitialized) return;

            // Throttled detection
            if (Time.time >= m_NextCheckTime)
            {
                m_NextCheckTime = Time.time + (1f / m_TanSuatCheck);
                KiemTraPickupItem();
            }

            // Input handling
            XuLyInput();
            
            // Update carried item position
            CapNhatViTriVatMang();
        }

        private void OnDisable()
        {
            if (m_InputHandler != null)
            {
                m_InputHandler.OnPickupPressed -= OnPickupInput;
            }
        }
        #endregion

        #region Initialization
        private void KhoiTaoReferences()
        {
            // Tự động tìm references nếu chưa có
            if (m_PlayerCamera == null)
                m_PlayerCamera = Camera.main ?? FindObjectOfType<Camera>();

            if (m_PlayerInventory == null)
                m_PlayerInventory = FindObjectOfType<PlayerInventory>();

            if (m_InputHandler == null)
                m_InputHandler = GetComponent<PlayerSystem.PlayerInputHandler>();

            // Tạo carry point nếu chưa có
            if (m_CarryPoint == null)
            {
                GameObject carryPointObj = new GameObject("CarryPoint");
                carryPointObj.transform.SetParent(m_PlayerCamera.transform);
                carryPointObj.transform.localPosition = Vector3.forward * m_KhoangCachMang;
                m_CarryPoint = carryPointObj.transform;
            }
        }

        private void KhoiTaoHeThong()
        {
            // Setup input system
            if (m_SuDungInputSystem && m_InputHandler != null)
            {
                // Đăng ký event pickup
                m_InputHandler.OnPickupPressed += OnPickupInput;
            }

            // Ẩn UI ban đầu
            AnUI();

            m_IsInitialized = true;
            Log("Pickup System đã khởi tạo thành công!");
        }
        #endregion

        #region Detection
        private void KiemTraPickupItem()
        {
            if (m_PlayerCamera == null) return;

            // Raycast từ center của camera
            Ray ray = m_PlayerCamera.ScreenPointToRay(new Vector3(Screen.width / 2f, Screen.height / 2f, 0f));
            
            if (m_ShowDebugRaycast)
            {
                Debug.DrawRay(ray.origin, ray.direction * m_KhoangCachToiDa, Color.yellow, 0.1f);
            }

            if (Physics.Raycast(ray, out RaycastHit hit, m_KhoangCachToiDa, m_LayerMask))
            {
                // Kiểm tra tag
                if (hit.collider.CompareTag(m_PickupTag))
                {
                    PickupItem pickupItem = hit.collider.GetComponent<PickupItem>();
                    if (pickupItem != null && pickupItem.CoTheNhat)
                    {
                        ThietLapItemDetected(pickupItem);
                        return;
                    }
                }
            }

            // Không tìm thấy item nào
            ThietLapItemDetected(null);
        }

        private void ThietLapItemDetected(PickupItem item)
        {
            if (m_CurrentDetectedItem == item) return;

            // Unhighlight item cũ
            if (m_CurrentDetectedItem != null)
            {
                m_CurrentDetectedItem.Highlight(false);
            }

            m_CurrentDetectedItem = item;

            // Highlight item mới
            if (m_CurrentDetectedItem != null)
            {
                m_CurrentDetectedItem.Highlight(true);
                HienThiUI();
                CapNhatUIText();
                Log($"Phát hiện vật phẩm: {m_CurrentDetectedItem.TenHienThi}");
            }
            else
            {
                AnUI();
            }
        }
        #endregion

        #region Input Handling
        private void XuLyInput()
        {
            // Fallback cho legacy input
            if (!m_SuDungInputSystem || m_InputActions == null)
            {
                if (Input.GetKeyDown(m_PhimPickup))
                {
                    ThucHienPickup();
                }
            }
        }

        private void OnPickupInput()
        {
            ThucHienPickup();
        }

        private void ThucHienPickup()
        {
            if (m_CarriedItem != null)
            {
                // Đang mang vật → thả xuống
                ThaVatPham();
            }
            else if (m_CurrentDetectedItem != null)
            {
                // Có vật phẩm để nhặt → nhặt lên
                NhatVatPham(m_CurrentDetectedItem);
            }
        }
        #endregion

        #region Pickup/Drop Logic
        private void NhatVatPham(PickupItem item)
        {
            if (item == null || !item.CoTheNhat) return;

            // Kiểm tra có thể mang thêm không
            if (m_CarriedItem != null && !m_CoTheMangNhieu)
            {
                Log("Không thể mang thêm vật phẩm!");
                return;
            }

            // Thực hiện pickup
            bool success = item.TryPickup(m_PlayerInventory);
            if (success)
            {
                m_CarriedItem = item;
                item.transform.SetParent(m_CarryPoint);
                
                // Disable physics
                Rigidbody rb = item.GetComponent<Rigidbody>();
                if (rb != null)
                {
                    rb.isKinematic = true;
                }

                // Disable collider
                Collider col = item.GetComponent<Collider>();
                if (col != null)
                {
                    col.enabled = false;
                }

                Log($"Đã nhặt: {item.TenHienThi}");
                CapNhatCarryingStatus();
            }
        }

        private void ThaVatPham()
        {
            if (m_CarriedItem == null) return;

            // Tính vị trí thả
            Vector3 dropPosition = m_PlayerCamera.transform.position + m_PlayerCamera.transform.forward * 2f;
            
            // Thả vật phẩm
            m_CarriedItem.transform.SetParent(null);
            m_CarriedItem.transform.position = dropPosition;

            // Enable physics
            Rigidbody rb = m_CarriedItem.GetComponent<Rigidbody>();
            if (rb != null)
            {
                rb.isKinematic = false;
            }

            // Enable collider
            Collider col = m_CarriedItem.GetComponent<Collider>();
            if (col != null)
            {
                col.enabled = true;
            }

            Log($"Đã thả: {m_CarriedItem.TenHienThi}");
            m_CarriedItem = null;
            CapNhatCarryingStatus();
        }

        private void CapNhatViTriVatMang()
        {
            if (m_CarriedItem != null && m_CarryPoint != null)
            {
                m_CarriedItem.transform.position = Vector3.Lerp(
                    m_CarriedItem.transform.position,
                    m_CarryPoint.position,
                    Time.deltaTime * m_TocDoSmooth
                );
            }
        }
        #endregion

        #region UI Management
        private void HienThiUI()
        {
            if (m_PromptPanel != null)
                m_PromptPanel.SetActive(true);
        }

        private void AnUI()
        {
            if (m_PromptPanel != null)
                m_PromptPanel.SetActive(false);
        }

        private void CapNhatUIText()
        {
            if (m_CurrentDetectedItem == null) return;

            if (m_TextTenVatPham != null)
                m_TextTenVatPham.text = m_CurrentDetectedItem.TenHienThi;

            if (m_TextHuongDan != null)
                m_TextHuongDan.text = $"Nhấn [{m_PhimPickup}] để nhặt";
        }

        private void CapNhatCarryingStatus()
        {
            if (m_TextCarryingStatus != null)
            {
                if (m_CarriedItem != null)
                {
                    m_TextCarryingStatus.text = $"Đang mang: {m_CarriedItem.TenHienThi}";
                }
                else
                {
                    m_TextCarryingStatus.text = "";
                }
            }
        }
        #endregion

        #region Utility
        private void Log(string message)
        {
            if (m_EnableDebugLogs)
                Debug.Log($"[PickupSystem] {message}");
        }
        #endregion
    }
}
