using UnityEngine;
using System.Collections;

namespace Player
{
    /// <summary>
    /// Qu<PERSON>n lý hiệu ứng visual và audio cho pickup system
    /// Bao gồm highlight, particle effects, sound effects
    /// Hỗ trợ bobbing animation và outline effects
    /// </summary>
    public class PickupEffects : MonoBehaviour
    {
        #region Serialized Fields
        [Header("🎨 Highlight Effects")]
        [SerializeField] private Material m_OutlineMaterial;
        [SerializeField] private Color m_MauOutline = Color.yellow;
        [SerializeField] private float m_DoDayOutline = 0.02f;
        [SerializeField] private bool m_CoGlowEffect = true;
        [SerializeField] private float m_CuongDoGlow = 2.0f;

        [Header("🌊 Animation Effects")]
        [SerializeField] private bool m_CoBobbing = true;
        [SerializeField] private float m_TocDoBobbing = 2.0f;
        [SerializeField] private float m_DoCaoBobbing = 0.1f;
        [SerializeField] private bool m_CoRotation = true;
        [SerializeField] private float m_TocDoRotation = 30.0f;

        [Header("✨ Particle Effects")]
        [SerializeField] private ParticleSystem m_EffectHighlight;
        [SerializeField] private ParticleSystem m_EffectPickup;
        [SerializeField] private ParticleSystem m_EffectDrop;
        [SerializeField] private ParticleSystem m_EffectCannotPickup;

        [Header("🔊 Audio Effects")]
        [SerializeField] private AudioClip m_SoundHighlight;
        [SerializeField] private AudioClip m_SoundPickup;
        [SerializeField] private AudioClip m_SoundDrop;
        [SerializeField] private AudioClip m_SoundCannotPickup;
        [SerializeField] private float m_AudioVolume = 1.0f;

        [Header("🎬 Screen Effects")]
        [SerializeField] private bool m_CoScreenShake = true;
        [SerializeField] private float m_CuongDoShake = 0.1f;
        [SerializeField] private float m_ThoiGianShake = 0.2f;

        [Header("🐛 Debug")]
        [SerializeField] private bool m_EnableDebugLogs = true;
        #endregion

        #region Private Fields
        private AudioSource m_AudioSource;
        private Camera m_PlayerCamera;
        
        // Animation tracking
        private Coroutine m_BobbingCoroutine;
        private Coroutine m_RotationCoroutine;
        private Coroutine m_ShakeCoroutine;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            KhoiTaoComponents();
        }

        private void Start()
        {
            ThietLapEffects();
        }
        #endregion

        #region Initialization
        private void KhoiTaoComponents()
        {
            // Lấy hoặc tạo AudioSource
            m_AudioSource = GetComponent<AudioSource>();
            if (m_AudioSource == null)
            {
                m_AudioSource = gameObject.AddComponent<AudioSource>();
                m_AudioSource.playOnAwake = false;
                m_AudioSource.spatialBlend = 1f; // 3D sound
                m_AudioSource.volume = m_AudioVolume;
            }

            // Tìm camera
            m_PlayerCamera = Camera.main ?? FindObjectOfType<Camera>();
        }

        private void ThietLapEffects()
        {
            // Tạo outline material nếu chưa có
            if (m_OutlineMaterial == null)
            {
                TaoOutlineMaterial();
            }

            Log("Pickup Effects đã được thiết lập");
        }

        private void TaoOutlineMaterial()
        {
            // Tạo material outline đơn giản
            m_OutlineMaterial = new Material(Shader.Find("Standard"));
            m_OutlineMaterial.color = m_MauOutline;
            m_OutlineMaterial.SetFloat("_Metallic", 0.5f);
            m_OutlineMaterial.SetFloat("_Smoothness", 0.8f);
            
            if (m_CoGlowEffect)
            {
                m_OutlineMaterial.EnableKeyword("_EMISSION");
                m_OutlineMaterial.SetColor("_EmissionColor", m_MauOutline * m_CuongDoGlow);
            }

            Log("Đã tạo outline material tự động");
        }
        #endregion

        #region Highlight Effects
        /// <summary>
        /// Áp dụng highlight effect cho object
        /// </summary>
        public void ApplyHighlight(GameObject target)
        {
            if (target == null) return;

            Renderer renderer = target.GetComponent<Renderer>();
            if (renderer != null && m_OutlineMaterial != null)
            {
                // Lưu material gốc
                Material originalMaterial = renderer.material;
                target.GetComponent<PickupItem>()?.GetType().GetField("m_OriginalMaterial", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                    ?.SetValue(target.GetComponent<PickupItem>(), originalMaterial);

                // Áp dụng highlight material
                renderer.material = m_OutlineMaterial;
            }

            // Bắt đầu animation effects
            if (m_CoBobbing)
            {
                StartBobbingAnimation(target);
            }

            if (m_CoRotation)
            {
                StartRotationAnimation(target);
            }

            // Play highlight effects
            PlayParticleEffect(m_EffectHighlight, target.transform.position);
            PlaySound(m_SoundHighlight);

            Log($"Áp dụng highlight cho: {target.name}");
        }

        /// <summary>
        /// Gỡ bỏ highlight effect
        /// </summary>
        public void RemoveHighlight(GameObject target)
        {
            if (target == null) return;

            // Khôi phục material gốc
            Renderer renderer = target.GetComponent<Renderer>();
            PickupItem pickupItem = target.GetComponent<PickupItem>();
            
            if (renderer != null && pickupItem != null)
            {
                // Lấy material gốc thông qua reflection (tạm thời)
                var field = pickupItem.GetType().GetField("m_OriginalMaterial", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                Material originalMaterial = field?.GetValue(pickupItem) as Material;
                
                if (originalMaterial != null)
                {
                    renderer.material = originalMaterial;
                }
            }

            // Dừng animation effects
            StopAnimationEffects(target);

            Log($"Gỡ bỏ highlight cho: {target.name}");
        }
        #endregion

        #region Animation Effects
        private void StartBobbingAnimation(GameObject target)
        {
            if (m_BobbingCoroutine != null)
            {
                StopCoroutine(m_BobbingCoroutine);
            }
            m_BobbingCoroutine = StartCoroutine(BobbingAnimation(target));
        }

        private void StartRotationAnimation(GameObject target)
        {
            if (m_RotationCoroutine != null)
            {
                StopCoroutine(m_RotationCoroutine);
            }
            m_RotationCoroutine = StartCoroutine(RotationAnimation(target));
        }

        private void StopAnimationEffects(GameObject target)
        {
            if (m_BobbingCoroutine != null)
            {
                StopCoroutine(m_BobbingCoroutine);
                m_BobbingCoroutine = null;
            }

            if (m_RotationCoroutine != null)
            {
                StopCoroutine(m_RotationCoroutine);
                m_RotationCoroutine = null;
            }
        }

        private IEnumerator BobbingAnimation(GameObject target)
        {
            if (target == null) yield break;

            Vector3 originalPosition = target.transform.position;
            float time = 0f;

            while (target != null)
            {
                time += Time.deltaTime * m_TocDoBobbing;
                float yOffset = Mathf.Sin(time) * m_DoCaoBobbing;
                target.transform.position = originalPosition + Vector3.up * yOffset;
                yield return null;
            }
        }

        private IEnumerator RotationAnimation(GameObject target)
        {
            if (target == null) yield break;

            while (target != null)
            {
                target.transform.Rotate(Vector3.up, m_TocDoRotation * Time.deltaTime);
                yield return null;
            }
        }
        #endregion

        #region Particle Effects
        /// <summary>
        /// Phát particle effect tại vị trí
        /// </summary>
        public void PlayParticleEffect(ParticleSystem effect, Vector3 position)
        {
            if (effect != null)
            {
                effect.transform.position = position;
                effect.Play();
                Log($"Phát particle effect tại: {position}");
            }
        }

        /// <summary>
        /// Phát effect khi pickup
        /// </summary>
        public void PlayPickupEffect(Vector3 position)
        {
            PlayParticleEffect(m_EffectPickup, position);
            PlaySound(m_SoundPickup);
            
            if (m_CoScreenShake)
            {
                PlayScreenShake();
            }

            Log("Phát pickup effect");
        }

        /// <summary>
        /// Phát effect khi drop
        /// </summary>
        public void PlayDropEffect(Vector3 position)
        {
            PlayParticleEffect(m_EffectDrop, position);
            PlaySound(m_SoundDrop);
            Log("Phát drop effect");
        }

        /// <summary>
        /// Phát effect khi không thể pickup
        /// </summary>
        public void PlayCannotPickupEffect(Vector3 position)
        {
            PlayParticleEffect(m_EffectCannotPickup, position);
            PlaySound(m_SoundCannotPickup);
            Log("Phát cannot pickup effect");
        }
        #endregion

        #region Audio Effects
        /// <summary>
        /// Phát âm thanh
        /// </summary>
        public void PlaySound(AudioClip clip)
        {
            if (clip != null && m_AudioSource != null)
            {
                m_AudioSource.PlayOneShot(clip, m_AudioVolume);
            }
        }
        #endregion

        #region Screen Effects
        private void PlayScreenShake()
        {
            if (m_PlayerCamera != null)
            {
                if (m_ShakeCoroutine != null)
                {
                    StopCoroutine(m_ShakeCoroutine);
                }
                m_ShakeCoroutine = StartCoroutine(ScreenShakeCoroutine());
            }
        }

        private IEnumerator ScreenShakeCoroutine()
        {
            Vector3 originalPosition = m_PlayerCamera.transform.localPosition;
            float elapsedTime = 0f;

            while (elapsedTime < m_ThoiGianShake)
            {
                elapsedTime += Time.deltaTime;
                
                Vector3 randomOffset = Random.insideUnitSphere * m_CuongDoShake;
                m_PlayerCamera.transform.localPosition = originalPosition + randomOffset;
                
                yield return null;
            }

            m_PlayerCamera.transform.localPosition = originalPosition;
        }
        #endregion

        #region Public API
        /// <summary>
        /// Thiết lập volume cho audio
        /// </summary>
        public void SetAudioVolume(float volume)
        {
            m_AudioVolume = Mathf.Clamp01(volume);
            if (m_AudioSource != null)
            {
                m_AudioSource.volume = m_AudioVolume;
            }
        }

        /// <summary>
        /// Bật/tắt screen shake
        /// </summary>
        public void SetScreenShakeEnabled(bool enabled)
        {
            m_CoScreenShake = enabled;
        }

        /// <summary>
        /// Thiết lập màu outline
        /// </summary>
        public void SetOutlineColor(Color color)
        {
            m_MauOutline = color;
            if (m_OutlineMaterial != null)
            {
                m_OutlineMaterial.color = color;
                if (m_CoGlowEffect)
                {
                    m_OutlineMaterial.SetColor("_EmissionColor", color * m_CuongDoGlow);
                }
            }
        }
        #endregion

        #region Utility
        private void Log(string message)
        {
            if (m_EnableDebugLogs)
                Debug.Log($"[PickupEffects] {message}");
        }
        #endregion

        #region Editor Helpers
#if UNITY_EDITOR
        [ContextMenu("Test Highlight Effect")]
        private void TestHighlightEffect()
        {
            ApplyHighlight(gameObject);
        }

        [ContextMenu("Test Pickup Effect")]
        private void TestPickupEffect()
        {
            PlayPickupEffect(transform.position);
        }
#endif
        #endregion
    }
}
