{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 2208, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 2208, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 2208, "tid": 119, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 2208, "tid": 119, "ts": 1750060790751169, "dur": 11, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 2208, "tid": 119, "ts": 1750060790751197, "dur": 7, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 2208, "tid": 85899345920, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790426615, "dur": 15150, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790441767, "dur": 308732, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790441782, "dur": 35, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790441822, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790441828, "dur": 130614, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790572453, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790572458, "dur": 100, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790572563, "dur": 12, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790572577, "dur": 2531, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790575114, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790575117, "dur": 145, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790575267, "dur": 4, "ph": "X", "name": "ProcessMessages 1640", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790575273, "dur": 87, "ph": "X", "name": "ReadAsync 1640", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790575363, "dur": 2, "ph": "X", "name": "ProcessMessages 846", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790575367, "dur": 90, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790575461, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790575464, "dur": 83, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790575553, "dur": 2, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790575557, "dur": 70, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790575631, "dur": 2, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790575634, "dur": 96, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790575741, "dur": 2, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790575745, "dur": 69, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790575818, "dur": 2, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790575821, "dur": 119, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790575943, "dur": 3, "ph": "X", "name": "ProcessMessages 1371", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790575948, "dur": 63, "ph": "X", "name": "ReadAsync 1371", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790576015, "dur": 2, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790576019, "dur": 95, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790576117, "dur": 3, "ph": "X", "name": "ProcessMessages 1523", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790576122, "dur": 69, "ph": "X", "name": "ReadAsync 1523", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790576194, "dur": 2, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790576198, "dur": 49, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790576250, "dur": 1, "ph": "X", "name": "ProcessMessages 170", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790576252, "dur": 52, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790576307, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790576310, "dur": 68, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790576381, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790576384, "dur": 48, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790576436, "dur": 1, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790576438, "dur": 57, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790576499, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790576502, "dur": 57, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790576562, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790576565, "dur": 64, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790576632, "dur": 2, "ph": "X", "name": "ProcessMessages 1173", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790576637, "dur": 66, "ph": "X", "name": "ReadAsync 1173", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790576706, "dur": 3, "ph": "X", "name": "ProcessMessages 1185", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790576710, "dur": 61, "ph": "X", "name": "ReadAsync 1185", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790576774, "dur": 2, "ph": "X", "name": "ProcessMessages 1021", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790576778, "dur": 69, "ph": "X", "name": "ReadAsync 1021", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790576851, "dur": 3, "ph": "X", "name": "ProcessMessages 1313", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790576856, "dur": 70, "ph": "X", "name": "ReadAsync 1313", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790576931, "dur": 3, "ph": "X", "name": "ProcessMessages 1282", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790576936, "dur": 82, "ph": "X", "name": "ReadAsync 1282", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790577022, "dur": 3, "ph": "X", "name": "ProcessMessages 1259", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790577027, "dur": 89, "ph": "X", "name": "ReadAsync 1259", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790577119, "dur": 2, "ph": "X", "name": "ProcessMessages 1060", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790577123, "dur": 67, "ph": "X", "name": "ReadAsync 1060", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790577193, "dur": 3, "ph": "X", "name": "ProcessMessages 933", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790577197, "dur": 76, "ph": "X", "name": "ReadAsync 933", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790577276, "dur": 3, "ph": "X", "name": "ProcessMessages 1319", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790577281, "dur": 72, "ph": "X", "name": "ReadAsync 1319", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790577355, "dur": 2, "ph": "X", "name": "ProcessMessages 1240", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790577359, "dur": 71, "ph": "X", "name": "ReadAsync 1240", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790577434, "dur": 3, "ph": "X", "name": "ProcessMessages 1258", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790577438, "dur": 64, "ph": "X", "name": "ReadAsync 1258", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790577505, "dur": 2, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790577509, "dur": 87, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790577599, "dur": 3, "ph": "X", "name": "ProcessMessages 1322", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790577604, "dur": 78, "ph": "X", "name": "ReadAsync 1322", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790577684, "dur": 4, "ph": "X", "name": "ProcessMessages 1587", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790577690, "dur": 71, "ph": "X", "name": "ReadAsync 1587", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790577763, "dur": 3, "ph": "X", "name": "ProcessMessages 1311", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790577768, "dur": 65, "ph": "X", "name": "ReadAsync 1311", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790577838, "dur": 3, "ph": "X", "name": "ProcessMessages 1028", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790577843, "dur": 90, "ph": "X", "name": "ReadAsync 1028", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790577939, "dur": 3, "ph": "X", "name": "ProcessMessages 1341", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790577944, "dur": 96, "ph": "X", "name": "ReadAsync 1341", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790578044, "dur": 4, "ph": "X", "name": "ProcessMessages 1936", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790578049, "dur": 80, "ph": "X", "name": "ReadAsync 1936", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790578132, "dur": 4, "ph": "X", "name": "ProcessMessages 1610", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790578137, "dur": 70, "ph": "X", "name": "ReadAsync 1610", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790578211, "dur": 2, "ph": "X", "name": "ProcessMessages 1487", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790578215, "dur": 67, "ph": "X", "name": "ReadAsync 1487", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790578285, "dur": 2, "ph": "X", "name": "ProcessMessages 1129", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790578290, "dur": 65, "ph": "X", "name": "ReadAsync 1129", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790578358, "dur": 5, "ph": "X", "name": "ProcessMessages 1062", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790578365, "dur": 56, "ph": "X", "name": "ReadAsync 1062", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790578424, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790578427, "dur": 59, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790578489, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790578492, "dur": 68, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790578563, "dur": 3, "ph": "X", "name": "ProcessMessages 1200", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790578567, "dur": 65, "ph": "X", "name": "ReadAsync 1200", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790578636, "dur": 2, "ph": "X", "name": "ProcessMessages 1117", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790578640, "dur": 54, "ph": "X", "name": "ReadAsync 1117", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790578697, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790578700, "dur": 50, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790578753, "dur": 2, "ph": "X", "name": "ProcessMessages 1042", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790578757, "dur": 56, "ph": "X", "name": "ReadAsync 1042", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790578816, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790578819, "dur": 60, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790578882, "dur": 2, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790578886, "dur": 64, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790578953, "dur": 2, "ph": "X", "name": "ProcessMessages 1077", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790578957, "dur": 70, "ph": "X", "name": "ReadAsync 1077", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790579031, "dur": 2, "ph": "X", "name": "ProcessMessages 776", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790579035, "dur": 67, "ph": "X", "name": "ReadAsync 776", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790579106, "dur": 2, "ph": "X", "name": "ProcessMessages 1179", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790579110, "dur": 68, "ph": "X", "name": "ReadAsync 1179", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790579181, "dur": 2, "ph": "X", "name": "ProcessMessages 1165", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790579185, "dur": 60, "ph": "X", "name": "ReadAsync 1165", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790579248, "dur": 2, "ph": "X", "name": "ProcessMessages 761", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790579251, "dur": 65, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790579320, "dur": 3, "ph": "X", "name": "ProcessMessages 1125", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790579324, "dur": 61, "ph": "X", "name": "ReadAsync 1125", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790579389, "dur": 2, "ph": "X", "name": "ProcessMessages 710", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790579392, "dur": 64, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790579460, "dur": 2, "ph": "X", "name": "ProcessMessages 959", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790579463, "dur": 59, "ph": "X", "name": "ReadAsync 959", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790579526, "dur": 2, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790579530, "dur": 64, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790579597, "dur": 3, "ph": "X", "name": "ProcessMessages 1153", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790579601, "dur": 63, "ph": "X", "name": "ReadAsync 1153", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790579667, "dur": 3, "ph": "X", "name": "ProcessMessages 1109", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790579672, "dur": 62, "ph": "X", "name": "ReadAsync 1109", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790579737, "dur": 2, "ph": "X", "name": "ProcessMessages 1086", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790579743, "dur": 62, "ph": "X", "name": "ReadAsync 1086", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790579808, "dur": 2, "ph": "X", "name": "ProcessMessages 1114", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790579812, "dur": 62, "ph": "X", "name": "ReadAsync 1114", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790579877, "dur": 2, "ph": "X", "name": "ProcessMessages 799", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790579881, "dur": 66, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790579950, "dur": 3, "ph": "X", "name": "ProcessMessages 1174", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790579954, "dur": 101, "ph": "X", "name": "ReadAsync 1174", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790580059, "dur": 2, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790580063, "dur": 73, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790580139, "dur": 3, "ph": "X", "name": "ProcessMessages 1880", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790580144, "dur": 63, "ph": "X", "name": "ReadAsync 1880", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790580210, "dur": 2, "ph": "X", "name": "ProcessMessages 987", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790580214, "dur": 64, "ph": "X", "name": "ReadAsync 987", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790580281, "dur": 2, "ph": "X", "name": "ProcessMessages 1087", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790580285, "dur": 58, "ph": "X", "name": "ReadAsync 1087", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790580346, "dur": 2, "ph": "X", "name": "ProcessMessages 749", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790580349, "dur": 63, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790580416, "dur": 2, "ph": "X", "name": "ProcessMessages 984", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790580420, "dur": 65, "ph": "X", "name": "ReadAsync 984", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790580488, "dur": 2, "ph": "X", "name": "ProcessMessages 922", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790580492, "dur": 74, "ph": "X", "name": "ReadAsync 922", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790580569, "dur": 3, "ph": "X", "name": "ProcessMessages 1226", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790580574, "dur": 61, "ph": "X", "name": "ReadAsync 1226", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790580638, "dur": 2, "ph": "X", "name": "ProcessMessages 826", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790580642, "dur": 65, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790580710, "dur": 2, "ph": "X", "name": "ProcessMessages 1021", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790580714, "dur": 62, "ph": "X", "name": "ReadAsync 1021", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790580779, "dur": 2, "ph": "X", "name": "ProcessMessages 927", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790580783, "dur": 67, "ph": "X", "name": "ReadAsync 927", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790580854, "dur": 2, "ph": "X", "name": "ProcessMessages 1243", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790580858, "dur": 63, "ph": "X", "name": "ReadAsync 1243", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790580924, "dur": 2, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790580928, "dur": 67, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790580998, "dur": 3, "ph": "X", "name": "ProcessMessages 1357", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790581002, "dur": 54, "ph": "X", "name": "ReadAsync 1357", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790581059, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790581062, "dur": 109, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790581174, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790581177, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790581255, "dur": 3, "ph": "X", "name": "ProcessMessages 1301", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790581260, "dur": 65, "ph": "X", "name": "ReadAsync 1301", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790581329, "dur": 3, "ph": "X", "name": "ProcessMessages 910", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790581334, "dur": 63, "ph": "X", "name": "ReadAsync 910", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790581401, "dur": 2, "ph": "X", "name": "ProcessMessages 1016", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790581405, "dur": 63, "ph": "X", "name": "ReadAsync 1016", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790581471, "dur": 2, "ph": "X", "name": "ProcessMessages 907", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790581475, "dur": 95, "ph": "X", "name": "ReadAsync 907", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790581574, "dur": 3, "ph": "X", "name": "ProcessMessages 1242", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790581579, "dur": 69, "ph": "X", "name": "ReadAsync 1242", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790581651, "dur": 2, "ph": "X", "name": "ProcessMessages 1160", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790581655, "dur": 65, "ph": "X", "name": "ReadAsync 1160", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790581723, "dur": 3, "ph": "X", "name": "ProcessMessages 1089", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790581728, "dur": 63, "ph": "X", "name": "ReadAsync 1089", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790581794, "dur": 2, "ph": "X", "name": "ProcessMessages 999", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790581798, "dur": 65, "ph": "X", "name": "ReadAsync 999", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790581867, "dur": 3, "ph": "X", "name": "ProcessMessages 1342", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790581871, "dur": 63, "ph": "X", "name": "ReadAsync 1342", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790581937, "dur": 2, "ph": "X", "name": "ProcessMessages 947", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790581941, "dur": 67, "ph": "X", "name": "ReadAsync 947", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790582011, "dur": 3, "ph": "X", "name": "ProcessMessages 1458", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790582015, "dur": 62, "ph": "X", "name": "ReadAsync 1458", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790582081, "dur": 2, "ph": "X", "name": "ProcessMessages 1141", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790582085, "dur": 64, "ph": "X", "name": "ReadAsync 1141", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790582152, "dur": 3, "ph": "X", "name": "ProcessMessages 1276", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790582157, "dur": 61, "ph": "X", "name": "ReadAsync 1276", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790582221, "dur": 2, "ph": "X", "name": "ProcessMessages 971", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790582225, "dur": 74, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790582302, "dur": 3, "ph": "X", "name": "ProcessMessages 1462", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790582307, "dur": 74, "ph": "X", "name": "ReadAsync 1462", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790582384, "dur": 2, "ph": "X", "name": "ProcessMessages 1324", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790582388, "dur": 73, "ph": "X", "name": "ReadAsync 1324", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790582464, "dur": 3, "ph": "X", "name": "ProcessMessages 1225", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790582469, "dur": 56, "ph": "X", "name": "ReadAsync 1225", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790582528, "dur": 3, "ph": "X", "name": "ProcessMessages 993", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790582533, "dur": 63, "ph": "X", "name": "ReadAsync 993", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790582599, "dur": 2, "ph": "X", "name": "ProcessMessages 1135", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790582603, "dur": 62, "ph": "X", "name": "ReadAsync 1135", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790582669, "dur": 2, "ph": "X", "name": "ProcessMessages 911", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790582673, "dur": 64, "ph": "X", "name": "ReadAsync 911", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790582740, "dur": 2, "ph": "X", "name": "ProcessMessages 896", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790582745, "dur": 66, "ph": "X", "name": "ReadAsync 896", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790582814, "dur": 7, "ph": "X", "name": "ProcessMessages 1086", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790582823, "dur": 65, "ph": "X", "name": "ReadAsync 1086", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790582892, "dur": 3, "ph": "X", "name": "ProcessMessages 1449", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790582896, "dur": 62, "ph": "X", "name": "ReadAsync 1449", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790582961, "dur": 2, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790582965, "dur": 68, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583036, "dur": 2, "ph": "X", "name": "ProcessMessages 1218", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583040, "dur": 64, "ph": "X", "name": "ReadAsync 1218", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583108, "dur": 3, "ph": "X", "name": "ProcessMessages 1135", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583112, "dur": 65, "ph": "X", "name": "ReadAsync 1135", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583180, "dur": 2, "ph": "X", "name": "ProcessMessages 1124", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583184, "dur": 64, "ph": "X", "name": "ReadAsync 1124", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583251, "dur": 2, "ph": "X", "name": "ProcessMessages 869", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583256, "dur": 63, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583321, "dur": 2, "ph": "X", "name": "ProcessMessages 1027", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583325, "dur": 65, "ph": "X", "name": "ReadAsync 1027", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583393, "dur": 2, "ph": "X", "name": "ProcessMessages 1078", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583396, "dur": 64, "ph": "X", "name": "ReadAsync 1078", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583466, "dur": 3, "ph": "X", "name": "ProcessMessages 1033", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583471, "dur": 87, "ph": "X", "name": "ReadAsync 1033", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583561, "dur": 4, "ph": "X", "name": "ProcessMessages 1228", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583567, "dur": 75, "ph": "X", "name": "ReadAsync 1228", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583645, "dur": 2, "ph": "X", "name": "ProcessMessages 1063", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583649, "dur": 67, "ph": "X", "name": "ReadAsync 1063", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583720, "dur": 2, "ph": "X", "name": "ProcessMessages 990", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583724, "dur": 65, "ph": "X", "name": "ReadAsync 990", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583792, "dur": 2, "ph": "X", "name": "ProcessMessages 1182", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583797, "dur": 53, "ph": "X", "name": "ReadAsync 1182", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583852, "dur": 1, "ph": "X", "name": "ProcessMessages 823", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583854, "dur": 30, "ph": "X", "name": "ReadAsync 823", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583885, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583887, "dur": 39, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583928, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583929, "dur": 34, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583965, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790583966, "dur": 35, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584005, "dur": 36, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584042, "dur": 1, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584044, "dur": 35, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584081, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584083, "dur": 33, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584118, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584120, "dur": 33, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584157, "dur": 33, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584191, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584193, "dur": 35, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584232, "dur": 34, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584268, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584269, "dur": 33, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584306, "dur": 35, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584342, "dur": 1, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584344, "dur": 33, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584381, "dur": 35, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584418, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584420, "dur": 32, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584456, "dur": 32, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584489, "dur": 2, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584492, "dur": 49, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584543, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584545, "dur": 35, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584582, "dur": 1, "ph": "X", "name": "ProcessMessages 871", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584584, "dur": 35, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584620, "dur": 1, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584625, "dur": 33, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584659, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584661, "dur": 34, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584697, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584699, "dur": 27, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584730, "dur": 30, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584761, "dur": 1, "ph": "X", "name": "ProcessMessages 652", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584763, "dur": 39, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584804, "dur": 1, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584806, "dur": 30, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584838, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584840, "dur": 38, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584882, "dur": 34, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584918, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584920, "dur": 34, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584956, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790584957, "dur": 29, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790585005, "dur": 58, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790585066, "dur": 4, "ph": "X", "name": "ProcessMessages 878", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790585073, "dur": 67, "ph": "X", "name": "ReadAsync 878", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790585144, "dur": 3, "ph": "X", "name": "ProcessMessages 1288", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790585148, "dur": 63, "ph": "X", "name": "ReadAsync 1288", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790585214, "dur": 2, "ph": "X", "name": "ProcessMessages 885", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790585218, "dur": 65, "ph": "X", "name": "ReadAsync 885", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790585286, "dur": 2, "ph": "X", "name": "ProcessMessages 1150", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790585290, "dur": 65, "ph": "X", "name": "ReadAsync 1150", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790585358, "dur": 2, "ph": "X", "name": "ProcessMessages 937", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790585362, "dur": 67, "ph": "X", "name": "ReadAsync 937", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790585432, "dur": 2, "ph": "X", "name": "ProcessMessages 1371", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790585435, "dur": 62, "ph": "X", "name": "ReadAsync 1371", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790585500, "dur": 2, "ph": "X", "name": "ProcessMessages 982", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790585504, "dur": 56, "ph": "X", "name": "ReadAsync 982", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790585564, "dur": 3, "ph": "X", "name": "ProcessMessages 1379", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790585569, "dur": 55, "ph": "X", "name": "ReadAsync 1379", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790585627, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790585629, "dur": 62, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790585695, "dur": 2, "ph": "X", "name": "ProcessMessages 898", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790585698, "dur": 65, "ph": "X", "name": "ReadAsync 898", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790585767, "dur": 3, "ph": "X", "name": "ProcessMessages 1378", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790585771, "dur": 98, "ph": "X", "name": "ReadAsync 1378", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790585873, "dur": 2, "ph": "X", "name": "ProcessMessages 971", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790585877, "dur": 81, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790585960, "dur": 3, "ph": "X", "name": "ProcessMessages 1892", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790585965, "dur": 62, "ph": "X", "name": "ReadAsync 1892", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586031, "dur": 3, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586036, "dur": 90, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586130, "dur": 3, "ph": "X", "name": "ProcessMessages 1202", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586134, "dur": 77, "ph": "X", "name": "ReadAsync 1202", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586214, "dur": 3, "ph": "X", "name": "ProcessMessages 1363", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586219, "dur": 44, "ph": "X", "name": "ReadAsync 1363", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586265, "dur": 1, "ph": "X", "name": "ProcessMessages 843", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586267, "dur": 45, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586314, "dur": 1, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586316, "dur": 38, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586357, "dur": 1, "ph": "X", "name": "ProcessMessages 944", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586359, "dur": 35, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586396, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586397, "dur": 33, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586435, "dur": 32, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586469, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586471, "dur": 31, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586504, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586506, "dur": 52, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586560, "dur": 1, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586562, "dur": 43, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586607, "dur": 1, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586609, "dur": 34, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586645, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586647, "dur": 35, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586684, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586686, "dur": 35, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586723, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586724, "dur": 36, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586762, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586764, "dur": 34, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586800, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586801, "dur": 35, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586838, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586840, "dur": 35, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586877, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586879, "dur": 31, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586912, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586914, "dur": 39, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586954, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586956, "dur": 37, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586995, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790586996, "dur": 29, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587028, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587029, "dur": 44, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587075, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587077, "dur": 36, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587116, "dur": 34, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587152, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587154, "dur": 34, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587191, "dur": 31, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587224, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587226, "dur": 31, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587260, "dur": 35, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587297, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587298, "dur": 34, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587334, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587336, "dur": 28, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587367, "dur": 41, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587411, "dur": 2, "ph": "X", "name": "ProcessMessages 694", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587414, "dur": 48, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587466, "dur": 4, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587472, "dur": 65, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587539, "dur": 2, "ph": "X", "name": "ProcessMessages 874", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587543, "dur": 77, "ph": "X", "name": "ReadAsync 874", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587623, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587626, "dur": 69, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587698, "dur": 2, "ph": "X", "name": "ProcessMessages 990", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587702, "dur": 65, "ph": "X", "name": "ReadAsync 990", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587770, "dur": 2, "ph": "X", "name": "ProcessMessages 1006", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587774, "dur": 78, "ph": "X", "name": "ReadAsync 1006", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587855, "dur": 2, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587859, "dur": 59, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587921, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587924, "dur": 64, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587991, "dur": 2, "ph": "X", "name": "ProcessMessages 894", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790587995, "dur": 56, "ph": "X", "name": "ReadAsync 894", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790588054, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790588057, "dur": 62, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790588123, "dur": 2, "ph": "X", "name": "ProcessMessages 918", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790588127, "dur": 62, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790588192, "dur": 2, "ph": "X", "name": "ProcessMessages 1011", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790588198, "dur": 57, "ph": "X", "name": "ReadAsync 1011", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790588259, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790588262, "dur": 60, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790588325, "dur": 2, "ph": "X", "name": "ProcessMessages 945", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790588329, "dur": 58, "ph": "X", "name": "ReadAsync 945", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790588391, "dur": 2, "ph": "X", "name": "ProcessMessages 1063", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790588395, "dur": 54, "ph": "X", "name": "ReadAsync 1063", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790588451, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790588454, "dur": 62, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790588520, "dur": 2, "ph": "X", "name": "ProcessMessages 1113", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790588524, "dur": 58, "ph": "X", "name": "ReadAsync 1113", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790588585, "dur": 1, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790588587, "dur": 80, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790588671, "dur": 3, "ph": "X", "name": "ProcessMessages 1084", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790588675, "dur": 65, "ph": "X", "name": "ReadAsync 1084", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790588743, "dur": 2, "ph": "X", "name": "ProcessMessages 1108", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790588747, "dur": 64, "ph": "X", "name": "ReadAsync 1108", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790588814, "dur": 2, "ph": "X", "name": "ProcessMessages 1030", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790588818, "dur": 52, "ph": "X", "name": "ReadAsync 1030", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790588873, "dur": 1, "ph": "X", "name": "ProcessMessages 243", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790588876, "dur": 62, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790588941, "dur": 2, "ph": "X", "name": "ProcessMessages 869", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790588945, "dur": 69, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790589018, "dur": 3, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790589022, "dur": 62, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790589089, "dur": 53, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790589145, "dur": 1, "ph": "X", "name": "ProcessMessages 139", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790589148, "dur": 52, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790589203, "dur": 1, "ph": "X", "name": "ProcessMessages 151", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790589205, "dur": 64, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790589272, "dur": 2, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790589276, "dur": 57, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790589339, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790589342, "dur": 70, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790589415, "dur": 2, "ph": "X", "name": "ProcessMessages 866", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790589419, "dur": 61, "ph": "X", "name": "ReadAsync 866", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790589483, "dur": 2, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790589488, "dur": 65, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790589556, "dur": 2, "ph": "X", "name": "ProcessMessages 1141", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790589560, "dur": 75, "ph": "X", "name": "ReadAsync 1141", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790589638, "dur": 2, "ph": "X", "name": "ProcessMessages 1089", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790589642, "dur": 58, "ph": "X", "name": "ReadAsync 1089", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790589703, "dur": 2, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790589707, "dur": 64, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790589775, "dur": 2, "ph": "X", "name": "ProcessMessages 844", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790589779, "dur": 55, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790589837, "dur": 1, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790589839, "dur": 81, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790589923, "dur": 2, "ph": "X", "name": "ProcessMessages 1374", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790589928, "dur": 68, "ph": "X", "name": "ReadAsync 1374", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790589998, "dur": 2, "ph": "X", "name": "ProcessMessages 1316", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790590003, "dur": 67, "ph": "X", "name": "ReadAsync 1316", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790590073, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790590076, "dur": 63, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790590142, "dur": 2, "ph": "X", "name": "ProcessMessages 1039", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790590145, "dur": 56, "ph": "X", "name": "ReadAsync 1039", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790590205, "dur": 1, "ph": "X", "name": "ProcessMessages 168", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790590207, "dur": 64, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790590274, "dur": 2, "ph": "X", "name": "ProcessMessages 1071", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790590278, "dur": 63, "ph": "X", "name": "ReadAsync 1071", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790590344, "dur": 3, "ph": "X", "name": "ProcessMessages 1109", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790590349, "dur": 57, "ph": "X", "name": "ReadAsync 1109", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790590409, "dur": 2, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790590413, "dur": 63, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790590479, "dur": 2, "ph": "X", "name": "ProcessMessages 868", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790590483, "dur": 56, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790590543, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790590545, "dur": 66, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790590614, "dur": 2, "ph": "X", "name": "ProcessMessages 1129", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790590618, "dur": 63, "ph": "X", "name": "ReadAsync 1129", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790590685, "dur": 2, "ph": "X", "name": "ProcessMessages 1205", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790590688, "dur": 64, "ph": "X", "name": "ReadAsync 1205", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790590756, "dur": 2, "ph": "X", "name": "ProcessMessages 1104", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790590760, "dur": 60, "ph": "X", "name": "ReadAsync 1104", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790590823, "dur": 2, "ph": "X", "name": "ProcessMessages 720", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790590827, "dur": 64, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790590893, "dur": 2, "ph": "X", "name": "ProcessMessages 849", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790590897, "dur": 58, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790590958, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790590961, "dur": 64, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790591028, "dur": 2, "ph": "X", "name": "ProcessMessages 1021", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790591032, "dur": 63, "ph": "X", "name": "ReadAsync 1021", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790591097, "dur": 2, "ph": "X", "name": "ProcessMessages 974", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790591101, "dur": 78, "ph": "X", "name": "ReadAsync 974", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790591184, "dur": 2, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790591188, "dur": 66, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790591257, "dur": 3, "ph": "X", "name": "ProcessMessages 927", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790591261, "dur": 66, "ph": "X", "name": "ReadAsync 927", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790591331, "dur": 2, "ph": "X", "name": "ProcessMessages 785", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790591335, "dur": 59, "ph": "X", "name": "ReadAsync 785", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790591397, "dur": 2, "ph": "X", "name": "ProcessMessages 797", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790591401, "dur": 68, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790591472, "dur": 2, "ph": "X", "name": "ProcessMessages 1091", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790591476, "dur": 61, "ph": "X", "name": "ReadAsync 1091", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790591541, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790591544, "dur": 61, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790591608, "dur": 2, "ph": "X", "name": "ProcessMessages 929", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790591612, "dur": 62, "ph": "X", "name": "ReadAsync 929", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790591677, "dur": 2, "ph": "X", "name": "ProcessMessages 942", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790591696, "dur": 75, "ph": "X", "name": "ReadAsync 942", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790591774, "dur": 2, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790591779, "dur": 71, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790591853, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790591857, "dur": 69, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790591929, "dur": 2, "ph": "X", "name": "ProcessMessages 1193", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790591933, "dur": 55, "ph": "X", "name": "ReadAsync 1193", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790591991, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790591994, "dur": 60, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790592057, "dur": 2, "ph": "X", "name": "ProcessMessages 907", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790592061, "dur": 63, "ph": "X", "name": "ReadAsync 907", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790592127, "dur": 2, "ph": "X", "name": "ProcessMessages 988", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790592140, "dur": 57, "ph": "X", "name": "ReadAsync 988", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790592201, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790592204, "dur": 64, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790592271, "dur": 2, "ph": "X", "name": "ProcessMessages 997", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790592275, "dur": 64, "ph": "X", "name": "ReadAsync 997", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790592343, "dur": 2, "ph": "X", "name": "ProcessMessages 1153", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790592347, "dur": 66, "ph": "X", "name": "ReadAsync 1153", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790592416, "dur": 2, "ph": "X", "name": "ProcessMessages 1073", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790592419, "dur": 54, "ph": "X", "name": "ReadAsync 1073", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790592476, "dur": 2, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790592481, "dur": 62, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790592546, "dur": 2, "ph": "X", "name": "ProcessMessages 817", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790592550, "dur": 60, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790592613, "dur": 1, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790592616, "dur": 60, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790592679, "dur": 3, "ph": "X", "name": "ProcessMessages 959", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790592683, "dur": 63, "ph": "X", "name": "ReadAsync 959", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790592749, "dur": 2, "ph": "X", "name": "ProcessMessages 1146", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790592753, "dur": 61, "ph": "X", "name": "ReadAsync 1146", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790592817, "dur": 2, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790592820, "dur": 63, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790592886, "dur": 2, "ph": "X", "name": "ProcessMessages 967", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790592890, "dur": 63, "ph": "X", "name": "ReadAsync 967", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790592956, "dur": 2, "ph": "X", "name": "ProcessMessages 1140", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790592960, "dur": 60, "ph": "X", "name": "ReadAsync 1140", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790593023, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790593026, "dur": 61, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790593090, "dur": 3, "ph": "X", "name": "ProcessMessages 1008", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790593094, "dur": 63, "ph": "X", "name": "ReadAsync 1008", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790593160, "dur": 3, "ph": "X", "name": "ProcessMessages 1101", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790593164, "dur": 55, "ph": "X", "name": "ReadAsync 1101", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790593221, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790593224, "dur": 67, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790593294, "dur": 2, "ph": "X", "name": "ProcessMessages 787", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790593297, "dur": 27, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790593326, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790593328, "dur": 96, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790593430, "dur": 2, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790593434, "dur": 78, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790593516, "dur": 4, "ph": "X", "name": "ProcessMessages 907", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790593522, "dur": 87, "ph": "X", "name": "ReadAsync 907", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790593614, "dur": 3, "ph": "X", "name": "ProcessMessages 877", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790593618, "dur": 90, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790593712, "dur": 3, "ph": "X", "name": "ProcessMessages 1189", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790593716, "dur": 76, "ph": "X", "name": "ReadAsync 1189", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790593795, "dur": 2, "ph": "X", "name": "ProcessMessages 1152", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790593800, "dur": 71, "ph": "X", "name": "ReadAsync 1152", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790593873, "dur": 3, "ph": "X", "name": "ProcessMessages 1246", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790593878, "dur": 53, "ph": "X", "name": "ReadAsync 1246", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790593934, "dur": 2, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790593937, "dur": 64, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790594004, "dur": 2, "ph": "X", "name": "ProcessMessages 871", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790594008, "dur": 83, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790594094, "dur": 3, "ph": "X", "name": "ProcessMessages 1072", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790594098, "dur": 67, "ph": "X", "name": "ReadAsync 1072", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790594167, "dur": 2, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790594171, "dur": 81, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790594255, "dur": 2, "ph": "X", "name": "ProcessMessages 1075", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790594259, "dur": 86, "ph": "X", "name": "ReadAsync 1075", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790594351, "dur": 3, "ph": "X", "name": "ProcessMessages 889", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790594357, "dur": 79, "ph": "X", "name": "ReadAsync 889", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790594441, "dur": 3, "ph": "X", "name": "ProcessMessages 797", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790594446, "dur": 66, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790594515, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790594518, "dur": 26, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790594546, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790594549, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790594623, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790594626, "dur": 114, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790594751, "dur": 3, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790594756, "dur": 47, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790594807, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790594810, "dur": 48, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790594862, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790594868, "dur": 63, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790594934, "dur": 2, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790594937, "dur": 39, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790594979, "dur": 3, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790594984, "dur": 49, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595037, "dur": 3, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595042, "dur": 44, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595089, "dur": 2, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595093, "dur": 33, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595127, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595129, "dur": 22, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595154, "dur": 80, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595238, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595313, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595318, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595370, "dur": 2, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595373, "dur": 42, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595419, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595425, "dur": 46, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595473, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595476, "dur": 26, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595505, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595506, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595548, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595549, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595586, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595588, "dur": 48, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595638, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595640, "dur": 29, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595672, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595673, "dur": 22, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595699, "dur": 63, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595766, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595771, "dur": 50, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595823, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595827, "dur": 37, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595867, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595869, "dur": 24, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595895, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595898, "dur": 39, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595939, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595942, "dur": 48, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595993, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790595996, "dur": 34, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596032, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596035, "dur": 50, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596087, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596090, "dur": 27, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596120, "dur": 2, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596123, "dur": 38, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596164, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596166, "dur": 37, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596206, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596221, "dur": 60, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596285, "dur": 3, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596289, "dur": 80, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596372, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596375, "dur": 35, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596413, "dur": 4, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596418, "dur": 68, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596490, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596492, "dur": 42, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596537, "dur": 3, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596542, "dur": 33, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596577, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596579, "dur": 30, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596611, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596614, "dur": 23, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596639, "dur": 2, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596643, "dur": 88, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596734, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596736, "dur": 34, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596773, "dur": 3, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596778, "dur": 37, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596817, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596845, "dur": 137, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790596987, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790597043, "dur": 3, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790597047, "dur": 50, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790597100, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790597102, "dur": 76, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790597181, "dur": 205, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790597391, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790597443, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790597446, "dur": 287, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790597739, "dur": 123, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790597866, "dur": 23, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790597892, "dur": 1168, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790599065, "dur": 886, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790599954, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790599957, "dur": 1189, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790601150, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790601152, "dur": 402, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790601560, "dur": 1345, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790602908, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790602910, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790602954, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790602992, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790602994, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790603036, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790603068, "dur": 288, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790603360, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790603392, "dur": 1198, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790604594, "dur": 401, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790604998, "dur": 3, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790605002, "dur": 83, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790605088, "dur": 2, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790605091, "dur": 26, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790605118, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790605120, "dur": 16, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790605139, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790605190, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790605218, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790605258, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790605290, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790605291, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790605321, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790605364, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790605366, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790605395, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790605495, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790605524, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790605558, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790605575, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790605620, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790605659, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790605662, "dur": 29, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790605695, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790605746, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790605773, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790605819, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790605847, "dur": 113, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790605964, "dur": 134, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790606103, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790606129, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790606131, "dur": 44, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790606179, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790606216, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790606217, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790606262, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790606300, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790606317, "dur": 96, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790606417, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790606447, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790606484, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790606517, "dur": 67, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790606588, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790606620, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790606659, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790606662, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790606716, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790606719, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790606750, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790606752, "dur": 129, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790606884, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790606920, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790606922, "dur": 109, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790607035, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790607076, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790607116, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790607119, "dur": 150, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790607273, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790607308, "dur": 4, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790607313, "dur": 27, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790607342, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790607345, "dur": 75, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790607421, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790607453, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790607478, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790607481, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790607508, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790607531, "dur": 112, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790607645, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790607696, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790607699, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790607732, "dur": 409, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790608147, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790608182, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790608184, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790608243, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790608274, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790608308, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790608339, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790608363, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790608407, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790608450, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790608453, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790608509, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790608532, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790608534, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790608566, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790608608, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790608610, "dur": 87, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790608699, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790608701, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790608738, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790608741, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790608765, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790608767, "dur": 356, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790609127, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790609183, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790609185, "dur": 76, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790609266, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790609269, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790609325, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790609328, "dur": 191, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790609521, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790609523, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790609565, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790609568, "dur": 170, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790609743, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790609799, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790609802, "dur": 77, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790609882, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790609884, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790609931, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790609933, "dur": 206, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790610142, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790610145, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790610197, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790610199, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790610242, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790610245, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790610274, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790610276, "dur": 773, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790611056, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790611059, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790611150, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790611153, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790611216, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790611218, "dur": 573, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790611799, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790611871, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790611875, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790611925, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790611928, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790611981, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790611983, "dur": 178, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790612166, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790612169, "dur": 344, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790612518, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790612522, "dur": 73, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790612598, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790612604, "dur": 44751, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790657368, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790657374, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790657449, "dur": 37, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790657487, "dur": 7918, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790665416, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790665421, "dur": 1181, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790666607, "dur": 4, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790666612, "dur": 46, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790666664, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790666665, "dur": 4454, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790671127, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790671131, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790671202, "dur": 9, "ph": "X", "name": "ProcessMessages 1296", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790671213, "dur": 38, "ph": "X", "name": "ReadAsync 1296", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790671255, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790671258, "dur": 52, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790671312, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790671315, "dur": 67, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790671385, "dur": 3, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790671390, "dur": 25, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790671417, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790671419, "dur": 30, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790671453, "dur": 27, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790671482, "dur": 200, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790671687, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790671720, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790671722, "dur": 69181, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790740915, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790740922, "dur": 100, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790741030, "dur": 38, "ph": "X", "name": "ProcessMessages 1076", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790741070, "dur": 3021, "ph": "X", "name": "ReadAsync 1076", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790744097, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790744101, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790744148, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 2208, "tid": 85899345920, "ts": 1750060790744151, "dur": 6338, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 2208, "tid": 119, "ts": 1750060790751209, "dur": 3043, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 2208, "tid": 81604378624, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 2208, "tid": 81604378624, "ts": 1750060790426567, "dur": 6, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 2208, "tid": 81604378624, "ts": 1750060790426573, "dur": 15192, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 2208, "tid": 81604378624, "ts": 1750060790441767, "dur": 37, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 2208, "tid": 119, "ts": 1750060790754256, "dur": 29, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 2208, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 2208, "tid": 1, "ts": 1750060789192625, "dur": 37233, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 2208, "tid": 1, "ts": 1750060789229866, "dur": 532475, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 2208, "tid": 1, "ts": 1750060789762344, "dur": 60560, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 2208, "tid": 119, "ts": 1750060790754288, "dur": 11, "ph": "X", "name": "", "args": {}}, {"pid": 2208, "tid": 77309411328, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789191694, "dur": 9512, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789201208, "dur": 629709, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789201446, "dur": 165, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789201614, "dur": 676, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789202296, "dur": 1628, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789204214, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789204219, "dur": 172, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789204491, "dur": 16, "ph": "X", "name": "ProcessMessages 8052", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789204511, "dur": 769, "ph": "X", "name": "ReadAsync 8052", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789205401, "dur": 12, "ph": "X", "name": "ProcessMessages 7157", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789205665, "dur": 244, "ph": "X", "name": "ReadAsync 7157", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789205911, "dur": 20, "ph": "X", "name": "ProcessMessages 11959", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789205933, "dur": 434, "ph": "X", "name": "ReadAsync 11959", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789206372, "dur": 2, "ph": "X", "name": "ProcessMessages 940", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789206376, "dur": 485, "ph": "X", "name": "ReadAsync 940", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789206866, "dur": 17, "ph": "X", "name": "ProcessMessages 9998", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789206885, "dur": 373, "ph": "X", "name": "ReadAsync 9998", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789207264, "dur": 12, "ph": "X", "name": "ProcessMessages 5853", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789207278, "dur": 289, "ph": "X", "name": "ReadAsync 5853", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789207572, "dur": 7, "ph": "X", "name": "ProcessMessages 3475", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789207684, "dur": 354, "ph": "X", "name": "ReadAsync 3475", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789208045, "dur": 13, "ph": "X", "name": "ProcessMessages 6311", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789208061, "dur": 440, "ph": "X", "name": "ReadAsync 6311", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789208505, "dur": 12, "ph": "X", "name": "ProcessMessages 6149", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789208618, "dur": 194, "ph": "X", "name": "ReadAsync 6149", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789208815, "dur": 13, "ph": "X", "name": "ProcessMessages 7192", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789208910, "dur": 157, "ph": "X", "name": "ReadAsync 7192", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789209140, "dur": 7, "ph": "X", "name": "ProcessMessages 3196", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789209149, "dur": 93, "ph": "X", "name": "ReadAsync 3196", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789209244, "dur": 6, "ph": "X", "name": "ProcessMessages 3365", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789209253, "dur": 413, "ph": "X", "name": "ReadAsync 3365", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789209670, "dur": 2, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789209674, "dur": 140, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789209828, "dur": 11, "ph": "X", "name": "ProcessMessages 6155", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789209841, "dur": 1376, "ph": "X", "name": "ReadAsync 6155", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789211225, "dur": 8, "ph": "X", "name": "ProcessMessages 2805", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789211236, "dur": 595, "ph": "X", "name": "ReadAsync 2805", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789211836, "dur": 22, "ph": "X", "name": "ProcessMessages 14713", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789211860, "dur": 73, "ph": "X", "name": "ReadAsync 14713", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789212070, "dur": 3, "ph": "X", "name": "ProcessMessages 1130", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789212076, "dur": 723, "ph": "X", "name": "ReadAsync 1130", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789212801, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789212804, "dur": 367, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789213175, "dur": 12, "ph": "X", "name": "ProcessMessages 9783", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789213510, "dur": 179, "ph": "X", "name": "ReadAsync 9783", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789213692, "dur": 9, "ph": "X", "name": "ProcessMessages 8984", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789214023, "dur": 87, "ph": "X", "name": "ReadAsync 8984", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789214112, "dur": 6, "ph": "X", "name": "ProcessMessages 6627", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789214120, "dur": 221, "ph": "X", "name": "ReadAsync 6627", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789214412, "dur": 2, "ph": "X", "name": "ProcessMessages 2333", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789214416, "dur": 242, "ph": "X", "name": "ReadAsync 2333", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789214735, "dur": 553, "ph": "X", "name": "ProcessMessages 3586", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789216847, "dur": 671, "ph": "X", "name": "ReadAsync 3586", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789217719, "dur": 35, "ph": "X", "name": "ProcessMessages 20503", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789217757, "dur": 154, "ph": "X", "name": "ReadAsync 20503", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789217948, "dur": 9, "ph": "X", "name": "ProcessMessages 4760", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789218430, "dur": 345, "ph": "X", "name": "ReadAsync 4760", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789218901, "dur": 15, "ph": "X", "name": "ProcessMessages 7031", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789218918, "dur": 287, "ph": "X", "name": "ReadAsync 7031", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789219379, "dur": 4, "ph": "X", "name": "ProcessMessages 1804", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789219386, "dur": 247, "ph": "X", "name": "ReadAsync 1804", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789219637, "dur": 4, "ph": "X", "name": "ProcessMessages 1618", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789219824, "dur": 315, "ph": "X", "name": "ReadAsync 1618", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789220146, "dur": 8, "ph": "X", "name": "ProcessMessages 3580", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789220156, "dur": 298, "ph": "X", "name": "ReadAsync 3580", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789220459, "dur": 5, "ph": "X", "name": "ProcessMessages 2185", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789220467, "dur": 345, "ph": "X", "name": "ReadAsync 2185", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789221131, "dur": 5, "ph": "X", "name": "ProcessMessages 2479", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789221139, "dur": 252, "ph": "X", "name": "ReadAsync 2479", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789221395, "dur": 6, "ph": "X", "name": "ProcessMessages 3571", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789221500, "dur": 422, "ph": "X", "name": "ReadAsync 3571", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789221924, "dur": 2, "ph": "X", "name": "ProcessMessages 2232", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789221928, "dur": 502, "ph": "X", "name": "ReadAsync 2232", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789222433, "dur": 4, "ph": "X", "name": "ProcessMessages 3492", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789222438, "dur": 566, "ph": "X", "name": "ReadAsync 3492", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789223212, "dur": 15, "ph": "X", "name": "ProcessMessages 7967", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789223376, "dur": 110, "ph": "X", "name": "ReadAsync 7967", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789223490, "dur": 8, "ph": "X", "name": "ProcessMessages 5242", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789223593, "dur": 354, "ph": "X", "name": "ReadAsync 5242", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789223951, "dur": 3, "ph": "X", "name": "ProcessMessages 1412", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789223956, "dur": 368, "ph": "X", "name": "ReadAsync 1412", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789224328, "dur": 5, "ph": "X", "name": "ProcessMessages 2232", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789224335, "dur": 202, "ph": "X", "name": "ReadAsync 2232", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789224541, "dur": 3, "ph": "X", "name": "ProcessMessages 1587", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789224546, "dur": 462, "ph": "X", "name": "ReadAsync 1587", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789225013, "dur": 8, "ph": "X", "name": "ProcessMessages 3809", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789225024, "dur": 608, "ph": "X", "name": "ReadAsync 3809", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789226599, "dur": 5, "ph": "X", "name": "ProcessMessages 2324", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789226693, "dur": 294, "ph": "X", "name": "ReadAsync 2324", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789226989, "dur": 10, "ph": "X", "name": "ProcessMessages 14301", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789227434, "dur": 389, "ph": "X", "name": "ReadAsync 14301", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789227828, "dur": 8, "ph": "X", "name": "ProcessMessages 4795", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789227838, "dur": 465, "ph": "X", "name": "ReadAsync 4795", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789228411, "dur": 3, "ph": "X", "name": "ProcessMessages 2605", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789228416, "dur": 296, "ph": "X", "name": "ReadAsync 2605", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789228716, "dur": 12, "ph": "X", "name": "ProcessMessages 6198", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789229256, "dur": 144, "ph": "X", "name": "ReadAsync 6198", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789229404, "dur": 10, "ph": "X", "name": "ProcessMessages 4145", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789229478, "dur": 42, "ph": "X", "name": "ReadAsync 4145", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789229689, "dur": 6, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789229698, "dur": 162, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789229865, "dur": 7, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789229874, "dur": 309, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789230186, "dur": 3, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789230190, "dur": 343, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789230718, "dur": 6, "ph": "X", "name": "ProcessMessages 924", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789230725, "dur": 89, "ph": "X", "name": "ReadAsync 924", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789231079, "dur": 533, "ph": "X", "name": "ProcessMessages 2852", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789231875, "dur": 126, "ph": "X", "name": "ReadAsync 2852", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789232673, "dur": 8, "ph": "X", "name": "ProcessMessages 1040", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789232684, "dur": 870, "ph": "X", "name": "ReadAsync 1040", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789233558, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789233609, "dur": 2343, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789235972, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789235974, "dur": 202, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789236715, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789236718, "dur": 201, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789236923, "dur": 147, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789237073, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789237075, "dur": 1499, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789238582, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789238627, "dur": 4, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789238632, "dur": 25, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789238809, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789239588, "dur": 331, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789239922, "dur": 5, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789240202, "dur": 122, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789240383, "dur": 3, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789240542, "dur": 79, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789240788, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789240791, "dur": 28, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789241160, "dur": 103, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789241265, "dur": 88, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789241417, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789241421, "dur": 121, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789241691, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789241830, "dur": 125, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789242214, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789242217, "dur": 1376, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789243601, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789243620, "dur": 280, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789244078, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789244082, "dur": 198, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789244283, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789244285, "dur": 120, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789244486, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789244489, "dur": 739, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789245559, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789245563, "dur": 137, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789245797, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789245800, "dur": 876, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789246748, "dur": 276, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789247088, "dur": 169, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789247260, "dur": 3, "ph": "X", "name": "ProcessMessages 104", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789247265, "dur": 58585, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789306546, "dur": 521, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789307330, "dur": 8436, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789315778, "dur": 31, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789315811, "dur": 1560, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789317374, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789317377, "dur": 244, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789317624, "dur": 7, "ph": "X", "name": "ProcessMessages 992", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789317632, "dur": 557, "ph": "X", "name": "ReadAsync 992", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789318192, "dur": 5, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789318199, "dur": 1243, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789319624, "dur": 6, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789320290, "dur": 59436, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789381651, "dur": 195, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789384755, "dur": 2445, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789388458, "dur": 2055, "ph": "X", "name": "ProcessMessages 1640", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789391021, "dur": 433314, "ph": "X", "name": "ReadAsync 1640", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789824344, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789824348, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789824407, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 2208, "tid": 77309411328, "ts": 1750060789824410, "dur": 6499, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 2208, "tid": 119, "ts": 1750060790754302, "dur": 579, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 2208, "tid": 73014444032, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 2208, "tid": 73014444032, "ts": 1750060789186981, "dur": 635959, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 2208, "tid": 73014444032, "ts": 1750060789822941, "dur": 2, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 2208, "tid": 73014444032, "ts": 1750060789822944, "dur": 71, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 2208, "tid": 119, "ts": 1750060790754885, "dur": 14, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 2208, "tid": 68719476736, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 2208, "tid": 68719476736, "ts": 1750060789167901, "dur": 663050, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 2208, "tid": 68719476736, "ts": 1750060789171036, "dur": 15808, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 2208, "tid": 68719476736, "ts": 1750060789830959, "dur": 591788, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 2208, "tid": 68719476736, "ts": 1750060790422772, "dur": 327779, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 2208, "tid": 68719476736, "ts": 1750060790422967, "dur": 3556, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 2208, "tid": 68719476736, "ts": 1750060790750560, "dur": 61, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 2208, "tid": 68719476736, "ts": 1750060790750576, "dur": 28, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 2208, "tid": 68719476736, "ts": 1750060790750624, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 2208, "tid": 119, "ts": 1750060790754902, "dur": 39, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1750060790442097, "dur": 132346, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060790574453, "dur": 414, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060790574970, "dur": 72, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1750060790575042, "dur": 371, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060790575540, "dur": 96, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_1BD15D364A91220E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750060790575669, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_C5179C2B98A35602.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750060790575772, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_6E196A46756374CC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750060790575861, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_A84A75205A4D3666.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750060790576250, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_A0D61AB0BD46DF64.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750060790576447, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_77C9B2F6CDEA9940.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750060790576528, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_55EFA22634AA8841.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750060790576717, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_E1BAC9240B32956E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750060790577345, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_7D559772BEA705DB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750060790581888, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750060790589357, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750060790591508, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750060790575430, "dur": 19377, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060790594821, "dur": 147931, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060790742753, "dur": 343, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060790743097, "dur": 128, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060790744394, "dur": 53, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060790744470, "dur": 1104, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1750060790577378, "dur": 17777, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060790595292, "dur": 218, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_88EB000108787B7B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750060790595517, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_C9BF491CE9429A6C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750060790596280, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1750060790596502, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1750060790596713, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750060790596913, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1750060790597170, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060790597380, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750060790597453, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060790597769, "dur": 133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060790597903, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060790598078, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060790598337, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060790598554, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060790598737, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060790599279, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060790599484, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060790599675, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060790599867, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060790600088, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060790600273, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060790600485, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060790600696, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060790600896, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060790601098, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060790601318, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060790601542, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060790601721, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060790601903, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060790602081, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060790602280, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060790602501, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060790602925, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060790603342, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060790603831, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750060790604293, "dur": 1407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750060790605701, "dur": 500, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060790606252, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750060790606444, "dur": 788, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750060790607312, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750060790607461, "dur": 417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750060790607924, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750060790608065, "dur": 668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750060790608782, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750060790608917, "dur": 1378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750060790610335, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750060790610466, "dur": 904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750060790611417, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750060790611556, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750060790612148, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750060790612265, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750060790612778, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750060790612516, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750060790612964, "dur": 128359, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750060790576275, "dur": 18565, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790596459, "dur": 161, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/6000.0.50f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 2, "ts": 1750060790596621, "dur": 966, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/6000.0.50f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 2, "ts": 1750060790597588, "dur": 56, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/6000.0.50f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 2, "ts": 1750060790594846, "dur": 2798, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790597645, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790597999, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790598209, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790598454, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790598647, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790598838, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790599326, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790599553, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790599833, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790600027, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790600215, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790600815, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790601055, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790601264, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790601469, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790601705, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790601899, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790602090, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790602270, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790602607, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790602928, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790603374, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790603796, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750060790604001, "dur": 572, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790604577, "dur": 545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750060790605123, "dur": 332, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790605476, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790605730, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750060790605898, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750060790606245, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790606472, "dur": 589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790607061, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790607317, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790607729, "dur": 4446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790612176, "dur": 49518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790661699, "dur": 2013, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750060790663759, "dur": 2177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750060790665937, "dur": 757, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790666701, "dur": 2680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750060790669382, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790669467, "dur": 1943, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750060790671411, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060790671523, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1750060790671737, "dur": 71002, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790576654, "dur": 18318, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790594983, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_76ADE6F2C4A0FDCA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750060790595059, "dur": 332, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_76ADE6F2C4A0FDCA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750060790595496, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_136791916CB4C4E9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750060790596358, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1750060790596685, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790596762, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1750060790597195, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790597380, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790597555, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790597915, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790598145, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790598589, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790598793, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790599285, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790599971, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790600156, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790600438, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790600804, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790600987, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790601572, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790601766, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790602000, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790602191, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790602412, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790602808, "dur": 525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790603333, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790603831, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750060790604022, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790604088, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750060790604286, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750060790604851, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790605006, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750060790605096, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750060790605544, "dur": 476, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790606050, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790606254, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750060790606442, "dur": 1196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750060790607706, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750060790607840, "dur": 1092, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750060790608980, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750060790609111, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750060790609518, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750060790609649, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750060790610128, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750060790610258, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750060790610633, "dur": 1572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790612205, "dur": 47528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790659740, "dur": 2543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750060790662321, "dur": 2176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750060790664541, "dur": 2123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750060790666708, "dur": 2722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750060790669431, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060790669703, "dur": 2031, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750060790671754, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750060790671854, "dur": 70910, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790576880, "dur": 18261, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790595362, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_BF4431DE103A29DE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060790595460, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_BF4431DE103A29DE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060790595533, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B739A7DF6FD203B7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060790596183, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750060790596353, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750060790596532, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750060790596916, "dur": 212, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750060790597202, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790597410, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750060790597478, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790597723, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790598015, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790598220, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790598437, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790598641, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790598819, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790599308, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790599496, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790599708, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790600004, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790600195, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790600465, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790600645, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790600835, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790601031, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790601231, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790601410, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790601619, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790601823, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790602035, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790602215, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790602474, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790602811, "dur": 498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790603330, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790603825, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060790603997, "dur": 476, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790604476, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750060790605049, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790605318, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790605621, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790605741, "dur": 568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790606309, "dur": 761, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790607071, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790607357, "dur": 4791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790612150, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060790612311, "dur": 47393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790659710, "dur": 2588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750060790662300, "dur": 936, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790663243, "dur": 2106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750060790665350, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790665613, "dur": 3148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750060790668762, "dur": 568, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790669336, "dur": 1961, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750060790671462, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060790671541, "dur": 268, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Searcher.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750060790671810, "dur": 70910, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790577379, "dur": 17798, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790595186, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_C5179C2B98A35602.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060790595401, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_9DD7D897C42E415F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060790595499, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_1DB8604BF3C2B15B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060790596319, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750060790596484, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1750060790596665, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750060790596772, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1750060790597122, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790597405, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750060790597456, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790597655, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790597879, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790598048, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790598345, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790598672, "dur": 773, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@e07c4c789cd8\\Editor\\GraphView\\Views\\VFXPicker.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750060790598575, "dur": 946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790599521, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790599695, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790599869, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790600068, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790600269, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790600613, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790600842, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790601051, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790601280, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790601483, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790601679, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790601879, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790602071, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790602232, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790602433, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790602663, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790602906, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790603352, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790603813, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060790603990, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790604057, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750060790604570, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060790604742, "dur": 546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750060790605446, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790605746, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790606303, "dur": 758, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790607062, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790607313, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060790607473, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750060790607882, "dur": 4325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790612208, "dur": 47507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790659723, "dur": 3126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750060790662851, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060790663139, "dur": 8681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750060790671853, "dur": 70869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790577424, "dur": 17769, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790595288, "dur": 212, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_A88759632040C0E4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750060790595509, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_3EE9BB20E930FDFD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750060790596196, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1750060790596318, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750060790596478, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750060790596597, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1750060790596907, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750060790597162, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790597392, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790597792, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790597956, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790598148, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790598380, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790598603, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790598801, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790599287, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790599476, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790599665, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790599835, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790600069, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790600282, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790600471, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790600681, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790600920, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790601124, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790601320, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790601510, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790601702, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790601879, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790602112, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790602297, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790602558, "dur": 108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790602667, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790602939, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790603350, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790603840, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750060790604382, "dur": 450, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790604836, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750060790605444, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790605734, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790605941, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790606259, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750060790606439, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750060790606796, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790607067, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790607315, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750060790607416, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750060790607698, "dur": 4484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790612183, "dur": 47574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790659766, "dur": 3721, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750060790663488, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790663664, "dur": 2390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750060790666088, "dur": 2836, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750060790668925, "dur": 2470, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790671416, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1750060790671481, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1750060790671735, "dur": 341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060790672090, "dur": 70642, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790576336, "dur": 18514, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790595057, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_7DDC965C907AB2A5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750060790595521, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_9CC28255281D0D3B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750060790596208, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750060790596531, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1750060790596842, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750060790597171, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790597432, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790597745, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790598038, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790598282, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790598474, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790598742, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790598955, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790599430, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790599599, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790599785, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790600426, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790600702, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790600919, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790601097, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790601293, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790601486, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790601714, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790601901, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790602124, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790602302, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790602583, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790602892, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790603332, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790603821, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750060790604039, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750060790604220, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790604454, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750060790604940, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790605006, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750060790605101, "dur": 460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750060790605562, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790605663, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790605727, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750060790605900, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790605965, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750060790606478, "dur": 579, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790607083, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790607328, "dur": 1456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790608785, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750060790608883, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750060790609141, "dur": 3031, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790612173, "dur": 47617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790659795, "dur": 4017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750060790663813, "dur": 575, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790664396, "dur": 4026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750060790668424, "dur": 860, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790669290, "dur": 2073, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750060790671439, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060790671573, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750060790671721, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1750060790671792, "dur": 70933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790576480, "dur": 18404, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790594895, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_1BD15D364A91220E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750060790595085, "dur": 294, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_CF8C437788A387C6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750060790595492, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_1E5BE8A4F6F01B45.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750060790596440, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750060790596617, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750060790596914, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750060790597173, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790597441, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790597603, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790597909, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790598112, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790598396, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790598574, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790598776, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790598966, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790599427, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790599637, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790599852, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790600068, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790600250, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790600511, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790600739, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790600933, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790601142, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790601338, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790601510, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790601851, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790602068, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790602261, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790602449, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790602665, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790602962, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790603347, "dur": 1622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790604970, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750060790605145, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750060790605600, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790605750, "dur": 565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790606315, "dur": 744, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790607077, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790607340, "dur": 2182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790609524, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750060790609637, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750060790609902, "dur": 2278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790612180, "dur": 47595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790659782, "dur": 4885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750060790664668, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790664781, "dur": 2163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750060790666945, "dur": 4578, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060790671523, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750060790671742, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1750060790671826, "dur": 70936, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790576400, "dur": 18468, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790595081, "dur": 268, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_9F0F250247658205.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750060790595478, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790596220, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750060790596405, "dur": 2957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750060790599464, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790599658, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790599852, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790600087, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790600296, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790600518, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790600700, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790600922, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790601103, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790601344, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790601577, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790601786, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790601988, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790602187, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790602365, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790602621, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790602879, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790603313, "dur": 487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790603802, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750060790604243, "dur": 1326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790605578, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750060790606031, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790606257, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750060790606460, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750060790606890, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790607072, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790607349, "dur": 4817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790612167, "dur": 49553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790661747, "dur": 2055, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750060790663804, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790664116, "dur": 2114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750060790666231, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790666366, "dur": 2741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750060790669108, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790669194, "dur": 2085, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750060790671441, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060790671535, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750060790671678, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Updater.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750060790671775, "dur": 70963, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790576542, "dur": 18371, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790595057, "dur": 262, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_EAF6FA72B704DB04.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750060790595486, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790596318, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1750060790596585, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1750060790596759, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1750060790596910, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1750060790597122, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790597390, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790597590, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790598676, "dur": 762, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@9b7e7eef789d\\Editor\\TMP\\TMPro_SortingLayerHelper.cs"}}, {"pid": 12345, "tid": 10, "ts": 1750060790597906, "dur": 1640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790599546, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790599739, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790599934, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790600122, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790600351, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790600551, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790600782, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790600968, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790601282, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790601487, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790601707, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790602021, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790602210, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790602410, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790602638, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790602938, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790603331, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790603809, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750060790604013, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750060790604492, "dur": 617, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790605141, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750060790605311, "dur": 559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750060790605870, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790606222, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750060790606371, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790606505, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750060790606837, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790607086, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790607321, "dur": 605, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790607927, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750060790608032, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750060790608539, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750060790608633, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750060790608943, "dur": 3225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790612169, "dur": 47517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790659688, "dur": 2204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750060790661931, "dur": 4860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750060790666792, "dur": 1950, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790668750, "dur": 2024, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750060790670775, "dur": 522, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790671455, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790671732, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060790671848, "dur": 70869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790576592, "dur": 18343, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790595041, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_7B06E7D06A357F34.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750060790595433, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_9C6FA246188F95D9.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750060790595502, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_8D701D583C70E786.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750060790596354, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1750060790596500, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750060790596716, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750060790597788, "dur": 300, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750060790598089, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790598332, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790598519, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790598708, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790598886, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790599363, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790599545, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790599718, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790599921, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790600116, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790600312, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790600578, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790600791, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790600988, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790601213, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790601396, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790601873, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790602510, "dur": 53, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790602564, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790602949, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790603352, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790603813, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750060790603999, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790604179, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750060790604702, "dur": 567, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790605274, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750060790605507, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750060790606016, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750060790606133, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750060790606529, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790606586, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790606687, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790607073, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790607347, "dur": 4809, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790612157, "dur": 47535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790659693, "dur": 1952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1750060790661647, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790661925, "dur": 2767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1750060790664694, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790664819, "dur": 2963, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1750060790667783, "dur": 877, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790668667, "dur": 1967, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1750060790670682, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790671050, "dur": 375, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790671538, "dur": 157, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750060790671731, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060790671850, "dur": 70877, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790576657, "dur": 18335, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790595001, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_39CA7637537503FE.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750060790595098, "dur": 323, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_39CA7637537503FE.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750060790595468, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_7D33EB796E04AD1F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750060790595537, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_CF814559B1575A99.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750060790596185, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750060790596352, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1750060790596455, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750060790596827, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750060790596905, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1750060790596991, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750060790597160, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790597383, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790597579, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790597914, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790598357, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790598830, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790599291, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790599487, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790599670, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790599879, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790600163, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790600419, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790600621, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790600847, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790601049, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790601251, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790601432, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790601797, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790602038, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790602402, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790602656, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790602976, "dur": 341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790603317, "dur": 475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790603795, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750060790604358, "dur": 704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750060790605062, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790605181, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790605512, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750060790605668, "dur": 866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750060790606571, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790606636, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790607066, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790607312, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750060790607418, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750060790607709, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750060790607802, "dur": 862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750060790608727, "dur": 3424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790612164, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750060790612541, "dur": 47148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790659699, "dur": 2674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750060790662375, "dur": 553, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790662935, "dur": 2065, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750060790665000, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790665408, "dur": 2836, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750060790668285, "dur": 1984, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750060790670269, "dur": 747, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790671024, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790671164, "dur": 411, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060790671576, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750060790671740, "dur": 71008, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790576723, "dur": 18299, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790595090, "dur": 240, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_5D5EF2E9E0015A01.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750060790595514, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_E3FF4E8988E07430.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750060790596011, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750060790596204, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750060790596397, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1750060790596483, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1750060790596658, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1750060790597173, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790597406, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2098168516021703545.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750060790597472, "dur": 823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790598296, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790598481, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790598661, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790598832, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790599314, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790599974, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790600183, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790600356, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790600552, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790600742, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790600944, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790601143, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790601331, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790601549, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790601756, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790601991, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790602200, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790602407, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790602808, "dur": 506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790603314, "dur": 502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790603817, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750060790604011, "dur": 1234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750060790605246, "dur": 424, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790605694, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750060790605878, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750060790606314, "dur": 529, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790606847, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790607080, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790607333, "dur": 1651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790608985, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750060790609078, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750060790609512, "dur": 2673, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790612186, "dur": 47558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790659748, "dur": 3352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750060790663101, "dur": 681, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790663788, "dur": 2121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750060790665950, "dur": 2874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750060790668825, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790669112, "dur": 1821, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750060790670934, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790671285, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060790671536, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 13, "ts": 1750060790671731, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualEffectGraph.Runtime.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1750060790671816, "dur": 70938, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790576768, "dur": 18280, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790595059, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_0C579B27BF533F1A.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750060790595404, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_40EACCB6DE9B854E.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750060790595464, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_E6B906AFC5CA6A31.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750060790596099, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1750060790596182, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1750060790596395, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1750060790596906, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1750060790597169, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790597390, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790597609, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790597967, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790598214, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790598460, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790598644, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790598844, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790599328, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790599536, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790599737, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790599950, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790600137, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790600397, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790600605, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790600819, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790601003, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790601230, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790601410, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790601718, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790601917, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790602108, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790602283, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790602471, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790602809, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790603312, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790603814, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750060790604024, "dur": 561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750060790604646, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750060790605150, "dur": 784, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790605959, "dur": 295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790606255, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750060790606485, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750060790606983, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790607065, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790607316, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750060790607487, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750060790608051, "dur": 4109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790612161, "dur": 50178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790662341, "dur": 2473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750060790664815, "dur": 576, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790665396, "dur": 2829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750060790668226, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060790668347, "dur": 2036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750060790670411, "dur": 1637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750060790672082, "dur": 70659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060790576819, "dur": 18259, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060790595099, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_E6F99946C6EBBC96.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750060790595266, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_2877E6341DB76B77.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750060790595473, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060790596039, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750060790596271, "dur": 3734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750060790600085, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750060790600377, "dur": 2877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750060790603348, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750060790603434, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750060790603788, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750060790603976, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750060790604485, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060790604617, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060790604876, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750060790605094, "dur": 552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750060790605725, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750060790605909, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750060790606288, "dur": 737, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060790607070, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 15, "ts": 1750060790607509, "dur": 134, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060790607656, "dur": 50041, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 15, "ts": 1750060790659684, "dur": 1965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750060790661650, "dur": 718, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060790662376, "dur": 1968, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750060790664345, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060790664403, "dur": 2104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750060790666508, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060790666705, "dur": 2771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750060790669477, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060790669543, "dur": 2063, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750060790671720, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1750060790671787, "dur": 70948, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790576862, "dur": 18253, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790595489, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_7D559772BEA705DB.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750060790596188, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750060790596358, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1750060790596586, "dur": 239, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750060790596911, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750060790597212, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790597320, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12900333509587573630.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750060790597386, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790597611, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790597781, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790597955, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790598150, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790598389, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790598568, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790598744, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790598926, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790599412, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790599598, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790599776, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790600006, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790600259, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790600477, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790600666, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790601290, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790601521, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790601721, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790601900, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790602078, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790602263, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790602460, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790602718, "dur": 51, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790602807, "dur": 506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790603314, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790603790, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750060790603991, "dur": 661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1750060790604712, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750060790604889, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750060790605071, "dur": 317, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750060790605389, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1750060790605804, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790605933, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790606269, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750060790606450, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1750060790606852, "dur": 219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790607071, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790607354, "dur": 4804, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790612159, "dur": 47525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790659685, "dur": 3559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750060790663245, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790663472, "dur": 2167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750060790665640, "dur": 780, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790666425, "dur": 2767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750060790669194, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060790669456, "dur": 2073, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750060790671549, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750060790671741, "dur": 71017, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060790749349, "dur": 1182, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1750060789896178, "dur": 505794, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750060789897413, "dur": 71346, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750060790324138, "dur": 4294, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750060790328435, "dur": 73525, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750060790330169, "dur": 57419, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750060790408901, "dur": 1350, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1750060790408367, "dur": 2252, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1750060789201882, "dur": 1253, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060789203143, "dur": 765, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060789204011, "dur": 68, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1750060789204079, "dur": 357, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060789204917, "dur": 174, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_0713B1E0F0E8C075.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750060789205700, "dur": 119, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_BEF5CA1EC64E588B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750060789206371, "dur": 234, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_E65D5496618FFBC1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750060789207073, "dur": 191, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_E55D0F7C63F01D9E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750060789207657, "dur": 133, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750060789207974, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1750060789208382, "dur": 122, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_EC1F0C20321316E3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750060789208848, "dur": 117, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_EE36537354EA42C8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750060789209318, "dur": 120, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750060789209602, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.ref.dll_D75DF4EA1AFE54F4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750060789209845, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750060789210371, "dur": 106, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750060789210627, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1750060789211947, "dur": 235, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750060789213503, "dur": 113, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750060789214248, "dur": 122, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1750060789216236, "dur": 1735, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750060789218492, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 0, "ts": 1750060789219126, "dur": 152, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 0, "ts": 1750060789219721, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Editor.ConversionSystem.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750060789220080, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1750060789220870, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750060789221193, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750060789222197, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750060789223381, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750060789224290, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1750060789224649, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1750060789225053, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualEffectGraph.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750060789225821, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17571664448659802584.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750060789227450, "dur": 210, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750060789228165, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750060789228538, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1750060789204451, "dur": 24966, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060789229430, "dur": 594639, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060789824071, "dur": 525, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060789824814, "dur": 67, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060789824889, "dur": 61, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060789824984, "dur": 1099, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1750060789205259, "dur": 24300, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789229564, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_A11EFFC223F8866B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750060789229837, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_7B75EE72BE5BAF62.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750060789230012, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_1DB8604BF3C2B15B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750060789230665, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789230796, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750060789230890, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750060789231225, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1750060789231451, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750060789231595, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2781082588993088484.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750060789231698, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789231847, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789232092, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789232327, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789232557, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789232755, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789232995, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789233183, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789233707, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789233898, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789234077, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789234654, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789234838, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789235004, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789235553, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789235737, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789235930, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789236221, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789236395, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789236578, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789237159, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789237468, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789238004, "dur": 1112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750060789239128, "dur": 571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750060789239732, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789239996, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750060789240172, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750060789240501, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789240594, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789241053, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789241360, "dur": 5491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789246852, "dur": 61662, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789308518, "dur": 2052, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750060789310571, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789310631, "dur": 2102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750060789312734, "dur": 619, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789313360, "dur": 1958, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750060789315318, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060789315583, "dur": 3386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750060789318999, "dur": 505174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789205108, "dur": 24333, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789229460, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789229551, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_76ADE6F2C4A0FDCA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750060789229638, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_025D68ED9798F53F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750060789229967, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789230778, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_6717AAFEBC09DAE0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750060789230898, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750060789231389, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1750060789231694, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789231837, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789232029, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789232255, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789232483, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789232664, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789232864, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789233057, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789233274, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789233767, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789233939, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789234124, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789234320, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789234519, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789234778, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789234982, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789235184, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789235375, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789235575, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789235777, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789235993, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789236189, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789236361, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789236547, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789236777, "dur": 389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789237166, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789237469, "dur": 531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789238004, "dur": 662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750060789238667, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789238767, "dur": 739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750060789239560, "dur": 747, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789240310, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789240583, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789241005, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789241352, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750060789241522, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750060789242009, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789242085, "dur": 4735, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789246824, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750060789247086, "dur": 61386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789308476, "dur": 2755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750060789311263, "dur": 2189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750060789313453, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789313548, "dur": 1715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750060789315295, "dur": 2099, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750060789317396, "dur": 764, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789318171, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789318355, "dur": 63391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060789381747, "dur": 442346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789205448, "dur": 24106, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789229645, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_1DC9F96436961A23.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750060789229976, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789230713, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1750060789230889, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1750060789231079, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750060789231433, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750060789231607, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16285879237017002407.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750060789231679, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789232038, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789232335, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789232577, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789232783, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789232966, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789233228, "dur": 646, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\treeview\\TrackGui\\TimelineTrackGUI.cs"}}, {"pid": 12345, "tid": 3, "ts": 1750060789233152, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789233979, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789234179, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789234360, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789234599, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789234793, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789235000, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789235206, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789235424, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789235607, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789235828, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789236003, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789236218, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789236415, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789236600, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789236789, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789237122, "dur": 335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789237479, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789237985, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750060789238226, "dur": 443, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789238673, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750060789239204, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789239342, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750060789239539, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750060789239990, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750060789240200, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750060789240545, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789240598, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789241010, "dur": 340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789241351, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750060789241524, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750060789241940, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750060789242087, "dur": 640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750060789242814, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750060789242941, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750060789243438, "dur": 3398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789246837, "dur": 61628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789308466, "dur": 2712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750060789311179, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789311245, "dur": 2135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750060789313381, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789313648, "dur": 1847, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750060789315496, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789315817, "dur": 2053, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750060789317966, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789318305, "dur": 689, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060789319008, "dur": 505102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060789205236, "dur": 24279, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060789229640, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060789229749, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_FB3B15524EEAB01A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060789229964, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B35C1755F7CD7A14.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060789230780, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060789230931, "dur": 3603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750060789234594, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060789234839, "dur": 2564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750060789237495, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060789237581, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750060789237973, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060789238154, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060789238268, "dur": 589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750060789238857, "dur": 451, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060789239350, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060789239574, "dur": 521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750060789240096, "dur": 439, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060789240578, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060789240717, "dur": 566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750060789241346, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060789241479, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750060789241918, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060789242060, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750060789242554, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060789242675, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060789242845, "dur": 1419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750060789244347, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060789244502, "dur": 1302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750060789245806, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060789245911, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060789246057, "dur": 672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750060789246820, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060789246976, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750060789247300, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750060789247692, "dur": 132696, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750060789381742, "dur": 442315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789205366, "dur": 24199, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789229569, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_05821C8CF4745330.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060789229838, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_E1BAC9240B32956E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060789229964, "dur": 172, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_9BECDD314111B6EB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060789230684, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789230751, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1750060789230886, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1750060789230962, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750060789231200, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750060789231399, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1750060789231683, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789232049, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789232473, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789232691, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789232874, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789233055, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789233237, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789233749, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789233943, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789234137, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789234314, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789234480, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789235137, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789235328, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789235524, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789235702, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789235902, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789236080, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789236270, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789236456, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789236679, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789237041, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789237462, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789237976, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060789238219, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060789238353, "dur": 1304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750060789239657, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789239720, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789239788, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060789239963, "dur": 1122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750060789241145, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060789241266, "dur": 1000, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750060789242314, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060789242418, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750060789242737, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789242801, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789242882, "dur": 3959, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789246842, "dur": 62353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060789309199, "dur": 2546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750060789311780, "dur": 2610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750060789314433, "dur": 2331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750060789316809, "dur": 1866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750060789318718, "dur": 505440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789205154, "dur": 24326, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789229487, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_D44F1D7947A8470C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750060789229566, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789229736, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_CDEA261D3E32C133.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750060789229980, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789230904, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750060789231591, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12900333509587573630.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750060789231692, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789231853, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789232252, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789232430, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789232659, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789232858, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789233076, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789233623, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789233822, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789234002, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789234179, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789234367, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789234557, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789234760, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789234962, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789235181, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789235363, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789235542, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789235730, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789235919, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789236103, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789236305, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789236478, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789236699, "dur": 115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789236814, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789237147, "dur": 330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789237477, "dur": 520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789237998, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750060789238226, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750060789238427, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750060789239002, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789239390, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750060789239677, "dur": 450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750060789240180, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789240581, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750060789240737, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750060789241194, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789241365, "dur": 5464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789246830, "dur": 61640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789308472, "dur": 2981, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750060789311454, "dur": 352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789311811, "dur": 2057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750060789313869, "dur": 1033, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060789314909, "dur": 2069, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750060789317011, "dur": 1562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750060789318610, "dur": 505554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789205140, "dur": 24326, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789229473, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_1BD15D364A91220E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750060789229650, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_42BE61A2F375B465.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750060789229730, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_42BE61A2F375B465.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750060789229854, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_32C818CD7980EFF8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750060789229998, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_136791916CB4C4E9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750060789230866, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1750060789230971, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750060789231226, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1750060789231434, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750060789231692, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789232258, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789232534, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789232762, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789232982, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789233199, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789233706, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789233889, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789234057, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789234465, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789234662, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789234857, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789235070, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789235252, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789235445, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789235635, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789235809, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789236008, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789236233, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789236423, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789236616, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789236853, "dur": 143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789237045, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789237461, "dur": 519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789237981, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750060789238382, "dur": 639, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789239029, "dur": 668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750060789239698, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789239761, "dur": 584, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789240348, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789240604, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789241012, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789241349, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750060789241510, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750060789241884, "dur": 4959, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789246844, "dur": 61728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789308575, "dur": 3362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750060789311968, "dur": 2062, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750060789314062, "dur": 1903, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750060789315966, "dur": 1412, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789317404, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789317575, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789317931, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789318057, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789318302, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060789318781, "dur": 505356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789205255, "dur": 24278, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789229643, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_B6C5AA3F81E3AFD4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750060789229967, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789230900, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750060789231018, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750060789231161, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1750060789231236, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1750060789231394, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1750060789231693, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789231889, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789232104, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789232259, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789232471, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789232677, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789232862, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789233052, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789233224, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789233753, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789233938, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789234112, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789234312, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789234501, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789234729, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789235196, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789235382, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789235575, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789235774, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789236000, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789236203, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789236372, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789236569, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789236784, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789237040, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789237463, "dur": 519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789237983, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750060789238233, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750060789238710, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789238790, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750060789239251, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789239481, "dur": 776, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789240258, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789240579, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750060789240707, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750060789241001, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": 1750060789241400, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789241586, "dur": 123, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789242045, "dur": 63980, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": 1750060789308463, "dur": 2095, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750060789310559, "dur": 424, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060789310991, "dur": 1835, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750060789312864, "dur": 1877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750060789314785, "dur": 2060, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750060789316878, "dur": 1866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750060789318776, "dur": 505348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789205208, "dur": 24287, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789229506, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_1C3753EAD72C002A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750060789229659, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_C5179C2B98A35602.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750060789229983, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_1E5BE8A4F6F01B45.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750060789230130, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_1E5BE8A4F6F01B45.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750060789230777, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_F39DFE4EF8637071.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750060789230902, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1750060789231110, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750060789231210, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1750060789231446, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10142702499866438521.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750060789231584, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750060789231684, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789231850, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789232079, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789232356, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789232574, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789232769, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789232965, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789233157, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789233681, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789233868, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789234224, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789234393, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789234595, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789234840, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789235012, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789235193, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789235370, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789235554, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789235738, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789235954, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789236139, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789236345, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789236522, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789236777, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789237175, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789237466, "dur": 520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789237990, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750060789238211, "dur": 769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750060789238981, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789239274, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750060789239512, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750060789240063, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789240191, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789240584, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789241018, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789241371, "dur": 5490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789246862, "dur": 62367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789309231, "dur": 2150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750060789311382, "dur": 397, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789311784, "dur": 1971, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750060789313756, "dur": 873, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789314635, "dur": 2632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750060789317362, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789317503, "dur": 385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789318042, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789318299, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060789318721, "dur": 505460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789205383, "dur": 24242, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789229625, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_100C028F850EE8CE.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750060789229968, "dur": 196, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_217E1FA7BA4C2F92.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750060789231383, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1750060789231639, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1750060789231690, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789231844, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789232248, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789232498, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789232692, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789232906, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789233170, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789233703, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789233931, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789234108, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789234310, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789234477, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789234692, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789234886, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789235105, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789235309, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789235494, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789235708, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789235899, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789236065, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789236408, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789236598, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789236850, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789237167, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789237470, "dur": 523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789237998, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750060789238243, "dur": 1207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750060789239450, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789239554, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750060789239752, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750060789240182, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789240590, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789241004, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789241349, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750060789241506, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750060789241934, "dur": 4905, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789246839, "dur": 62308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789309150, "dur": 2096, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750060789311247, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789311323, "dur": 2372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750060789313697, "dur": 528, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060789314231, "dur": 2070, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750060789316331, "dur": 2007, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750060789318380, "dur": 505681, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789205294, "dur": 24277, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789229723, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_4B4D0A166E0D20DC.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750060789229837, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_9DD7D897C42E415F.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750060789229970, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_7D559772BEA705DB.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750060789230652, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750060789230735, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789230855, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1750060789231033, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750060789231330, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750060789231436, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750060789231580, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1488387367365330867.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750060789231702, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789231862, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789232218, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789232414, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789232613, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789232818, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789233007, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789233208, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789233732, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789233966, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789234139, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789234311, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789234490, "dur": 382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789234873, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789235055, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789235261, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789235464, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789235651, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789235862, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789236030, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789236236, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789236414, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789236591, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789236813, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789237152, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789237469, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789237995, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750060789238231, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750060789238424, "dur": 1016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750060789239493, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789239797, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750060789239956, "dur": 425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750060789240431, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789240596, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789241003, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789241148, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789241354, "dur": 1324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789242679, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750060789242861, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750060789243291, "dur": 3554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789246845, "dur": 61617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789308470, "dur": 2088, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1750060789310559, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789310618, "dur": 5733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1750060789316387, "dur": 1844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1750060789318232, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789318312, "dur": 63431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060789381745, "dur": 442360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789205346, "dur": 24272, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789229620, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_E6F99946C6EBBC96.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750060789229820, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_2DE11BE0CCB2F9AC.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750060789229949, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_7D33EB796E04AD1F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750060789230061, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_8D701D583C70E786.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750060789230402, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750060789230482, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750060789230565, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750060789230704, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750060789230782, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750060789230859, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750060789231189, "dur": 716, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1750060789231906, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789232145, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789232400, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789232668, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789232981, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789233171, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789233698, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789234174, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789234354, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789234533, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789234754, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789234967, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789235164, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789235404, "dur": 777, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@9b7e7eef789d\\Runtime\\TMP\\FontFeatureCommonGPOS.cs"}}, {"pid": 12345, "tid": 12, "ts": 1750060789235355, "dur": 942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789236298, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789236607, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789237045, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789237460, "dur": 516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789237978, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750060789238253, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750060789238891, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789239153, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750060789239302, "dur": 870, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789240175, "dur": 695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750060789240871, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789240933, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789241040, "dur": 326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789241366, "dur": 5458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789246827, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750060789247031, "dur": 61449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789308484, "dur": 2709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750060789311194, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789311276, "dur": 2497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750060789313773, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789314018, "dur": 1831, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750060789315850, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789315913, "dur": 2099, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750060789318066, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789318309, "dur": 63440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060789381749, "dur": 442331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060789205109, "dur": 24345, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060789229721, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_6E196A46756374CC.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750060789229949, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_25DE2719A06B376F.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750060789230137, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_E3FF4E8988E07430.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750060789230779, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750060789230955, "dur": 3028, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750060789234056, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060789234252, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060789234433, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060789234607, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060789234824, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060789235011, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060789235227, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060789235449, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060789235693, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060789235889, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060789236101, "dur": 376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060789236478, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060789236672, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060789236956, "dur": 108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060789237064, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060789237466, "dur": 506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060789237994, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750060789238230, "dur": 671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750060789238901, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060789239012, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750060789239200, "dur": 502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750060789239703, "dur": 443, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060789240167, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750060789240582, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750060789240735, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750060789241146, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750060789241292, "dur": 1098, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750060789242428, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750060789242531, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750060789243154, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750060789243315, "dur": 963, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750060789244349, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750060789244484, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750060789244864, "dur": 1995, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060789246859, "dur": 61644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060789308506, "dur": 3254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750060789311792, "dur": 2689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750060789314482, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060789314772, "dur": 2241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750060789317014, "dur": 579, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060789317683, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060789317952, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060789318294, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060789318382, "dur": 505685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789205402, "dur": 24199, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789229655, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789229870, "dur": 202, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_9C4CF4B6FE076BF3.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750060789230078, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_3EE9BB20E930FDFD.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750060789230587, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1750060789230903, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1750060789231105, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1750060789231208, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1750060789231435, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1750060789231690, "dur": 379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789232070, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789232331, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789232579, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789232783, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789232983, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789233197, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789233689, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789233882, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789234084, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789234257, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789234442, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789234631, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789234931, "dur": 369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789235300, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789235515, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789235728, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789235917, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789236119, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789236315, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789236500, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789236739, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789236936, "dur": 57, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789237041, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789237482, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789237979, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750060789238223, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750060789238725, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789238842, "dur": 459, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789239304, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789239365, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750060789239547, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750060789239939, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750060789240073, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750060789240476, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789240593, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789241024, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789241382, "dur": 5464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789246846, "dur": 61677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789308528, "dur": 3576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750060789312105, "dur": 644, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789312756, "dur": 1975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750060789314732, "dur": 516, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789315255, "dur": 2128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750060789317432, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789317733, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789317999, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789318296, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060789318621, "dur": 505438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789205474, "dur": 24154, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789229629, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_36DAB0C55B9C80DA.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750060789229872, "dur": 229, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_2272D339B0EB3A67.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750060789230685, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1750060789230812, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1750060789231286, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1750060789231458, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1750060789231681, "dur": 396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789232077, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789232337, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789232880, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789233080, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789233283, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789233801, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789233989, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789234160, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789234468, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789234663, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789234882, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789235098, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789235334, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789235667, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789235860, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789236045, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789236730, "dur": 109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789236839, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789237120, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789237475, "dur": 532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789238008, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750060789238236, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750060789238850, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750060789239040, "dur": 540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750060789239635, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750060789239799, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750060789239948, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750060789240312, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789240608, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789240674, "dur": 336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789241010, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789241359, "dur": 1801, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789243161, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750060789243329, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750060789243735, "dur": 3117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789246852, "dur": 61641, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789308499, "dur": 2171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750060789310671, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789310879, "dur": 2868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750060789313748, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789313807, "dur": 2371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750060789316179, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789316242, "dur": 1891, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750060789318256, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789318361, "dur": 505327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060789823690, "dur": 316, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789205515, "dur": 24038, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789229554, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_49F05BCF866287C3.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750060789229980, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789230757, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_EC1F0C20321316E3.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750060789230873, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1750060789231008, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1750060789231092, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1750060789231455, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17346584914308636752.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750060789231690, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789231842, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789232043, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789232338, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789232589, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789232782, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789232979, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789233175, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789233730, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789233928, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789234106, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789234316, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789234493, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789234696, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789234916, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789235127, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789235320, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789235502, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789235692, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789235892, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789236103, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789236322, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789236516, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789237085, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789237472, "dur": 523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789237997, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750060789238491, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1750060789239033, "dur": 464, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789239550, "dur": 713, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789240263, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789240586, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789241018, "dur": 359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789241378, "dur": 5456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789246835, "dur": 61632, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789308469, "dur": 2757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750060789311227, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789311512, "dur": 4475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750060789315989, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789316051, "dur": 1937, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750060789318159, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060789318472, "dur": 505591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060789830045, "dur": 1177, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 2208, "tid": 119, "ts": 1750060790754988, "dur": 30, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 2208, "tid": 119, "ts": 1750060790760003, "dur": 94, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 2208, "tid": 119, "ts": 1750060790760422, "dur": 42, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 2208, "tid": 119, "ts": 1750060790755062, "dur": 4940, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 2208, "tid": 119, "ts": 1750060790760201, "dur": 219, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 2208, "tid": 119, "ts": 1750060790760500, "dur": 1181, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 2208, "tid": 119, "ts": 1750060790751182, "dur": 10560, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}