{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 2208, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 2208, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 2208, "tid": 44, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 2208, "tid": 44, "ts": 1750060208938212, "dur": 10, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 2208, "tid": 44, "ts": 1750060208938236, "dur": 4, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 2208, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 2208, "tid": 1, "ts": 1750060207330776, "dur": 1735, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 2208, "tid": 1, "ts": 1750060207332515, "dur": 55559, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 2208, "tid": 1, "ts": 1750060207388077, "dur": 48605, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 2208, "tid": 44, "ts": 1750060208938242, "dur": 8, "ph": "X", "name": "", "args": {}}, {"pid": 2208, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207330747, "dur": 16883, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207347631, "dur": 1589897, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207347642, "dur": 30, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207347675, "dur": 195, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207347875, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207347879, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207347902, "dur": 6, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207347910, "dur": 4336, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207352252, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207352255, "dur": 329, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207352588, "dur": 14, "ph": "X", "name": "ProcessMessages 20488", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207352604, "dur": 81, "ph": "X", "name": "ReadAsync 20488", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207352690, "dur": 4, "ph": "X", "name": "ProcessMessages 2208", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207352695, "dur": 59, "ph": "X", "name": "ReadAsync 2208", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207352759, "dur": 1, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207352761, "dur": 42, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207352806, "dur": 27, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207352837, "dur": 35, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207352875, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207352877, "dur": 46, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207352926, "dur": 2, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207352930, "dur": 49, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207352982, "dur": 1, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207352985, "dur": 49, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353037, "dur": 1, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353040, "dur": 29, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353070, "dur": 1, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353072, "dur": 27, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353102, "dur": 29, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353133, "dur": 22, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353158, "dur": 20, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353180, "dur": 21, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353204, "dur": 27, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353235, "dur": 48, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353285, "dur": 34, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353321, "dur": 1, "ph": "X", "name": "ProcessMessages 807", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353323, "dur": 32, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353359, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353363, "dur": 41, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353405, "dur": 1, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353409, "dur": 29, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353439, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353441, "dur": 23, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353467, "dur": 23, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353492, "dur": 30, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353524, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353527, "dur": 37, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353568, "dur": 32, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353604, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353607, "dur": 34, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353642, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353644, "dur": 22, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353668, "dur": 2, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353671, "dur": 25, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353700, "dur": 22, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353725, "dur": 22, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353749, "dur": 20, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353772, "dur": 23, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353796, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353798, "dur": 23, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353824, "dur": 22, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353849, "dur": 19, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353870, "dur": 38, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353912, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353942, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353944, "dur": 29, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207353976, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354015, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354018, "dur": 22, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354044, "dur": 22, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354069, "dur": 22, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354093, "dur": 22, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354118, "dur": 33, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354153, "dur": 24, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354179, "dur": 71, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354253, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354298, "dur": 50, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354353, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354355, "dur": 42, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354399, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354400, "dur": 24, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354427, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354429, "dur": 33, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354464, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354466, "dur": 26, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354496, "dur": 28, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354527, "dur": 23, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354552, "dur": 27, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354582, "dur": 18, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354603, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354627, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354629, "dur": 28, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354659, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354660, "dur": 49, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354713, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354715, "dur": 42, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354759, "dur": 1, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354762, "dur": 22, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354788, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354790, "dur": 35, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354829, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354865, "dur": 1, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354867, "dur": 25, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354895, "dur": 34, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354932, "dur": 1, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354936, "dur": 38, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354977, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207354979, "dur": 24, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355005, "dur": 1, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355007, "dur": 38, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355046, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355048, "dur": 34, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355083, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355085, "dur": 29, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355116, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355118, "dur": 37, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355157, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355160, "dur": 22, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355185, "dur": 23, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355212, "dur": 23, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355238, "dur": 52, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355292, "dur": 2, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355295, "dur": 20, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355318, "dur": 42, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355361, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355363, "dur": 27, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355395, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355397, "dur": 34, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355434, "dur": 23, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355459, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355461, "dur": 29, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355492, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355493, "dur": 34, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355529, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355532, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355571, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355573, "dur": 38, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355613, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355617, "dur": 31, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355650, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355653, "dur": 40, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355695, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355697, "dur": 39, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355738, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355740, "dur": 45, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355788, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355793, "dur": 32, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355826, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355830, "dur": 25, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355856, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355858, "dur": 30, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355892, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355921, "dur": 2, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355924, "dur": 32, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355958, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355961, "dur": 30, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355992, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207355994, "dur": 27, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207356024, "dur": 26, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207356053, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207356055, "dur": 22, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207356080, "dur": 26, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207356108, "dur": 2, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207356113, "dur": 48, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207356164, "dur": 1, "ph": "X", "name": "ProcessMessages 810", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207356166, "dur": 33, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207356203, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207356205, "dur": 22, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207356228, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207356232, "dur": 3425, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207359661, "dur": 1, "ph": "X", "name": "ProcessMessages 118", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207359663, "dur": 1628, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207361304, "dur": 26, "ph": "X", "name": "ProcessMessages 20499", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207361332, "dur": 527, "ph": "X", "name": "ReadAsync 20499", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207361875, "dur": 22, "ph": "X", "name": "ProcessMessages 20525", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207361924, "dur": 555, "ph": "X", "name": "ReadAsync 20525", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207362484, "dur": 4, "ph": "X", "name": "ProcessMessages 4811", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207362555, "dur": 429, "ph": "X", "name": "ReadAsync 4811", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207363185, "dur": 10, "ph": "X", "name": "ProcessMessages 10384", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207363201, "dur": 79, "ph": "X", "name": "ReadAsync 10384", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207363284, "dur": 4, "ph": "X", "name": "ProcessMessages 6940", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207363300, "dur": 319, "ph": "X", "name": "ReadAsync 6940", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207363703, "dur": 1, "ph": "X", "name": "ProcessMessages 995", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207363706, "dur": 1872, "ph": "X", "name": "ReadAsync 995", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207365595, "dur": 4, "ph": "X", "name": "ProcessMessages 2591", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207365612, "dur": 266, "ph": "X", "name": "ReadAsync 2591", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207365880, "dur": 35, "ph": "X", "name": "ProcessMessages 20503", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207365916, "dur": 43, "ph": "X", "name": "ReadAsync 20503", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207365961, "dur": 1, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207365963, "dur": 49, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366015, "dur": 1, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366017, "dur": 35, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366055, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366057, "dur": 31, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366090, "dur": 2, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366093, "dur": 34, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366128, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366130, "dur": 47, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366179, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366182, "dur": 42, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366226, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366228, "dur": 38, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366270, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366272, "dur": 33, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366307, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366308, "dur": 37, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366347, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366349, "dur": 37, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366389, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366391, "dur": 40, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366432, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366435, "dur": 38, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366475, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366477, "dur": 39, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366518, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366520, "dur": 38, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366560, "dur": 2, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366563, "dur": 51, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366620, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366622, "dur": 35, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366659, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366661, "dur": 33, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366696, "dur": 28, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366727, "dur": 36, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366765, "dur": 19, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366787, "dur": 27, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366815, "dur": 27, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366845, "dur": 25, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366873, "dur": 23, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366898, "dur": 43, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366943, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366945, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366976, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207366978, "dur": 34, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367013, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367015, "dur": 22, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367041, "dur": 77, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367120, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367159, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367162, "dur": 32, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367195, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367197, "dur": 25, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367223, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367225, "dur": 64, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367291, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367292, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367333, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367335, "dur": 31, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367369, "dur": 26, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367397, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367399, "dur": 70, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367472, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367501, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367503, "dur": 32, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367537, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367539, "dur": 73, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367616, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367662, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367665, "dur": 21, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367687, "dur": 1, "ph": "X", "name": "ProcessMessages 223", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367689, "dur": 23, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367713, "dur": 1, "ph": "X", "name": "ProcessMessages 119", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367715, "dur": 21, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367740, "dur": 153, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367897, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367945, "dur": 2, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367948, "dur": 32, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367981, "dur": 2, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207367984, "dur": 68, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207368056, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207368083, "dur": 23, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207368109, "dur": 35, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207368147, "dur": 73, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207368223, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207368258, "dur": 23, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207368284, "dur": 26, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207368313, "dur": 21, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207368337, "dur": 63, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207368402, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207368430, "dur": 22, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207368455, "dur": 29, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207368487, "dur": 11, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207368500, "dur": 76, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207368580, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207368606, "dur": 22, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207368631, "dur": 21, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207368654, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207368656, "dur": 83, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207368741, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207368744, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207368770, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207368772, "dur": 85, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207368861, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207368863, "dur": 44, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207368912, "dur": 21, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207368936, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207368960, "dur": 2, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207368965, "dur": 2316, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207371288, "dur": 2, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207371293, "dur": 321, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207371617, "dur": 25, "ph": "X", "name": "ProcessMessages 20376", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207371643, "dur": 49, "ph": "X", "name": "ReadAsync 20376", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207371695, "dur": 4, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207371702, "dur": 51, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207371756, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207371758, "dur": 21, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207371783, "dur": 127, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207371915, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207371956, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207371958, "dur": 43, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372003, "dur": 1, "ph": "X", "name": "ProcessMessages 213", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372006, "dur": 33, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372041, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372043, "dur": 39, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372084, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372085, "dur": 37, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372127, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372130, "dur": 49, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372180, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372183, "dur": 37, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372222, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372224, "dur": 30, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372257, "dur": 1, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372260, "dur": 31, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372295, "dur": 29, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372326, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372328, "dur": 117, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372450, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372472, "dur": 44, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372519, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372553, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372556, "dur": 37, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372596, "dur": 46, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372647, "dur": 31, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372680, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372683, "dur": 111, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372800, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372803, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372858, "dur": 1, "ph": "X", "name": "ProcessMessages 793", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372861, "dur": 36, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372900, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372904, "dur": 59, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207372967, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373008, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373012, "dur": 35, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373049, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373052, "dur": 83, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373139, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373198, "dur": 2, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373202, "dur": 22, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373226, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373229, "dur": 66, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373299, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373321, "dur": 32, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373356, "dur": 31, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373389, "dur": 26, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373417, "dur": 15, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373434, "dur": 68, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373504, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373537, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373539, "dur": 39, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373581, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373583, "dur": 22, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373607, "dur": 1, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373609, "dur": 69, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373680, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373716, "dur": 30, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373749, "dur": 25, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373776, "dur": 60, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373840, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373879, "dur": 27, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373909, "dur": 45, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373956, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373987, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207373989, "dur": 60, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207374051, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207374075, "dur": 33, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207374111, "dur": 23, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207374136, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207374138, "dur": 103, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207374244, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207374273, "dur": 29, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207374304, "dur": 61, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207374368, "dur": 56, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207374427, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207374458, "dur": 25, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207374485, "dur": 11, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207374499, "dur": 85, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207374586, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207374616, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207374618, "dur": 26, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207374646, "dur": 32, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207374681, "dur": 78, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207374761, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207374786, "dur": 22, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207374810, "dur": 23, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207374836, "dur": 68, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207374908, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207374939, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207374941, "dur": 23, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207374967, "dur": 19, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207374989, "dur": 60, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207375051, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207375076, "dur": 38, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207375117, "dur": 23, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207375142, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207375144, "dur": 97, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207375245, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207375270, "dur": 21, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207375293, "dur": 19, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207375314, "dur": 20, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207375337, "dur": 21, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207375361, "dur": 20, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207375383, "dur": 11, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207375395, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207375397, "dur": 127, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207375527, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207375551, "dur": 30, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207375583, "dur": 22, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207375608, "dur": 21, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207375631, "dur": 21, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207375654, "dur": 119, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207375775, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207375797, "dur": 30, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207375830, "dur": 23, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207375855, "dur": 22, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207375879, "dur": 25, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207375907, "dur": 111, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207376020, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207376053, "dur": 21, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207376076, "dur": 18, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207376096, "dur": 28, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207376126, "dur": 78, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207376207, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207376209, "dur": 158, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207376371, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207376374, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207376450, "dur": 2, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207376454, "dur": 33, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207376489, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207376494, "dur": 93, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207376592, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207376595, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207376634, "dur": 2, "ph": "X", "name": "ProcessMessages 1201", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207376639, "dur": 70, "ph": "X", "name": "ReadAsync 1201", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207376713, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207376736, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207376738, "dur": 22, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207376762, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207376764, "dur": 21, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207376788, "dur": 62, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207376852, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207376917, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207376919, "dur": 32, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207376952, "dur": 1, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207376954, "dur": 37, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207376994, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207377018, "dur": 20, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207377041, "dur": 29, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207377072, "dur": 27, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207377101, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207377103, "dur": 67, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207377172, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207377209, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207377211, "dur": 38, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207377253, "dur": 2, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207377257, "dur": 27, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207377287, "dur": 49, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207377340, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207377373, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207377375, "dur": 32, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207377409, "dur": 22, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207377433, "dur": 61, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207377497, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207380750, "dur": 6, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207380758, "dur": 432, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207381194, "dur": 62, "ph": "X", "name": "ProcessMessages 15046", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207381258, "dur": 1896, "ph": "X", "name": "ReadAsync 15046", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207383158, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207383161, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207383200, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207383203, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207383235, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207383237, "dur": 717, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207383958, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207383992, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207383995, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207384031, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207384033, "dur": 454, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207384492, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207384541, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207384543, "dur": 4787, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207389337, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207389340, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207389393, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207389396, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207389430, "dur": 315, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207389750, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207389787, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207389790, "dur": 364, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207390158, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207390195, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207390197, "dur": 266, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207390468, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207390517, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207390520, "dur": 29, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207390552, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207390554, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207390597, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207390624, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207390626, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207390658, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207390685, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207390732, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207390757, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207390793, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207390824, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207390826, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207390864, "dur": 324, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207391192, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207391218, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207391264, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207391292, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207391343, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207391372, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207391424, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207391471, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207391473, "dur": 52, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207391529, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207391556, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207391558, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207391582, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207391583, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207391615, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207391644, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207391646, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207391679, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207391681, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207391712, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207391734, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207391758, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207391813, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207391840, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207391913, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207391950, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207391952, "dur": 202, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207392158, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207392159, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207392185, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207392187, "dur": 38, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207392228, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207392266, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207392268, "dur": 129, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207392401, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207392427, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207392480, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207392506, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207392549, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207392574, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207392597, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207392629, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207392631, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207392667, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207392668, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207392699, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207392725, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207392757, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207392784, "dur": 75, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207392862, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207392865, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207392897, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207392901, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207392950, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207392982, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207393041, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207393068, "dur": 163, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207393235, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207393238, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207393276, "dur": 29, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207393308, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207393343, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207393345, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207393372, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207393396, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207393398, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207393436, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207393438, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207393478, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207393501, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207393540, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207393565, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207393616, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207393649, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207393651, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207393676, "dur": 68, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207393746, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207393772, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207393795, "dur": 106, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207393904, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207393933, "dur": 187, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207394125, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207394161, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207394205, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207394209, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207394244, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207394246, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207394279, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207394281, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207394307, "dur": 119, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207394429, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207394456, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207394483, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207394502, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207394539, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207394541, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207394573, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207394575, "dur": 163, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207394742, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207394768, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207394770, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207394794, "dur": 488, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207395287, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207395326, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207395328, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207395362, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207395364, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207395393, "dur": 23, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207395419, "dur": 114, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207395535, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207395538, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207395577, "dur": 3, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207395581, "dur": 71, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207395657, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207395691, "dur": 103, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207395798, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207395800, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207395832, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207395861, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207395946, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207395976, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207396026, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207396054, "dur": 205, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207396265, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207396305, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207396307, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207396331, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207396359, "dur": 146, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207396507, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207396509, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207396533, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207396561, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207396585, "dur": 256, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207396844, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207396877, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207396911, "dur": 99, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207397013, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207397038, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207397040, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207397067, "dur": 289, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207397360, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207397397, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207397399, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207397462, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207397488, "dur": 117, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207397608, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207397639, "dur": 231, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207397872, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207397903, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207397905, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207397936, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207398023, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207398051, "dur": 2516, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207400574, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207400577, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207400614, "dur": 2, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207400616, "dur": 41, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207400661, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207400693, "dur": 308, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207401004, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207401006, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207401046, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207401050, "dur": 67074, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207468137, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207468143, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207468175, "dur": 27, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207468204, "dur": 4630, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207472842, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207472846, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207472876, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207472879, "dur": 105, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207472988, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207472991, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207473022, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207473090, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207473108, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207473153, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207473186, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207473189, "dur": 49, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207473241, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207473243, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207473275, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207473277, "dur": 344, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207473625, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207473655, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207473657, "dur": 565, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207474225, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207474227, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207474264, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207474266, "dur": 707, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207474978, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207475013, "dur": 601, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207475619, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207475655, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207475657, "dur": 211, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207475871, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207475873, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207475904, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207475943, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207475945, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207475973, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207476005, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207476008, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207476077, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207476108, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207476111, "dur": 664, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207476779, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207476814, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207476816, "dur": 399, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207477219, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207477255, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207477256, "dur": 723, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207477983, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207478023, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207478025, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207478052, "dur": 165, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207478222, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207478251, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207478321, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207478347, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207478373, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207478408, "dur": 89, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207478501, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207478524, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207478574, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207478576, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207478611, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207478614, "dur": 249, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207478869, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207478903, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207478905, "dur": 344, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207479253, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207479255, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207479291, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207479293, "dur": 246, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207479544, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207479592, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207479595, "dur": 873, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207480471, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207480474, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207480510, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207480513, "dur": 189, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207480705, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207480707, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207480738, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207480740, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207480786, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207480788, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207480819, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207480822, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207480849, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207480883, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207480924, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207480961, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481006, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481008, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481048, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481050, "dur": 27, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481081, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481083, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481109, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481155, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481157, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481188, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481190, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481224, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481227, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481252, "dur": 12, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481267, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481293, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481294, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481320, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481360, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481395, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481398, "dur": 30, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481431, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481433, "dur": 25, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481460, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481462, "dur": 35, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481500, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481503, "dur": 28, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481533, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481536, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481575, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481578, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481607, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481610, "dur": 30, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481642, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481644, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481678, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481680, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481713, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481715, "dur": 27, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481744, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481746, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481787, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481789, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481817, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481819, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481847, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481869, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481894, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481896, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481962, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207481987, "dur": 82, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207482072, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207482109, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207482110, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207482158, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207482160, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207482197, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207482199, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207482238, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207482266, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207482268, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207482295, "dur": 462, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207482761, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207482817, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207482820, "dur": 18, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207482841, "dur": 77, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207482921, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207482940, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207482942, "dur": 231178, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207714134, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207714141, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207714231, "dur": 33, "ph": "X", "name": "ProcessMessages 8971", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060207714266, "dur": 579694, "ph": "X", "name": "ReadAsync 8971", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060208293971, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060208293979, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060208294073, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060208294079, "dur": 91902, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060208385993, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060208386000, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060208386081, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060208386096, "dur": 70, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060208386171, "dur": 31, "ph": "X", "name": "ProcessMessages 231", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060208386204, "dur": 4882, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060208391090, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060208391092, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060208391148, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060208391152, "dur": 1244, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060208392401, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060208392442, "dur": 21, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060208392465, "dur": 536895, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060208929380, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060208929387, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060208929478, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060208929484, "dur": 494, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060208929984, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060208929987, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060208930074, "dur": 36, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060208930113, "dur": 714, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060208930831, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060208930833, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060208930870, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 2208, "tid": 25769803776, "ts": 1750060208930872, "dur": 6644, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 2208, "tid": 44, "ts": 1750060208938253, "dur": 1694, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 2208, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 2208, "tid": 21474836480, "ts": 1750060207330716, "dur": 105987, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 2208, "tid": 21474836480, "ts": 1750060207436704, "dur": 2, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 2208, "tid": 21474836480, "ts": 1750060207436706, "dur": 71, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 2208, "tid": 44, "ts": 1750060208939949, "dur": 8, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 2208, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 2208, "tid": 17179869184, "ts": 1750060207327125, "dur": 1610459, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 2208, "tid": 17179869184, "ts": 1750060207327239, "dur": 3088, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 2208, "tid": 17179869184, "ts": 1750060208937591, "dur": 75, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 2208, "tid": 17179869184, "ts": 1750060208937608, "dur": 23, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 2208, "tid": 17179869184, "ts": 1750060208937668, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 2208, "tid": 44, "ts": 1750060208939959, "dur": 12, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1750060207347001, "dur": 1472, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060207348485, "dur": 936, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060207349575, "dur": 95, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1750060207349670, "dur": 552, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060207351611, "dur": 342, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_214806B231DDE102.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750060207357230, "dur": 2205, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750060207360708, "dur": 382, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750060207361387, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Editor.ref.dll_0F50152946DB09D0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750060207362092, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750060207362635, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Updater.ref.dll_34B8D5B7DED6E76D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750060207364147, "dur": 1153, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750060207367054, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1750060207370773, "dur": 286, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750060207350241, "dur": 27472, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060207377728, "dur": 1551707, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060208929436, "dur": 468, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060208929944, "dur": 55, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060208930139, "dur": 51, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060208930210, "dur": 940, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1750060207350699, "dur": 27066, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207377769, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_1BD15D364A91220E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750060207378193, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207378937, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750060207379059, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1750060207379322, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207379504, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207379644, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207379766, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9703144790800738880.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750060207379840, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207380031, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207380382, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207380711, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207381219, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207381505, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207381725, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207381963, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207382492, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207382731, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207382945, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207383152, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207383402, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207383879, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207384098, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207384306, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207384529, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207384766, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207385008, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207385224, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207385766, "dur": 1275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207387041, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207387245, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207388301, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207389051, "dur": 589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207389643, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750060207389895, "dur": 634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750060207390530, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207390757, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750060207390963, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207391068, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750060207391627, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PerformanceTesting.ref.dll_F99DC928B1D72E5E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750060207391812, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750060207391982, "dur": 690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750060207392673, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207392766, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207392858, "dur": 733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207393591, "dur": 383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207393986, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750060207394174, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750060207394724, "dur": 4603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207399328, "dur": 70422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207469762, "dur": 2551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750060207472315, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207472571, "dur": 2362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750060207474935, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207475037, "dur": 2309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750060207477396, "dur": 2283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750060207479680, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060207479877, "dur": 2402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750060207482347, "dur": 1447083, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207350625, "dur": 27116, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207377762, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207378202, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_136791916CB4C4E9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750060207378709, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750060207378863, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1750060207379083, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750060207379267, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750060207379400, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750060207379795, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207380068, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207380397, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207380742, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207381136, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207381404, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207381631, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207381874, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207382083, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207383143, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207383410, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207383658, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207383990, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207384222, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207384461, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207384683, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207384950, "dur": 396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207385346, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207385550, "dur": 979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207386530, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207387409, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207388512, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207388973, "dur": 664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207389638, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750060207389833, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207389917, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750060207390607, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207390677, "dur": 572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750060207391250, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207391340, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207391582, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207391753, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750060207391961, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750060207392595, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207392652, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207392859, "dur": 727, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207393640, "dur": 357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207393998, "dur": 5303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207399303, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750060207399566, "dur": 70197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207469764, "dur": 2376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750060207472142, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207472260, "dur": 2954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750060207475215, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207475291, "dur": 2469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750060207477761, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207477975, "dur": 2412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750060207480481, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207481155, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207481363, "dur": 957, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060207482335, "dur": 1447107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207350682, "dur": 27072, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207377769, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207378066, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_88EB000108787B7B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750060207378206, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207378999, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1750060207379130, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1750060207379380, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750060207379809, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207380004, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207380334, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207380611, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207380817, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207381030, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207381263, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207381502, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207381720, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207381984, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207382535, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207382762, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207382970, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207383162, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207383765, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207384100, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207384300, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207384521, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207384732, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207384969, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207385193, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207385386, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207386230, "dur": 973, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207387203, "dur": 1338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207388759, "dur": 87, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207388984, "dur": 856, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207389841, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750060207390218, "dur": 681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750060207390900, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207391019, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207391077, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207391360, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750060207391561, "dur": 634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750060207392196, "dur": 527, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207392754, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207392864, "dur": 728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207393592, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207393989, "dur": 5332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207399321, "dur": 70405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207469741, "dur": 2399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750060207472142, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207472223, "dur": 2762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750060207475033, "dur": 2408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750060207477442, "dur": 2892, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207480346, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207480447, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207480578, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207480811, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207481008, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207481230, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060207481542, "dur": 1447886, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207350742, "dur": 27036, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207377783, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_D44F1D7947A8470C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060207378204, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_1DB8604BF3C2B15B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060207378814, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1750060207378897, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750060207379061, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750060207379244, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750060207379441, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1750060207379557, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207379736, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207380001, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207380428, "dur": 972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207381401, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207381625, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207381868, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207382079, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207382749, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207382972, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207383193, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207383430, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207383710, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207383975, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207384201, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207384425, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207384653, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207384914, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207385122, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207385334, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207385550, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207385794, "dur": 972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207386766, "dur": 1271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207388037, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207389179, "dur": 470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207389652, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060207389881, "dur": 1142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750060207391024, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207391218, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207391367, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060207391621, "dur": 589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750060207392211, "dur": 494, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207392762, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207392865, "dur": 758, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207393623, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207393996, "dur": 5308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207399307, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060207399540, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207399597, "dur": 70175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207469774, "dur": 2705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750060207472480, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207472567, "dur": 2727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750060207475295, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207475496, "dur": 2377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750060207477875, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207477979, "dur": 2839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750060207480905, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750060207480971, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207481086, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207481369, "dur": 963, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060207482333, "dur": 1447090, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060207350792, "dur": 27004, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060207377805, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_1C3753EAD72C002A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060207378025, "dur": 222, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_94280B12CFD84F1A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060207378253, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_9CC28255281D0D3B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060207378838, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060207379032, "dur": 4280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750060207383394, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060207383894, "dur": 4749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750060207388735, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060207388844, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060207389153, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750060207389647, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060207390147, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060207390256, "dur": 883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750060207391139, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060207391357, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060207391573, "dur": 830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750060207392404, "dur": 397, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060207392853, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060207393030, "dur": 844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750060207393973, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060207394152, "dur": 543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750060207394785, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060207394966, "dur": 690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750060207395656, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060207395773, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060207395919, "dur": 1313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750060207397233, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060207397324, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060207397440, "dur": 982, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750060207398491, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060207398606, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750060207399330, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060207399471, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750060207399758, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750060207400067, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750060207400451, "dur": 313429, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750060207715258, "dur": 575025, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750060207715251, "dur": 576846, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750060208293128, "dur": 163, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060208293547, "dur": 91737, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750060208390353, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1750060208390346, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1750060208390482, "dur": 1328, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1750060208391820, "dur": 537628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207350819, "dur": 26994, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207378022, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_FB3B15524EEAB01A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750060207378206, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_7D33EB796E04AD1F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750060207378268, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_CF814559B1575A99.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750060207378970, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750060207379395, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1750060207379471, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750060207379678, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750060207379827, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207380476, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207381342, "dur": 500, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@fe5368074162\\Editor\\2D\\ShapeEditor\\Selection\\RectSelector.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750060207381843, "dur": 650, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@fe5368074162\\Editor\\2D\\ShapeEditor\\Selection\\PointRectSelector.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750060207381087, "dur": 1406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207382493, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207382702, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207382941, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207383149, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207383410, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207383709, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207384036, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207384266, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207384495, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207384714, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207384958, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207385163, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207385387, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207386221, "dur": 956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207387178, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207387390, "dur": 1151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207388541, "dur": 125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207388748, "dur": 63, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207388841, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207388982, "dur": 652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207389640, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750060207389823, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207389898, "dur": 716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750060207390614, "dur": 405, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207391075, "dur": 388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207391464, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207391688, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750060207391944, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207392023, "dur": 734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750060207392871, "dur": 719, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207393590, "dur": 388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207393979, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750060207394201, "dur": 506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750060207394779, "dur": 4550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207399330, "dur": 70406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207469743, "dur": 2760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750060207472561, "dur": 2371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750060207474934, "dur": 409, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207475349, "dur": 2344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750060207477738, "dur": 2409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750060207480449, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207480737, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207481035, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207481110, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207481294, "dur": 910, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060207482225, "dur": 1447218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207350853, "dur": 26987, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207378202, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207379386, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1750060207379505, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207379641, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750060207379801, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207380391, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207380678, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207381265, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207381501, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207381773, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207381957, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207382504, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207382700, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207382927, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207383128, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207383339, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207383581, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207383904, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207384120, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207384335, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207384541, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207384759, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207385414, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207385620, "dur": 1150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207386771, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207387530, "dur": 995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207388525, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207389073, "dur": 752, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207389826, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750060207390059, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207390237, "dur": 606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750060207390844, "dur": 489, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207391343, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207391645, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207391714, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750060207391980, "dur": 615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750060207392646, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207392855, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750060207393064, "dur": 474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750060207393652, "dur": 335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207393987, "dur": 5321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207399309, "dur": 36892, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207437133, "dur": 109, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/6000.0.50f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 7, "ts": 1750060207437242, "dur": 719, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/6000.0.50f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 7, "ts": 1750060207436202, "dur": 1797, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207437999, "dur": 34592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207472593, "dur": 2363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750060207474958, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207475025, "dur": 2312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750060207477339, "dur": 446, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207477791, "dur": 2319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750060207480152, "dur": 1954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750060207482106, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060207482190, "dur": 1447260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207350897, "dur": 26965, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207378151, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_9C4CF4B6FE076BF3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750060207378236, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_C9BF491CE9429A6C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750060207379191, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1750060207379366, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1750060207379798, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207380280, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207380977, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207381595, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207381799, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207382511, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207382753, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207382959, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207383199, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207383612, "dur": 724, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@6e225a61188c\\Editor\\LookDev\\ComparisonGizmoController.cs"}}, {"pid": 12345, "tid": 8, "ts": 1750060207383409, "dur": 985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207384395, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207384625, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207384888, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207385160, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207385695, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207386892, "dur": 378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207387271, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207388312, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207389158, "dur": 487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207389650, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750060207389840, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207389930, "dur": 603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750060207390534, "dur": 423, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207390967, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207391022, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750060207391253, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207391317, "dur": 642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750060207391959, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207392062, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207392339, "dur": 516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207392857, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750060207393073, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750060207393596, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207393980, "dur": 1271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207395253, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750060207395442, "dur": 744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750060207396187, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207396289, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750060207396475, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750060207396897, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750060207397017, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750060207397308, "dur": 2017, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207399325, "dur": 70404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207469734, "dur": 2623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750060207472401, "dur": 4087, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750060207476489, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207476632, "dur": 2205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750060207478838, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060207478941, "dur": 2653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750060207481669, "dur": 1447777, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207350936, "dur": 26941, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207377947, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_1DC9F96436961A23.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750060207378207, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_8D701D583C70E786.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750060207378771, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750060207378948, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750060207379194, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1750060207379817, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207380029, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207380644, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207381061, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207381313, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207381549, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207381751, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207381989, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207382559, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207382792, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207382992, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207383271, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207383487, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207384033, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207384256, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207384523, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207384898, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207385332, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207385525, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207385708, "dur": 1873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207387582, "dur": 1123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207388849, "dur": 61, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207388967, "dur": 665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207389634, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750060207389827, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207389891, "dur": 892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750060207390880, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750060207391124, "dur": 1686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750060207392810, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207392946, "dur": 679, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207393625, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207393974, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750060207394171, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750060207394643, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207394731, "dur": 4591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207399323, "dur": 70400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207469725, "dur": 2429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750060207472217, "dur": 2656, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750060207474875, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207475029, "dur": 2287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750060207477317, "dur": 444, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207477769, "dur": 2248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750060207480017, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060207480109, "dur": 2055, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750060207482218, "dur": 1447219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060207350966, "dur": 27152, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060207378155, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_1DD292255763438B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750060207378261, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B739A7DF6FD203B7.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750060207378992, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1750060207379221, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1750060207379399, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1750060207379783, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060207380043, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060207380520, "dur": 911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060207381432, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060207381655, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060207382118, "dur": 550, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@59eb6f11d242\\Editor\\BurstInspectorGUI.cs"}}, {"pid": 12345, "tid": 10, "ts": 1750060207382727, "dur": 514, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@59eb6f11d242\\Editor\\BurstDisassembler.Core.Wasm.info.cs"}}, {"pid": 12345, "tid": 10, "ts": 1750060207381895, "dur": 1431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060207383326, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060207383800, "dur": 551, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Flow\\Plugin\\Changelogs\\Changelog_1_4_7.cs"}}, {"pid": 12345, "tid": 10, "ts": 1750060207383574, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060207384415, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060207385128, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060207385376, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060207386171, "dur": 1197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060207387369, "dur": 1047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060207388417, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060207389017, "dur": 625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060207389646, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750060207389835, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060207390012, "dur": 616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750060207390628, "dur": 1709, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060207392345, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ConversionSystem.ref.dll_D58562F0DF82BCBA.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750060207392451, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750060207392649, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750060207393190, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750060207393320, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750060207393704, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750060207393840, "dur": 1144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750060207394985, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060207395095, "dur": 4239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060207399335, "dur": 70410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060207469749, "dur": 2862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750060207472613, "dur": 414, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060207473039, "dur": 4394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750060207477435, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060207477636, "dur": 2312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750060207479949, "dur": 614, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060207480615, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060207480895, "dur": 696, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060207481596, "dur": 1447836, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207351001, "dur": 26912, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207377923, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_5D5EF2E9E0015A01.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750060207378191, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_7D559772BEA705DB.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750060207378713, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1750060207378806, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1750060207379066, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750060207379508, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207379809, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207380129, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207380385, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207381490, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207381839, "dur": 820, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@e07c4c789cd8\\Editor\\GraphView\\Elements\\VFXOperatorUI.cs"}}, {"pid": 12345, "tid": 11, "ts": 1750060207381708, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207382715, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207382925, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207383143, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207383710, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207383988, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207384187, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207384412, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207384623, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207384881, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207385087, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207385365, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207385948, "dur": 1461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207387410, "dur": 1714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207389124, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207389663, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750060207389848, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207390073, "dur": 742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750060207390862, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750060207390998, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207391520, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207391754, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207392003, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207392259, "dur": 598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207392858, "dur": 730, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207393588, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207393705, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207393987, "dur": 2306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207396295, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750060207396442, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750060207396790, "dur": 2541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207399331, "dur": 70429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207469762, "dur": 3771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1750060207473535, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207473628, "dur": 6625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1750060207480254, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207480833, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207481234, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060207481520, "dur": 908834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060208390383, "dur": 538286, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750060208390356, "dur": 538315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1750060208928692, "dur": 673, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750060207351041, "dur": 26888, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207378017, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_CDEA261D3E32C133.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750060207378197, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207378677, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750060207378959, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1750060207379343, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750060207379535, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207379785, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207380055, "dur": 369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207380424, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207380685, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207381145, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207381436, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207381656, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207381910, "dur": 606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207382517, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207382836, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207383048, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207383273, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207383504, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207383746, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207384030, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207384254, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207384487, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207384687, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207384897, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207385105, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207385328, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207385541, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207385746, "dur": 1335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207387082, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207387277, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207388362, "dur": 783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207389145, "dur": 508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207389655, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750060207389877, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750060207390147, "dur": 622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750060207390778, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207390884, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207391072, "dur": 520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207391592, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207391668, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750060207391894, "dur": 707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750060207392681, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207392867, "dur": 718, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207393615, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207393994, "dur": 5316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207399311, "dur": 38693, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207438005, "dur": 32747, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207470754, "dur": 4765, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750060207475520, "dur": 661, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060207476191, "dur": 2409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750060207478658, "dur": 2813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750060207481536, "dur": 1447890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207351080, "dur": 26871, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207377962, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_4B4D0A166E0D20DC.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750060207378218, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_E3FF4E8988E07430.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750060207378835, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750060207379008, "dur": 3496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750060207382661, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207382866, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207383067, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207383270, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207383517, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207383796, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207384049, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207384271, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207384503, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207384706, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207384938, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207385125, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207385348, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207385555, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207386578, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207387245, "dur": 1474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207388749, "dur": 75, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207388850, "dur": 63, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207388980, "dur": 649, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207389631, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750060207389866, "dur": 1663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750060207391529, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207391668, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750060207391863, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207392051, "dur": 1506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750060207393557, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207393702, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750060207393876, "dur": 1271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750060207395247, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750060207395359, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750060207395708, "dur": 3630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207399339, "dur": 70416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207469757, "dur": 2742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750060207472501, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207472651, "dur": 2384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750060207475036, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207475353, "dur": 2745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750060207478099, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207478274, "dur": 2651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750060207480926, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207481175, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1750060207481274, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060207481692, "dur": 1447729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207351124, "dur": 26854, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207378056, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_69A7891FFD914456.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750060207378213, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_3EE9BB20E930FDFD.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750060207378807, "dur": 187, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1750060207379201, "dur": 206, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1750060207379536, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207379718, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207379795, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207380346, "dur": 391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207381350, "dur": 527, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Plugins\\InputForUI\\InputSystemProvider.cs"}}, {"pid": 12345, "tid": 14, "ts": 1750060207380738, "dur": 1183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207381921, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207382507, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207382788, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207382983, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207383192, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207383396, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207383680, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207384013, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207384223, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207384458, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207384827, "dur": 686, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Analytics\\NodeUsageAnalytics.cs"}}, {"pid": 12345, "tid": 14, "ts": 1750060207384671, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207385546, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207385755, "dur": 1486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207387241, "dur": 1615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207388856, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207388980, "dur": 647, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207389629, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750060207389810, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207389874, "dur": 869, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750060207390744, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207391044, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750060207391342, "dur": 560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750060207391902, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207392122, "dur": 730, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207392864, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750060207393062, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750060207393470, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207393586, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 14, "ts": 1750060207394032, "dur": 452, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207394489, "dur": 138, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207394962, "dur": 72549, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 14, "ts": 1750060207469722, "dur": 2434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750060207472157, "dur": 2213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207474383, "dur": 2363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750060207476747, "dur": 675, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207477429, "dur": 2343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750060207479773, "dur": 381, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060207480161, "dur": 2127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750060207482350, "dur": 1447095, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207351163, "dur": 26864, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207378193, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207378854, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1750060207378994, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1750060207379240, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1750060207379411, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1750060207379585, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207379807, "dur": 777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207380584, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207381026, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207381281, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207381550, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207381763, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207381992, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207382563, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207382852, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207383054, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207383356, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207383684, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207383940, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207384148, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207384811, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207385197, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207385402, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207385747, "dur": 1405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207387153, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207387366, "dur": 1034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207388401, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207389080, "dur": 571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207389660, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750060207389886, "dur": 1834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750060207391720, "dur": 400, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207392173, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750060207392470, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750060207392940, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207393033, "dur": 583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207393616, "dur": 360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207393977, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750060207394154, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750060207394617, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207394804, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750060207394941, "dur": 640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750060207395582, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207395705, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750060207395910, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750060207396300, "dur": 3040, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207399341, "dur": 73196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207472541, "dur": 2459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750060207475001, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207475344, "dur": 2430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750060207477775, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207477917, "dur": 2452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750060207480370, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207480486, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207481034, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207481291, "dur": 889, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060207482181, "dur": 1447270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207351193, "dur": 26843, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207378199, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_1E5BE8A4F6F01B45.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750060207378913, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750060207379144, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750060207379249, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1750060207379523, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207379815, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207380025, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207380550, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207380794, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207381006, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207381227, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207381453, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207381658, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207381860, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207382058, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207382621, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207382823, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207383033, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207383282, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207383569, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207383927, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207384153, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207384442, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207384684, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207384902, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207385102, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207385335, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207385555, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207386495, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207387382, "dur": 993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207388375, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207389212, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207389663, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750060207389848, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207389919, "dur": 1384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1750060207391303, "dur": 689, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207392079, "dur": 661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207392740, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207392864, "dur": 725, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207393589, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207393981, "dur": 1795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207395778, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750060207395976, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1750060207396462, "dur": 2869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207399332, "dur": 70409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207469743, "dur": 2686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750060207472430, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207472508, "dur": 2819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750060207475328, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207475390, "dur": 2357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750060207477748, "dur": 1213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207478967, "dur": 2473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750060207481487, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060207481563, "dur": 1447856, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060208935345, "dur": 1207, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 2208, "tid": 44, "ts": 1750060208940016, "dur": 17, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 2208, "tid": 44, "ts": 1750060208940062, "dur": 13147, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 2208, "tid": 44, "ts": 1750060208938224, "dur": 15035, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}