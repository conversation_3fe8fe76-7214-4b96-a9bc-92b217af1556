{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 2208, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 2208, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 2208, "tid": 186, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 2208, "tid": 186, "ts": 1750061567521189, "dur": 13, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 2208, "tid": 186, "ts": 1750061567521220, "dur": 8, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 2208, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 2208, "tid": 1, "ts": 1750061565935165, "dur": 1753, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 2208, "tid": 1, "ts": 1750061565936921, "dur": 29450, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 2208, "tid": 1, "ts": 1750061565966373, "dur": 33605, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 2208, "tid": 186, "ts": 1750061567521232, "dur": 16, "ph": "X", "name": "", "args": {}}, {"pid": 2208, "tid": 176093659136, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565935132, "dur": 16677, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565951810, "dur": 1568718, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565951821, "dur": 38, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565951863, "dur": 144, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565952011, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565952014, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565952066, "dur": 6, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565952076, "dur": 2736, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565954817, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565954819, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565954873, "dur": 3, "ph": "X", "name": "ProcessMessages 964", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565954878, "dur": 44, "ph": "X", "name": "ReadAsync 964", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565954924, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565954926, "dur": 40, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565954968, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565954972, "dur": 63, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955037, "dur": 1, "ph": "X", "name": "ProcessMessages 878", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955039, "dur": 39, "ph": "X", "name": "ReadAsync 878", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955080, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955082, "dur": 39, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955124, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955127, "dur": 35, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955164, "dur": 2, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955167, "dur": 44, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955216, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955218, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955274, "dur": 2, "ph": "X", "name": "ProcessMessages 926", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955277, "dur": 46, "ph": "X", "name": "ReadAsync 926", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955326, "dur": 1, "ph": "X", "name": "ProcessMessages 803", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955328, "dur": 52, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955383, "dur": 2, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955386, "dur": 48, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955439, "dur": 2, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955443, "dur": 34, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955478, "dur": 1, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955480, "dur": 59, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955542, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955544, "dur": 41, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955589, "dur": 28, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955619, "dur": 40, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955662, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955664, "dur": 33, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955698, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955700, "dur": 22, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955727, "dur": 21, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955751, "dur": 23, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955780, "dur": 37, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955819, "dur": 25, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955847, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955849, "dur": 44, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955896, "dur": 1, "ph": "X", "name": "ProcessMessages 677", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955899, "dur": 38, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955938, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955940, "dur": 28, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955971, "dur": 26, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955997, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565955999, "dur": 22, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565956023, "dur": 22, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565956049, "dur": 25, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565956077, "dur": 23, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565956101, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565956103, "dur": 30, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565956136, "dur": 34, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565956172, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565956174, "dur": 33, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565956209, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565956212, "dur": 32, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565956247, "dur": 25, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565956276, "dur": 25, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565956306, "dur": 21, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565956330, "dur": 28, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565956360, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565956362, "dur": 19, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565956385, "dur": 282, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565956671, "dur": 101, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565956775, "dur": 5, "ph": "X", "name": "ProcessMessages 5055", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565956783, "dur": 50, "ph": "X", "name": "ReadAsync 5055", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565956836, "dur": 1, "ph": "X", "name": "ProcessMessages 817", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565956837, "dur": 50, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565956893, "dur": 32, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565956928, "dur": 23, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565956954, "dur": 57, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957015, "dur": 1, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957019, "dur": 59, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957082, "dur": 1, "ph": "X", "name": "ProcessMessages 906", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957084, "dur": 48, "ph": "X", "name": "ReadAsync 906", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957133, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957135, "dur": 40, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957179, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957181, "dur": 31, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957217, "dur": 25, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957244, "dur": 29, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957278, "dur": 30, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957311, "dur": 32, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957346, "dur": 28, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957377, "dur": 14, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957396, "dur": 24, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957422, "dur": 25, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957450, "dur": 19, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957472, "dur": 24, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957501, "dur": 1, "ph": "X", "name": "ProcessMessages 178", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957505, "dur": 38, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957544, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957546, "dur": 66, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957618, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957623, "dur": 46, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957670, "dur": 1, "ph": "X", "name": "ProcessMessages 992", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957673, "dur": 30, "ph": "X", "name": "ReadAsync 992", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957706, "dur": 1, "ph": "X", "name": "ProcessMessages 38", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957708, "dur": 65, "ph": "X", "name": "ReadAsync 38", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957777, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957780, "dur": 72, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957857, "dur": 2, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957862, "dur": 47, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957911, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957913, "dur": 27, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957943, "dur": 24, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957971, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565957974, "dur": 39, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958017, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958020, "dur": 52, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958075, "dur": 1, "ph": "X", "name": "ProcessMessages 889", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958079, "dur": 45, "ph": "X", "name": "ReadAsync 889", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958126, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958129, "dur": 22, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958154, "dur": 37, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958194, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958195, "dur": 44, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958243, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958245, "dur": 32, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958281, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958283, "dur": 42, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958328, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958366, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958368, "dur": 30, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958401, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958442, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958444, "dur": 44, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958491, "dur": 24, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958518, "dur": 24, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958543, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958545, "dur": 16, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958564, "dur": 26, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958593, "dur": 24, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958618, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958620, "dur": 24, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958649, "dur": 189, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958842, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958880, "dur": 2, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958883, "dur": 33, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958919, "dur": 23, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958944, "dur": 26, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958972, "dur": 22, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565958998, "dur": 24, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959025, "dur": 19, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959047, "dur": 1, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959048, "dur": 26, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959078, "dur": 27, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959107, "dur": 24, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959133, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959134, "dur": 33, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959169, "dur": 23, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959195, "dur": 23, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959222, "dur": 19, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959244, "dur": 21, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959267, "dur": 25, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959295, "dur": 25, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959323, "dur": 21, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959346, "dur": 22, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959369, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959371, "dur": 20, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959393, "dur": 21, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959417, "dur": 20, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959439, "dur": 20, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959461, "dur": 28, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959492, "dur": 23, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959517, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959519, "dur": 23, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959545, "dur": 22, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959569, "dur": 21, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959592, "dur": 18, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959612, "dur": 22, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959637, "dur": 27, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959666, "dur": 22, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959691, "dur": 21, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959715, "dur": 25, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959742, "dur": 20, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959765, "dur": 21, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959788, "dur": 20, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959811, "dur": 27, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959841, "dur": 23, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959865, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959867, "dur": 19, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959889, "dur": 22, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959913, "dur": 20, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959936, "dur": 56, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959996, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565959998, "dur": 28, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960029, "dur": 33, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960066, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960098, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960099, "dur": 24, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960124, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960127, "dur": 26, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960155, "dur": 23, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960181, "dur": 24, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960209, "dur": 22, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960234, "dur": 20, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960256, "dur": 33, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960291, "dur": 26, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960321, "dur": 24, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960348, "dur": 22, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960373, "dur": 22, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960397, "dur": 21, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960420, "dur": 21, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960443, "dur": 19, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960465, "dur": 23, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960490, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960491, "dur": 22, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960516, "dur": 19, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960538, "dur": 23, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960563, "dur": 20, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960585, "dur": 22, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960609, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960612, "dur": 22, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960636, "dur": 28, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960667, "dur": 20, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960690, "dur": 21, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960712, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960715, "dur": 19, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960737, "dur": 21, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960759, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960761, "dur": 18, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960782, "dur": 22, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960807, "dur": 22, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960832, "dur": 26, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960860, "dur": 23, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960886, "dur": 21, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960910, "dur": 19, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960932, "dur": 21, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960955, "dur": 21, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565960979, "dur": 21, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961003, "dur": 33, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961041, "dur": 2, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961044, "dur": 54, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961103, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961106, "dur": 38, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961145, "dur": 1, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961148, "dur": 32, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961182, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961184, "dur": 26, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961213, "dur": 29, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961245, "dur": 21, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961268, "dur": 19, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961291, "dur": 22, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961316, "dur": 20, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961339, "dur": 20, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961363, "dur": 24, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961389, "dur": 20, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961412, "dur": 41, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961455, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961479, "dur": 37, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961518, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961542, "dur": 29, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961575, "dur": 37, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961614, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961616, "dur": 29, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961647, "dur": 1, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961649, "dur": 163, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961814, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961840, "dur": 27, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961870, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961872, "dur": 33, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961907, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961909, "dur": 23, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961935, "dur": 20, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961957, "dur": 20, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565961979, "dur": 21, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962004, "dur": 23, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962031, "dur": 23, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962056, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962058, "dur": 30, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962090, "dur": 4, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962095, "dur": 35, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962133, "dur": 22, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962157, "dur": 24, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962183, "dur": 22, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962208, "dur": 32, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962244, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962246, "dur": 28, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962275, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962278, "dur": 26, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962307, "dur": 29, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962338, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962340, "dur": 30, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962372, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962397, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962399, "dur": 30, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962432, "dur": 14, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962448, "dur": 35, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962486, "dur": 23, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962511, "dur": 20, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962534, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962557, "dur": 20, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962588, "dur": 25, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962616, "dur": 20, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962637, "dur": 21, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962661, "dur": 19, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962683, "dur": 11, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962697, "dur": 21, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962719, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962721, "dur": 27, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962751, "dur": 22, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962775, "dur": 19, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962797, "dur": 22, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962821, "dur": 21, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962844, "dur": 22, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962871, "dur": 25, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962898, "dur": 21, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962921, "dur": 22, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962946, "dur": 20, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962968, "dur": 22, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565962992, "dur": 21, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963016, "dur": 21, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963040, "dur": 21, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963063, "dur": 16, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963081, "dur": 22, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963105, "dur": 28, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963135, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963137, "dur": 32, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963172, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963207, "dur": 25, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963235, "dur": 29, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963267, "dur": 39, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963307, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963309, "dur": 24, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963335, "dur": 19, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963357, "dur": 22, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963383, "dur": 28, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963413, "dur": 25, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963441, "dur": 22, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963466, "dur": 22, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963490, "dur": 28, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963519, "dur": 32, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963553, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963555, "dur": 22, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963579, "dur": 25, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963608, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963629, "dur": 21, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963652, "dur": 21, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963676, "dur": 22, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963700, "dur": 20, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963722, "dur": 35, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963760, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963782, "dur": 17, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963801, "dur": 18, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963822, "dur": 21, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963847, "dur": 19, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963869, "dur": 20, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963891, "dur": 21, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963914, "dur": 22, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963938, "dur": 23, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963964, "dur": 31, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565963998, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964019, "dur": 23, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964044, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964045, "dur": 22, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964070, "dur": 24, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964096, "dur": 20, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964119, "dur": 19, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964140, "dur": 20, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964163, "dur": 32, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964198, "dur": 29, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964229, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964232, "dur": 36, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964271, "dur": 1, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964273, "dur": 30, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964307, "dur": 26, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964337, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964369, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964371, "dur": 30, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964404, "dur": 27, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964433, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964434, "dur": 28, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964464, "dur": 24, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964491, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964493, "dur": 31, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964526, "dur": 25, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964553, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964555, "dur": 30, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964588, "dur": 26, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964616, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964618, "dur": 23, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964642, "dur": 1, "ph": "X", "name": "ProcessMessages 195", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964644, "dur": 25, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964670, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964672, "dur": 22, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964697, "dur": 21, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964721, "dur": 25, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964749, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964751, "dur": 27, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964781, "dur": 21, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964805, "dur": 26, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964835, "dur": 25, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964863, "dur": 22, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964886, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964890, "dur": 30, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964922, "dur": 26, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964951, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964953, "dur": 28, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964983, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565964985, "dur": 24, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965011, "dur": 24, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965038, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965041, "dur": 29, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965072, "dur": 24, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965099, "dur": 1, "ph": "X", "name": "ProcessMessages 247", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965102, "dur": 28, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965132, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965134, "dur": 24, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965159, "dur": 1, "ph": "X", "name": "ProcessMessages 162", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965162, "dur": 27, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965191, "dur": 23, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965217, "dur": 45, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965265, "dur": 18, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965285, "dur": 22, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965309, "dur": 21, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965332, "dur": 22, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965356, "dur": 25, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965383, "dur": 25, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965411, "dur": 24, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965437, "dur": 24, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965463, "dur": 26, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965493, "dur": 30, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965525, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965528, "dur": 24, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965554, "dur": 22, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965579, "dur": 22, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965603, "dur": 10, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965616, "dur": 19, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965638, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965663, "dur": 22, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965688, "dur": 24, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965715, "dur": 22, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965738, "dur": 27, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965768, "dur": 24, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965795, "dur": 32, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965828, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965851, "dur": 27, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965881, "dur": 21, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965905, "dur": 20, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965928, "dur": 22, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965953, "dur": 19, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565965974, "dur": 26, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966002, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966026, "dur": 25, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966053, "dur": 20, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966076, "dur": 22, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966101, "dur": 21, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966124, "dur": 21, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966148, "dur": 19, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966170, "dur": 22, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966193, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966196, "dur": 26, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966224, "dur": 22, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966249, "dur": 21, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966295, "dur": 23, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966319, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966321, "dur": 21, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966345, "dur": 25, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966373, "dur": 27, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966402, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966403, "dur": 37, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966445, "dur": 30, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966477, "dur": 22, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966502, "dur": 25, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966530, "dur": 28, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966560, "dur": 24, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966586, "dur": 29, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966618, "dur": 47, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966667, "dur": 1, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966668, "dur": 21, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966691, "dur": 25, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966718, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966740, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966742, "dur": 20, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966764, "dur": 22, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966788, "dur": 20, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966810, "dur": 22, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966834, "dur": 20, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966857, "dur": 21, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966880, "dur": 20, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966903, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966905, "dur": 25, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966932, "dur": 20, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966954, "dur": 10, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966965, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565966992, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967018, "dur": 20, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967040, "dur": 20, "ph": "X", "name": "ReadAsync 110", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967062, "dur": 21, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967086, "dur": 21, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967109, "dur": 22, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967133, "dur": 20, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967156, "dur": 20, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967178, "dur": 20, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967201, "dur": 38, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967242, "dur": 28, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967273, "dur": 22, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967297, "dur": 25, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967324, "dur": 18, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967345, "dur": 19, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967367, "dur": 20, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967389, "dur": 21, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967413, "dur": 22, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967438, "dur": 28, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967468, "dur": 24, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967495, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967497, "dur": 27, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967527, "dur": 25, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967554, "dur": 42, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967600, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967602, "dur": 32, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967636, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967638, "dur": 26, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967666, "dur": 1, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967667, "dur": 23, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967692, "dur": 22, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967716, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967718, "dur": 22, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967743, "dur": 20, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967765, "dur": 20, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967788, "dur": 21, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967811, "dur": 22, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967834, "dur": 21, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967857, "dur": 21, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967880, "dur": 21, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967904, "dur": 20, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967926, "dur": 22, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967950, "dur": 24, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967976, "dur": 19, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565967997, "dur": 31, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968032, "dur": 28, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968063, "dur": 22, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968088, "dur": 22, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968112, "dur": 22, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968135, "dur": 22, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968159, "dur": 20, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968182, "dur": 20, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968204, "dur": 21, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968228, "dur": 28, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968257, "dur": 26, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968286, "dur": 22, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968311, "dur": 20, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968333, "dur": 22, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968357, "dur": 24, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968383, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968384, "dur": 23, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968410, "dur": 38, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968450, "dur": 24, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968476, "dur": 21, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968498, "dur": 1, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968499, "dur": 22, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968522, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968524, "dur": 22, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968549, "dur": 26, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968577, "dur": 22, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968601, "dur": 18, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968621, "dur": 22, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968645, "dur": 22, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968669, "dur": 40, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968714, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968716, "dur": 28, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968745, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968747, "dur": 28, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968778, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968780, "dur": 31, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968814, "dur": 23, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968839, "dur": 1, "ph": "X", "name": "ProcessMessages 243", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968841, "dur": 26, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968869, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968870, "dur": 68, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968942, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565968974, "dur": 29, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969005, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969007, "dur": 17, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969026, "dur": 79, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969108, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969139, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969140, "dur": 28, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969170, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969172, "dur": 23, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969199, "dur": 59, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969262, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969291, "dur": 26, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969318, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969320, "dur": 24, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969347, "dur": 62, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969412, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969442, "dur": 28, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969472, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969474, "dur": 25, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969501, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969502, "dur": 57, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969562, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969592, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969595, "dur": 31, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969628, "dur": 25, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969655, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969657, "dur": 67, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969727, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969770, "dur": 29, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969801, "dur": 26, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969829, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969831, "dur": 60, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969894, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969924, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969926, "dur": 26, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969954, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969956, "dur": 22, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565969981, "dur": 50, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565970033, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565970068, "dur": 24, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565970094, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565970096, "dur": 27, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565970126, "dur": 74, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565970202, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565970232, "dur": 21, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565970256, "dur": 26, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565970284, "dur": 18, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565970305, "dur": 82, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565970389, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565970415, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565970417, "dur": 24, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565970444, "dur": 21, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565970467, "dur": 21, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565970491, "dur": 99, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565970592, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565970623, "dur": 28, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565970654, "dur": 19, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565970677, "dur": 72, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565970752, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565970782, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565970783, "dur": 23, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565970809, "dur": 27, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565970839, "dur": 20, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565970862, "dur": 137, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565971002, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565971028, "dur": 33, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565971064, "dur": 20, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565971086, "dur": 20, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565971109, "dur": 136, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565971248, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565971271, "dur": 23, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565971296, "dur": 18, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565971316, "dur": 17, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565971335, "dur": 21, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565971359, "dur": 20, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565971381, "dur": 125, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565971508, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565971532, "dur": 24, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565971558, "dur": 22, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565971582, "dur": 24, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565971609, "dur": 17, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565971628, "dur": 111, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565971741, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565971774, "dur": 30, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565971807, "dur": 26, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565971835, "dur": 27, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565971864, "dur": 23, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565971889, "dur": 129, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972020, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972048, "dur": 24, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972076, "dur": 21, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972098, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972100, "dur": 19, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972122, "dur": 123, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972247, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972273, "dur": 22, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972297, "dur": 21, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972320, "dur": 18, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972340, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972362, "dur": 17, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972382, "dur": 22, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972407, "dur": 21, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972430, "dur": 65, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972498, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972525, "dur": 20, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972548, "dur": 22, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972572, "dur": 21, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972596, "dur": 21, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972619, "dur": 22, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972643, "dur": 22, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972667, "dur": 21, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972690, "dur": 22, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972714, "dur": 18, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972734, "dur": 21, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972757, "dur": 20, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972779, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972800, "dur": 21, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972824, "dur": 150, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565972977, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565973004, "dur": 23, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565973029, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565973030, "dur": 21, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565973053, "dur": 22, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565973077, "dur": 138, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565973220, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565973223, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565973275, "dur": 2, "ph": "X", "name": "ProcessMessages 1859", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565973279, "dur": 28, "ph": "X", "name": "ReadAsync 1859", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565973310, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565973312, "dur": 39, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565973354, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565973355, "dur": 47, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565973407, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565973409, "dur": 43, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565973456, "dur": 52, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565973515, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565973517, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565973575, "dur": 2, "ph": "X", "name": "ProcessMessages 852", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565973579, "dur": 36, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565973617, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565973620, "dur": 84, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565973707, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565973766, "dur": 2, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565973771, "dur": 66, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565973840, "dur": 2, "ph": "X", "name": "ProcessMessages 1213", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565973844, "dur": 33, "ph": "X", "name": "ReadAsync 1213", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565973879, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565973882, "dur": 558, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565974444, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565974446, "dur": 82, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565974529, "dur": 4, "ph": "X", "name": "ProcessMessages 4609", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565974534, "dur": 63, "ph": "X", "name": "ReadAsync 4609", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565974600, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565974602, "dur": 32, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565974636, "dur": 57, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565974697, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565974723, "dur": 24, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565974750, "dur": 23, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565974774, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565974776, "dur": 90, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565974870, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565974918, "dur": 1, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565974920, "dur": 41, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565974964, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565974965, "dur": 63, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565975030, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565975095, "dur": 4, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565975101, "dur": 34, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565975137, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565975138, "dur": 32, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565975176, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565975205, "dur": 25, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565975233, "dur": 25, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565975261, "dur": 57, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565975320, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565975344, "dur": 30, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565975377, "dur": 21, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565975400, "dur": 26, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565975429, "dur": 24, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565975457, "dur": 22, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565975482, "dur": 70, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565975554, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565975583, "dur": 27, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565975612, "dur": 23, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565975637, "dur": 22, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565975661, "dur": 66, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565975729, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565975758, "dur": 30, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565975791, "dur": 24, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565975818, "dur": 71, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565975892, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565975921, "dur": 23, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565975947, "dur": 55, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976004, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976032, "dur": 2, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976035, "dur": 81, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976119, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976149, "dur": 20, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976171, "dur": 21, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976195, "dur": 73, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976270, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976303, "dur": 24, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976329, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976351, "dur": 25, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976379, "dur": 24, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976405, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976407, "dur": 69, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976481, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976525, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976527, "dur": 33, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976562, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976563, "dur": 63, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976630, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976660, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976662, "dur": 29, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976693, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976695, "dur": 23, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976722, "dur": 59, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976785, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976816, "dur": 26, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976844, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976846, "dur": 27, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976874, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976876, "dur": 71, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976950, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565976983, "dur": 20, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565977005, "dur": 23, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565977030, "dur": 79, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565977113, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565977147, "dur": 25, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565977175, "dur": 21, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565977198, "dur": 59, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565977260, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565977290, "dur": 21, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565977313, "dur": 23, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565977338, "dur": 21, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565977362, "dur": 62, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565977426, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565977459, "dur": 27, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565977488, "dur": 23, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565977514, "dur": 61, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565977578, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565977605, "dur": 24, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565977634, "dur": 28, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565977665, "dur": 60, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565977728, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565977752, "dur": 27, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565977782, "dur": 21, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565977805, "dur": 72, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565977879, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565977903, "dur": 27, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565977932, "dur": 27, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565977961, "dur": 20, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565977983, "dur": 87, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978075, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978078, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978117, "dur": 1, "ph": "X", "name": "ProcessMessages 973", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978120, "dur": 24, "ph": "X", "name": "ReadAsync 973", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978147, "dur": 75, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978226, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978263, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978266, "dur": 36, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978304, "dur": 32, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978338, "dur": 70, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978412, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978450, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978452, "dur": 34, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978489, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978491, "dur": 34, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978527, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978529, "dur": 71, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978603, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978628, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978629, "dur": 19, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978651, "dur": 25, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978678, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978679, "dur": 31, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978712, "dur": 1, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978713, "dur": 69, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978785, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978821, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978823, "dur": 29, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978854, "dur": 66, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978922, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978959, "dur": 31, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565978992, "dur": 28, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979022, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979024, "dur": 52, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979078, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979109, "dur": 25, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979136, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979138, "dur": 28, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979169, "dur": 29, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979201, "dur": 29, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979232, "dur": 26, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979261, "dur": 21, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979285, "dur": 29, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979316, "dur": 65, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979385, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979413, "dur": 22, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979437, "dur": 23, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979464, "dur": 20, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979486, "dur": 22, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979511, "dur": 35, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979549, "dur": 33, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979584, "dur": 37, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979623, "dur": 62, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979687, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979731, "dur": 28, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979762, "dur": 26, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979790, "dur": 34, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979827, "dur": 27, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979857, "dur": 30, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979892, "dur": 1, "ph": "X", "name": "ProcessMessages 161", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979894, "dur": 36, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979934, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979937, "dur": 40, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565979979, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980008, "dur": 127, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980140, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980194, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980196, "dur": 43, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980241, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980243, "dur": 57, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980305, "dur": 2, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980310, "dur": 39, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980351, "dur": 3, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980356, "dur": 43, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980402, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980405, "dur": 44, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980451, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980453, "dur": 36, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980492, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980495, "dur": 53, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980552, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980557, "dur": 56, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980616, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980618, "dur": 46, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980667, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980669, "dur": 54, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980728, "dur": 2, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980732, "dur": 45, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980779, "dur": 2, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980782, "dur": 33, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980818, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980819, "dur": 37, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980860, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980862, "dur": 40, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980905, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980907, "dur": 33, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980943, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980945, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980982, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565980985, "dur": 42, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981030, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981034, "dur": 45, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981080, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981083, "dur": 32, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981117, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981120, "dur": 45, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981168, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981172, "dur": 39, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981215, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981218, "dur": 51, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981272, "dur": 3, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981276, "dur": 40, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981318, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981323, "dur": 40, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981365, "dur": 2, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981368, "dur": 41, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981412, "dur": 2, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981415, "dur": 39, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981456, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981459, "dur": 31, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981492, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981495, "dur": 43, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981541, "dur": 2, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981544, "dur": 38, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981585, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981587, "dur": 39, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981630, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981633, "dur": 40, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981676, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981680, "dur": 35, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981719, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981722, "dur": 37, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981761, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981763, "dur": 42, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981808, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981811, "dur": 35, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981849, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981851, "dur": 35, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981888, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981891, "dur": 33, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981926, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981928, "dur": 36, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981967, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565981970, "dur": 41, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565982014, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565982016, "dur": 29, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565982047, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565982049, "dur": 38, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565982093, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565982097, "dur": 37, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565982136, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565982139, "dur": 42, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565982183, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565982185, "dur": 29, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565982218, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565982220, "dur": 88, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565982313, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565982347, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565982350, "dur": 45, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565982397, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565982400, "dur": 30, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565982432, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565982435, "dur": 37, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565982474, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565982477, "dur": 33, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565982513, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565982516, "dur": 29, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565982550, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565982553, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565982583, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565982585, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565982623, "dur": 2761, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565985390, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565985393, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565985457, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565985461, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565985513, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565985516, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565985564, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565985567, "dur": 167, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565985738, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565985771, "dur": 3278, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565989055, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565989060, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565989100, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565989102, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565989134, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565989136, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565989194, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565989224, "dur": 294, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565989521, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565989546, "dur": 229, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565989780, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565989815, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565989817, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565989844, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565989846, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565989877, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565989879, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565989906, "dur": 385, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565990298, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565990345, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565990348, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565990390, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565990392, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565990431, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565990433, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565990514, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565990544, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565990548, "dur": 224, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565990775, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565990777, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565990806, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565990808, "dur": 66, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565990878, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565990915, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565990949, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565990978, "dur": 25, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565991006, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565991035, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565991066, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565991086, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565991088, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565991113, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565991137, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565991139, "dur": 20, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565991163, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565991189, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565991191, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565991237, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565991273, "dur": 74, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565991351, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565991376, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565991379, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565991420, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565991459, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565991498, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565991513, "dur": 237, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565991755, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565991800, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565991801, "dur": 71, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565991876, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565991902, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565991945, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565991947, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565991992, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565991994, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992016, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992050, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992082, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992083, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992121, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992122, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992158, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992160, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992256, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992288, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992329, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992365, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992367, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992400, "dur": 31, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992434, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992437, "dur": 37, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992476, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992478, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992519, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992543, "dur": 93, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992639, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992678, "dur": 41, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992721, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992752, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992787, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992845, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992880, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992882, "dur": 75, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992961, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992996, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565992998, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565993035, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565993037, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565993069, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565993102, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565993104, "dur": 98, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565993204, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565993239, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565993273, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565993275, "dur": 23, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565993301, "dur": 110, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565993414, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565993485, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565993515, "dur": 160, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565993678, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565993705, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565993749, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565993777, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565993779, "dur": 23, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565993805, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565993834, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565993836, "dur": 51, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565993889, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565993917, "dur": 105, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565994025, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565994049, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565994071, "dur": 187, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565994261, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565994292, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565994314, "dur": 110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565994428, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565994430, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565994459, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565994492, "dur": 177, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565994674, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565994712, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565994716, "dur": 60, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565994780, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565994807, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565994809, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565994835, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565994860, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565994884, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565994904, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565994952, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565994983, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565995008, "dur": 142, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565995154, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565995192, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565995234, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565995257, "dur": 116, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565995379, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565995401, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565995422, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565995424, "dur": 101, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565995529, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565995560, "dur": 92, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565995654, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565995686, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565995739, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565995777, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565995780, "dur": 43, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565995826, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565995828, "dur": 99, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565995930, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565995951, "dur": 80, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565996034, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565996077, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565996079, "dur": 88, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565996170, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565996199, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565996226, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565996258, "dur": 278, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565996538, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565996574, "dur": 67, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565996643, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565996669, "dur": 272, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565996942, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565996972, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565996973, "dur": 212, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565997188, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565997214, "dur": 99, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565997315, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565997337, "dur": 976, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565998316, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565998335, "dur": 91, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565998429, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565998459, "dur": 549, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565999010, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565999083, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565999087, "dur": 161, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565999250, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565999288, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565999290, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565999327, "dur": 210, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565999541, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061565999578, "dur": 589, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566000169, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566000196, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566000199, "dur": 68341, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566068550, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566068554, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566068578, "dur": 24, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566068603, "dur": 4284, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566072894, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566072897, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566072920, "dur": 526, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566073452, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566073476, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566073478, "dur": 42, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566073525, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566073559, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566073561, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566073589, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566073591, "dur": 180, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566073776, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566073802, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566073862, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566073892, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566073923, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566073979, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566074005, "dur": 995, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566075006, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566075043, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566075045, "dur": 584, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566075634, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566075665, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566075699, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566075701, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566075736, "dur": 239, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566075979, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566076008, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566076037, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566076113, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566076142, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566076143, "dur": 98, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566076246, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566076276, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566076307, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566076340, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566076363, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566076443, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566076474, "dur": 1012, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566077491, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566077525, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566077549, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566077551, "dur": 378, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566077932, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566077963, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566077996, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566078086, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566078111, "dur": 77, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566078192, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566078214, "dur": 162, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566078379, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566078405, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566078437, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566078458, "dur": 574, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566079038, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566079064, "dur": 632, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566079700, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566079738, "dur": 252, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566079994, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080018, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080047, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080048, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080067, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080088, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080111, "dur": 25, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080138, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080140, "dur": 23, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080165, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080166, "dur": 25, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080194, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080196, "dur": 27, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080225, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080228, "dur": 27, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080257, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080259, "dur": 29, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080292, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080294, "dur": 28, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080325, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080327, "dur": 27, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080356, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080359, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080384, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080386, "dur": 24, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080412, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080414, "dur": 22, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080438, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080440, "dur": 30, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080472, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080475, "dur": 24, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080500, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080503, "dur": 19, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080525, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080528, "dur": 42, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080574, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080599, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080624, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080650, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080685, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080713, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080738, "dur": 184, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080925, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080950, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566080970, "dur": 236357, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566317345, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566317351, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566317430, "dur": 40, "ph": "X", "name": "ReadAsync 9335", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566317472, "dur": 34, "ph": "X", "name": "ProcessMessages 2894", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566317508, "dur": 2265, "ph": "X", "name": "ReadAsync 2894", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566319778, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566319781, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566319876, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566319884, "dur": 136570, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566456470, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566456476, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566456531, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566456538, "dur": 34958, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566491508, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566491514, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566491583, "dur": 31, "ph": "X", "name": "ProcessMessages 8563", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566491615, "dur": 172027, "ph": "X", "name": "ReadAsync 8563", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566663657, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566663664, "dur": 138, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566663815, "dur": 36, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566663854, "dur": 5177, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566669037, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566669044, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566669085, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566669089, "dur": 1685, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566670780, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566670783, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566670857, "dur": 25, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566670884, "dur": 39437, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566710332, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566710337, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566710393, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566710397, "dur": 690, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566711093, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566711095, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566711182, "dur": 23, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566711207, "dur": 176183, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566887404, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566887411, "dur": 117, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566887534, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061566887542, "dur": 309723, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061567197275, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061567197280, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061567197317, "dur": 37, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061567197358, "dur": 5421, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061567202783, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061567202786, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061567202840, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061567202843, "dur": 360, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061567203210, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061567203213, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061567203254, "dur": 25, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061567203280, "dur": 308754, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061567512046, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061567512052, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061567512136, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061567512143, "dur": 361, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061567512509, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061567512511, "dur": 105, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061567512620, "dur": 27, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061567512648, "dur": 1186, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061567513838, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061567513841, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061567513879, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 2208, "tid": 176093659136, "ts": 1750061567513881, "dur": 6636, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 2208, "tid": 186, "ts": 1750061567521251, "dur": 4847, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 2208, "tid": 171798691840, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 2208, "tid": 171798691840, "ts": 1750061565935075, "dur": 64913, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 2208, "tid": 171798691840, "ts": 1750061565999990, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 2208, "tid": 171798691840, "ts": 1750061565999991, "dur": 34, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 2208, "tid": 186, "ts": 1750061567526102, "dur": 16, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 2208, "tid": 167503724544, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 2208, "tid": 167503724544, "ts": 1750061565931663, "dur": 1588922, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 2208, "tid": 167503724544, "ts": 1750061565931959, "dur": 3073, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 2208, "tid": 167503724544, "ts": 1750061567520594, "dur": 63, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 2208, "tid": 167503724544, "ts": 1750061567520610, "dur": 28, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 2208, "tid": 167503724544, "ts": 1750061567520660, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 2208, "tid": 186, "ts": 1750061567526122, "dur": 33, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1750061565951607, "dur": 1450, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750061565953067, "dur": 915, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750061565954127, "dur": 80, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1750061565954208, "dur": 425, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750061565956568, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_11815C2DB39EE2D6.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750061565957522, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_9CC28255281D0D3B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750061565957772, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750061565966484, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750061565973677, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750061565974340, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750061565976165, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll"}}, {"pid": 12345, "tid": 0, "ts": 1750061565954656, "dur": 25211, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750061565979883, "dur": 1532570, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750061567512454, "dur": 787, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750061567513242, "dur": 169, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750061567513415, "dur": 91, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750061567513706, "dur": 1031, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1750061565955536, "dur": 24532, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565980221, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_7461CA82F1A7BC76.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750061565980449, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565981104, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750061565981219, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750061565981408, "dur": 296, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750061565981748, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750061565981880, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1750061565982084, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565982361, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565982607, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565982853, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565983082, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565983334, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565983533, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565983813, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565984362, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565984601, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565984816, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565985033, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565985253, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565985514, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565985735, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565985942, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565986198, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565986420, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565986670, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565986897, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565987124, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565987360, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565987568, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565987772, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565988033, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565988650, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565988956, "dur": 525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565989485, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750061565990245, "dur": 502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750061565990749, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565991010, "dur": 756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565991844, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565992356, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565992914, "dur": 681, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565993596, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565993977, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750061565994143, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565994343, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750061565994747, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750061565994859, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750061565995417, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750061565995530, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750061565995920, "dur": 3045, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061565998966, "dur": 71632, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061566070605, "dur": 2751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750061566073407, "dur": 2090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750061566075543, "dur": 2337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750061566077916, "dur": 2558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750061566080510, "dur": 1431929, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565955527, "dur": 24495, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565980379, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_9C6FA246188F95D9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750061565980489, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_3EE9BB20E930FDFD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750061565980950, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750061565981096, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1750061565981264, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1750061565981410, "dur": 172, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1750061565981661, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1750061565981890, "dur": 172, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1750061565982084, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565982378, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565982747, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565982983, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565983231, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565983502, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565983766, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565984367, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565984711, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565985039, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565985406, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565985680, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565985939, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565986177, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565986431, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565986786, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565987469, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565987684, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565987899, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565988156, "dur": 115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565988271, "dur": 80, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565988351, "dur": 77, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565988539, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565988938, "dur": 526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565989466, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750061565990085, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750061565990610, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565990856, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565990974, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750061565991244, "dur": 478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750061565991723, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565991802, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565992014, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750061565992153, "dur": 347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565992510, "dur": 508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750061565993019, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565993132, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565993628, "dur": 365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565993993, "dur": 4942, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061565998937, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750061565999136, "dur": 71398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061566070541, "dur": 2274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750061566072815, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061566073119, "dur": 2209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750061566075330, "dur": 881, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061566076220, "dur": 2143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750061566078364, "dur": 519, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061566078892, "dur": 1879, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750061566080825, "dur": 1431626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565955474, "dur": 24707, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565980225, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_94280B12CFD84F1A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750061565980445, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_1E5BE8A4F6F01B45.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750061565981054, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565981127, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1750061565981314, "dur": 191, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750061565981519, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1750061565981669, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1750061565981881, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750061565982027, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2897256077774953845.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750061565982079, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565982233, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2897256077774953845.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750061565982391, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750061565982466, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565982756, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565982974, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565983217, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565983434, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565983681, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565984321, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565984650, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565984994, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565985248, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565985470, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565985730, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565985947, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565986197, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565986423, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565986634, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565986856, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565987078, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565987297, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565987521, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565987868, "dur": 675, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\bool3.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1750061565987738, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565988592, "dur": 356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565988948, "dur": 510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565989459, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750061565989635, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565990829, "dur": 826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750061565991656, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565992009, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750061565992208, "dur": 574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750061565992783, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061565992894, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750061565993083, "dur": 807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750061565993971, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750061565994150, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750061565994672, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750061565994832, "dur": 740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750061565995642, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750061565995808, "dur": 1233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750061565997087, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750061565997198, "dur": 970, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750061565998206, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750061565998312, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750061565998956, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750061565999124, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750061565999419, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750061566000570, "dur": 317169, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750061566319411, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750061566319110, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750061566319633, "dur": 172011, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750061566493070, "dur": 391002, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750061566493063, "dur": 392965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750061566887040, "dur": 153, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061566887500, "dur": 309626, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750061567202477, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1750061567202470, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1750061567202607, "dur": 466, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1750061567203075, "dur": 309394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565955352, "dur": 24585, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565979942, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_D44F1D7947A8470C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750061565980160, "dur": 195, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_1DC9F96436961A23.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750061565980456, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565980927, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750061565981112, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1750061565981575, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750061565981669, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1750061565981843, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750061565981988, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750061565982038, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565982226, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10411718816959651794.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750061565982323, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750061565982403, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565982761, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565982973, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565983250, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565983509, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565983764, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565984290, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565984483, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565984693, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565984927, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565985176, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565985391, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565985658, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565985863, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565986098, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565986314, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565986645, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565986865, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565987077, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565987282, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565987649, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565987864, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565988533, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565988939, "dur": 523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565989463, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750061565990057, "dur": 880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750061565990938, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565991028, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750061565991295, "dur": 986, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750061565992389, "dur": 509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565992900, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750061565993134, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565993366, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750061565993702, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565993977, "dur": 1159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565995137, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750061565995290, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750061565995855, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565995937, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750061565996047, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750061565996426, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750061565996529, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750061565996835, "dur": 2145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061565998981, "dur": 71601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061566070586, "dur": 2693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750061566073321, "dur": 2756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750061566076112, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750061566076179, "dur": 2382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750061566078562, "dur": 1547, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061566080375, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061566080610, "dur": 1431835, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565955718, "dur": 24431, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565980324, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_B2FE57DE340CBDFF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750061565980452, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_1DB8604BF3C2B15B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750061565980977, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750061565981181, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750061565981296, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750061565981408, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750061565981577, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1750061565981748, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750061565981887, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750061565982045, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565982296, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750061565982404, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565982896, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565983129, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565983384, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565983590, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565983820, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565984405, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565984662, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565984876, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565985199, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565985600, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565986068, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565986327, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565986568, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565986774, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565987003, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565987221, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565987425, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565987621, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565987853, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565988080, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565988254, "dur": 381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565988635, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565988947, "dur": 1032, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565989985, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750061565990126, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565990264, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750061565990891, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565991011, "dur": 793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565991804, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565992342, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750061565992507, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750061565992922, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565993112, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565993593, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565993797, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750061565993914, "dur": 1089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750061565995055, "dur": 3907, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061565998963, "dur": 71649, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061566070618, "dur": 2212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750061566072867, "dur": 2983, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750061566075887, "dur": 1867, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750061566077755, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061566077921, "dur": 2071, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750061566080000, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061566080161, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1750061566080403, "dur": 1432045, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565955294, "dur": 24622, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565980422, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_7D33EB796E04AD1F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750061565980513, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_C9BF491CE9429A6C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750061565980848, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750061565981116, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750061565981278, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1750061565981403, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1750061565981538, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1750061565981608, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1750061565981819, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750061565982034, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565982240, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750061565982356, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565982579, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565982930, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565983192, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565983397, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565983648, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565983858, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565984431, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565984629, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565984838, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565985158, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565985482, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565985715, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565985967, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565986218, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565986426, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565986637, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565986842, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565987090, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565987440, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565987646, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565987866, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565988163, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565988638, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565988952, "dur": 567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565989974, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750061565990081, "dur": 920, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565991007, "dur": 590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750061565991597, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565991852, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565992344, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565992908, "dur": 689, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565993597, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565993978, "dur": 1669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565995648, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750061565995805, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750061565996118, "dur": 2839, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061565998957, "dur": 71553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061566070512, "dur": 2782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750061566073295, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061566073414, "dur": 2513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750061566075928, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061566075987, "dur": 3028, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750061566079016, "dur": 953, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061566080162, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061566080369, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061566080520, "dur": 1431921, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565955336, "dur": 24592, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565979932, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_1BD15D364A91220E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750061565980344, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_BF4431DE103A29DE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750061565980497, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_E3FF4E8988E07430.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750061565980638, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C2AE5CAA0D72FF79.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750061565980949, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1750061565981118, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1750061565981262, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1750061565981316, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750061565981390, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750061565981565, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1750061565981882, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750061565982035, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565982216, "dur": 219, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10397561839769426034.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750061565982436, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565982793, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565983014, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565983305, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565983594, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565983808, "dur": 659, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Window\\TimelineWindow_HeaderGui.cs"}}, {"pid": 12345, "tid": 7, "ts": 1750061565983808, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565984664, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565984889, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565985144, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565985405, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565985748, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565985946, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565986204, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565986415, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565986628, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565986827, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565987052, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565987258, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565987458, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565987665, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565987880, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565988067, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565988465, "dur": 64, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565988537, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565988954, "dur": 548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565989506, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750061565990110, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750061565990813, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565991024, "dur": 782, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565991806, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565992337, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750061565992561, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750061565993161, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565993621, "dur": 348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565993970, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750061565994138, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750061565994545, "dur": 4437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061565998982, "dur": 71643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061566070631, "dur": 2773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750061566073440, "dur": 2371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750061566075812, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061566075984, "dur": 1902, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750061566077926, "dur": 1968, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750061566079946, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061566080091, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750061566080224, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061566080389, "dur": 1122084, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061567202492, "dur": 309324, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750061567202474, "dur": 309344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750061567511840, "dur": 536, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750061565955395, "dur": 24552, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565979952, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_1C3753EAD72C002A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750061565980107, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_B6C5AA3F81E3AFD4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750061565980318, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_B34AAB5B4FEDF5AF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750061565980476, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_8D701D583C70E786.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750061565980642, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_8D701D583C70E786.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750061565981225, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750061565981324, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1750061565981461, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750061565981673, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1750061565981881, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750061565982034, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565982358, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565982628, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565982924, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565983143, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565983363, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565983664, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565983884, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565984414, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565984635, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565984853, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565985121, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565985359, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565985571, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565985785, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565986039, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565986258, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565986488, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565986703, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565986935, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565987158, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565987358, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565987566, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565987801, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565988023, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565988233, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565988673, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565988956, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565989498, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750061565990445, "dur": 577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750061565991023, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565991266, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565991380, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750061565991550, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565991617, "dur": 576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750061565992194, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565992299, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565992350, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565992897, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750061565993123, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750061565993653, "dur": 331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565993984, "dur": 1960, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565995945, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750061565996087, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750061565996542, "dur": 2429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061565998972, "dur": 71535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061566070513, "dur": 2200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750061566072754, "dur": 2253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750061566075008, "dur": 835, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061566075853, "dur": 1943, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750061566077835, "dur": 2133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750061566080153, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061566080388, "dur": 588222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061566668642, "dur": 181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1750061566668612, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1750061566668864, "dur": 1718, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1750061566670586, "dur": 841875, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061565955458, "dur": 24504, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061565979969, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_3F8B9D82C8E678D4.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750061565980317, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_40EACCB6DE9B854E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750061565980452, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061565981275, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1750061565981413, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1750061565981625, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1750061565981761, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750061565982049, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061565982266, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2400031028012695012.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750061565982347, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17296387151066238333.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750061565982436, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061565982666, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061565982857, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061565983076, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061565983330, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061565983571, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061565984410, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061565984750, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061565985115, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061565985812, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061565986035, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061565986279, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061565986517, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061565986722, "dur": 1292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061565988015, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061565988257, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061565988555, "dur": 391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061565988946, "dur": 1064, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061565990015, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750061565990213, "dur": 760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750061565990974, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061565991127, "dur": 675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061565991803, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061565992342, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750061565992488, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061565992603, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750061565993128, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061565993643, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061565993985, "dur": 4966, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061565998952, "dur": 3098, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061566002050, "dur": 68474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061566070530, "dur": 3260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750061566073791, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061566073860, "dur": 3455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750061566077362, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750061566077420, "dur": 2259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750061566079680, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061566080009, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061566080126, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061566080187, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061566080373, "dur": 199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061566080591, "dur": 1431888, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565955533, "dur": 24512, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565980056, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_A11EFFC223F8866B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750061565980109, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565980298, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_0713B1E0F0E8C075.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750061565980453, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565980951, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1750061565981146, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1750061565981326, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1750061565981498, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1750061565981685, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1750061565981818, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1750061565981991, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1750061565982048, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565982419, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565982737, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565982933, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565983151, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565983363, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565983587, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565983818, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565984385, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565984720, "dur": 362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565985083, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565985454, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565985700, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565985959, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565986194, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565986412, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565986707, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565986935, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565987166, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565987362, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565987574, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565987793, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565988531, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565988940, "dur": 520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565989465, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750061565989608, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565990795, "dur": 1429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750061565992339, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750061565992534, "dur": 1199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750061565993792, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750061565993903, "dur": 1181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750061565995133, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750061565995248, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750061565995679, "dur": 3274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061565998954, "dur": 71567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061566070523, "dur": 2136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750061566072661, "dur": 1121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061566073788, "dur": 2293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750061566076120, "dur": 2102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750061566078265, "dur": 1992, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750061566080396, "dur": 1432059, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061565955250, "dur": 24647, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061565979929, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061565980101, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_36DAB0C55B9C80DA.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750061565980172, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_36DAB0C55B9C80DA.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750061565980448, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_136791916CB4C4E9.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750061565980978, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750061565981175, "dur": 4106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750061565985357, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750061565985613, "dur": 3254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750061565988979, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750061565989075, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750061565989460, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750061565990045, "dur": 792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750061565990900, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061565990970, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750061565991225, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750061565991671, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061565991761, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061565991825, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061565992338, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750061565992515, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750061565992951, "dur": 684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061565993635, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061565993998, "dur": 4988, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061565998987, "dur": 71572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061566070565, "dur": 2327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1750061566072893, "dur": 415, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061566073316, "dur": 2179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1750061566075496, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061566075578, "dur": 2066, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1750061566077645, "dur": 420, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061566078075, "dur": 1864, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1750061566079940, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061566080219, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061566080440, "dur": 1432003, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565955587, "dur": 24500, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565980096, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_E6F99946C6EBBC96.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750061565980253, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_89505EFF408B1938.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750061565980461, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565981273, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750061565981404, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1750061565981662, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1750061565982075, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565982252, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2781082588993088484.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750061565982388, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750061565982444, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565982769, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565982981, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565983248, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565983555, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565983817, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565984406, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565984720, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565985078, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565985286, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565985515, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565985719, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565985921, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565986150, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565986375, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565986615, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565986820, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565987071, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565987317, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565987552, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565987786, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565988050, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565988639, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565988952, "dur": 1036, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565989992, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750061565990142, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750061565990385, "dur": 562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750061565991014, "dur": 791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565991805, "dur": 544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565992350, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565992902, "dur": 687, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565993614, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565993994, "dur": 4955, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061565998950, "dur": 973, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061566001190, "dur": 104, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/6000.0.50f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 12, "ts": 1750061566001294, "dur": 708, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/6000.0.50f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 12, "ts": 1750061565999924, "dur": 2119, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061566002043, "dur": 68473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061566070520, "dur": 2686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750061566073208, "dur": 519, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061566073739, "dur": 2189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750061566075929, "dur": 1879, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061566077827, "dur": 2710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750061566080577, "dur": 1431881, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565955620, "dur": 24485, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565980393, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_217E1FA7BA4C2F92.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750061565981063, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750061565981212, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750061565981312, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1750061565981624, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750061565981713, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750061565982069, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565982253, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750061565982369, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565982811, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565983040, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565983584, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565983810, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565984335, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565984565, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565984770, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565984978, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565985494, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565985733, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565985951, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565986190, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565986408, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565986641, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565986847, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565987153, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565987361, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565987562, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565987801, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565988006, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565988223, "dur": 392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565988615, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565988957, "dur": 517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565989478, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750061565990099, "dur": 624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750061565990724, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565991054, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750061565991345, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750061565991862, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565991944, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750061565992131, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750061565992546, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565992717, "dur": 442, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565993165, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565993594, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565993976, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750061565994183, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750061565994772, "dur": 4216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061565998988, "dur": 71583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061566070575, "dur": 2091, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750061566072667, "dur": 747, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061566073419, "dur": 3851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750061566077271, "dur": 601, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061566077882, "dur": 2690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750061566080604, "dur": 1431860, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061565955668, "dur": 24462, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061565980139, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_C5179C2B98A35602.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750061565980314, "dur": 219, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_7B75EE72BE5BAF62.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750061565980653, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_BB80B5CC0580A018.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750061565981000, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750061565981209, "dur": 3982, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750061565985280, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061565985518, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061565985805, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061565986090, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061565986305, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061565986546, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061565986888, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061565987126, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061565987354, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061565987570, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061565987768, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061565987979, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061565988218, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061565988703, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061565988961, "dur": 506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061565989471, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750061565990072, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750061565990584, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061565990682, "dur": 649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750061565991332, "dur": 872, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061565992240, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061565992343, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061565992920, "dur": 671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061565993619, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061565993992, "dur": 4946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061565998944, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750061565999165, "dur": 71474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061566070641, "dur": 2970, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750061566073661, "dur": 2322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750061566075985, "dur": 326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061566076320, "dur": 1969, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750061566078323, "dur": 1968, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750061566080384, "dur": 238734, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061566319139, "dur": 133715, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750061566319121, "dur": 135354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750061566456028, "dur": 231, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061566456558, "dur": 206960, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750061566668604, "dur": 41476, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750061566668598, "dur": 41484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750061566710113, "dur": 859, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 14, "ts": 1750061566710977, "dur": 801485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565955751, "dur": 24405, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565980198, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_A84A75205A4D3666.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750061565980404, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_92D06276DA3B75A6.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750061565980653, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_E5E2ABC3484D0134.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750061565981226, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1750061565981604, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1750061565981758, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1750061565982067, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565982366, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565982696, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565983105, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565983342, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565983659, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565983869, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565984402, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565984656, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565984861, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565985132, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565985384, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565985625, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565985862, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565986091, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565986310, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565987009, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565987214, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565987409, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565987632, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565987835, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565988075, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565988268, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565988653, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565988950, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565989515, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750061565990781, "dur": 1386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750061565992168, "dur": 403, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565992580, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565992643, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750061565992834, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750061565993283, "dur": 329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565993612, "dur": 359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565993972, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750061565994112, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565994299, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750061565994701, "dur": 4293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061565998994, "dur": 71552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061566070552, "dur": 2686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750061566073274, "dur": 2180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750061566075507, "dur": 1805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750061566077411, "dur": 2130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750061566079577, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll"}}, {"pid": 12345, "tid": 15, "ts": 1750061566079630, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061566079905, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061566080085, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061566080362, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061566080419, "dur": 1432047, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565955793, "dur": 24379, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565980221, "dur": 298, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_CDEA261D3E32C133.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750061565980551, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565981178, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750061565981522, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750061565981657, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1750061565981890, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750061565982075, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565982353, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565982579, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565982783, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565982997, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565983271, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565983540, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565983755, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565984333, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565984614, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565985020, "dur": 1489, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@f10877fbf761\\Editor\\Data\\Graphs\\Matrix3ShaderProperty.cs"}}, {"pid": 12345, "tid": 16, "ts": 1750061565984966, "dur": 1712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565986678, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565986896, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565987149, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565987363, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565987586, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565987793, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565988022, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565988243, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565988678, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565988960, "dur": 1063, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565990028, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750061565990240, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1750061565990683, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565990761, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750061565991107, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1750061565991584, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565991660, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750061565991874, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1750061565992202, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565992289, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565992355, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565992897, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750061565993124, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1750061565993590, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 16, "ts": 1750061565994043, "dur": 177, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061565994555, "dur": 73851, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 16, "ts": 1750061566070509, "dur": 2189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750061566072699, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061566072776, "dur": 2050, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750061566074874, "dur": 2095, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750061566076970, "dur": 450, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061566077427, "dur": 1987, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750061566079415, "dur": 843, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061566080378, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061566080830, "dur": 1431660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750061567518792, "dur": 1240, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 2208, "tid": 186, "ts": 1750061567526198, "dur": 11774, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 2208, "tid": 186, "ts": 1750061567538016, "dur": 1350, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 2208, "tid": 186, "ts": 1750061567521204, "dur": 18210, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}