{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 2208, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 2208, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 2208, "tid": 163, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 2208, "tid": 163, "ts": 1750061133511044, "dur": 15, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 2208, "tid": 163, "ts": 1750061133511081, "dur": 10, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 2208, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 2208, "tid": 1, "ts": 1750061130663849, "dur": 2500, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 2208, "tid": 1, "ts": 1750061130666357, "dur": 30063, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 2208, "tid": 1, "ts": 1750061130696423, "dur": 32690, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 2208, "tid": 163, "ts": 1750061133511095, "dur": 21, "ph": "X", "name": "", "args": {}}, {"pid": 2208, "tid": 150323855360, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130663811, "dur": 15936, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130679748, "dur": 2830689, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130679759, "dur": 55, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130679819, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130679822, "dur": 92, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130679919, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130679958, "dur": 10, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130679970, "dur": 3252, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130683228, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130683233, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130683298, "dur": 3, "ph": "X", "name": "ProcessMessages 718", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130683302, "dur": 46, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130683354, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130683420, "dur": 2, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130683424, "dur": 69, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130683497, "dur": 2, "ph": "X", "name": "ProcessMessages 879", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130683501, "dur": 52, "ph": "X", "name": "ReadAsync 879", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130683556, "dur": 2, "ph": "X", "name": "ProcessMessages 1115", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130683559, "dur": 43, "ph": "X", "name": "ReadAsync 1115", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130683605, "dur": 3, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130683610, "dur": 47, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130683661, "dur": 2, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130683666, "dur": 85, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130683756, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130683758, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130683811, "dur": 2, "ph": "X", "name": "ProcessMessages 1180", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130683813, "dur": 52, "ph": "X", "name": "ReadAsync 1180", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130683867, "dur": 3, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130683872, "dur": 45, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130683919, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130683921, "dur": 57, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130683981, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130683983, "dur": 44, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684029, "dur": 1, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684030, "dur": 31, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684065, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684068, "dur": 35, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684107, "dur": 32, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684142, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684144, "dur": 29, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684178, "dur": 24, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684206, "dur": 34, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684242, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684244, "dur": 32, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684279, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684281, "dur": 48, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684330, "dur": 1, "ph": "X", "name": "ProcessMessages 1001", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684332, "dur": 27, "ph": "X", "name": "ReadAsync 1001", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684362, "dur": 35, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684398, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684400, "dur": 21, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684424, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684425, "dur": 26, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684454, "dur": 36, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684494, "dur": 31, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684527, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684530, "dur": 36, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684568, "dur": 2, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684571, "dur": 29, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684602, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684604, "dur": 27, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684634, "dur": 26, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684662, "dur": 24, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684687, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684689, "dur": 27, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684720, "dur": 20, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684743, "dur": 31, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684776, "dur": 33, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684812, "dur": 33, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684849, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684851, "dur": 27, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684882, "dur": 25, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684909, "dur": 24, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684935, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684937, "dur": 30, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684969, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684971, "dur": 24, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684996, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130684997, "dur": 21, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685021, "dur": 20, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685044, "dur": 22, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685070, "dur": 24, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685097, "dur": 20, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685119, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685121, "dur": 24, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685148, "dur": 19, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685170, "dur": 23, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685195, "dur": 1, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685196, "dur": 21, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685221, "dur": 24, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685248, "dur": 21, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685272, "dur": 19, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685296, "dur": 25, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685323, "dur": 22, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685348, "dur": 24, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685374, "dur": 37, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685414, "dur": 34, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685450, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685452, "dur": 29, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685483, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685485, "dur": 25, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685512, "dur": 2, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685515, "dur": 29, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685545, "dur": 1, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685547, "dur": 27, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685575, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685577, "dur": 25, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685605, "dur": 27, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685635, "dur": 35, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685674, "dur": 2, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685677, "dur": 43, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685722, "dur": 1, "ph": "X", "name": "ProcessMessages 976", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685726, "dur": 40, "ph": "X", "name": "ReadAsync 976", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685769, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685771, "dur": 41, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685814, "dur": 1, "ph": "X", "name": "ProcessMessages 723", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685816, "dur": 35, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685853, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685856, "dur": 43, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685901, "dur": 1, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130685905, "dur": 262, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686169, "dur": 1, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686174, "dur": 65, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686260, "dur": 6, "ph": "X", "name": "ProcessMessages 3546", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686269, "dur": 60, "ph": "X", "name": "ReadAsync 3546", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686333, "dur": 3, "ph": "X", "name": "ProcessMessages 1393", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686339, "dur": 37, "ph": "X", "name": "ReadAsync 1393", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686376, "dur": 1, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686378, "dur": 21, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686401, "dur": 97, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686502, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686504, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686537, "dur": 1, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686539, "dur": 27, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686567, "dur": 1, "ph": "X", "name": "ProcessMessages 214", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686569, "dur": 21, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686592, "dur": 22, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686617, "dur": 21, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686641, "dur": 24, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686667, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686669, "dur": 24, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686695, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686697, "dur": 24, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686724, "dur": 2, "ph": "X", "name": "ProcessMessages 235", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686727, "dur": 27, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686757, "dur": 27, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686788, "dur": 23, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686812, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686814, "dur": 26, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686842, "dur": 21, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686866, "dur": 23, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686891, "dur": 22, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686916, "dur": 19, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686938, "dur": 23, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686964, "dur": 17, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130686983, "dur": 19, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687005, "dur": 21, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687028, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687030, "dur": 21, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687053, "dur": 20, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687076, "dur": 19, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687099, "dur": 14, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687115, "dur": 21, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687140, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687143, "dur": 31, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687175, "dur": 1, "ph": "X", "name": "ProcessMessages 631", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687179, "dur": 27, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687207, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687209, "dur": 22, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687233, "dur": 22, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687258, "dur": 22, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687283, "dur": 19, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687304, "dur": 38, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687345, "dur": 2, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687348, "dur": 32, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687383, "dur": 1, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687385, "dur": 22, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687409, "dur": 21, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687432, "dur": 25, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687458, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687460, "dur": 22, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687485, "dur": 17, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687505, "dur": 14, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687521, "dur": 46, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687568, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687570, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687597, "dur": 26, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687624, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687627, "dur": 19, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687650, "dur": 23, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687675, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687677, "dur": 40, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687719, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687720, "dur": 27, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687750, "dur": 25, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687778, "dur": 23, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687803, "dur": 20, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687825, "dur": 2, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687829, "dur": 19, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687851, "dur": 22, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687875, "dur": 20, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687897, "dur": 25, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687924, "dur": 21, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687949, "dur": 22, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687973, "dur": 22, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130687998, "dur": 17, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688017, "dur": 19, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688038, "dur": 21, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688061, "dur": 19, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688083, "dur": 21, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688108, "dur": 22, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688132, "dur": 20, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688156, "dur": 19, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688178, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688201, "dur": 35, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688237, "dur": 2, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688240, "dur": 23, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688266, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688267, "dur": 25, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688295, "dur": 23, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688320, "dur": 20, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688343, "dur": 26, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688375, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688377, "dur": 55, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688433, "dur": 1, "ph": "X", "name": "ProcessMessages 982", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688435, "dur": 26, "ph": "X", "name": "ReadAsync 982", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688462, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688465, "dur": 22, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688489, "dur": 22, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688517, "dur": 23, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688542, "dur": 20, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688563, "dur": 2, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688566, "dur": 23, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688592, "dur": 24, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688617, "dur": 1, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688619, "dur": 20, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688641, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688643, "dur": 21, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688666, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688668, "dur": 20, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688692, "dur": 22, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688718, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688719, "dur": 24, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688746, "dur": 28, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688776, "dur": 19, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688798, "dur": 21, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688823, "dur": 18, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688844, "dur": 21, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688869, "dur": 21, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688892, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688894, "dur": 34, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688930, "dur": 26, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688959, "dur": 21, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130688982, "dur": 214, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689198, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689228, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689231, "dur": 29, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689261, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689263, "dur": 28, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689294, "dur": 24, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689320, "dur": 23, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689346, "dur": 19, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689366, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689391, "dur": 21, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689416, "dur": 20, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689439, "dur": 21, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689462, "dur": 22, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689487, "dur": 23, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689511, "dur": 26, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689540, "dur": 21, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689562, "dur": 23, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689588, "dur": 20, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689610, "dur": 21, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689634, "dur": 22, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689659, "dur": 18, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689681, "dur": 25, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689709, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689730, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689731, "dur": 25, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689759, "dur": 23, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689785, "dur": 24, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689810, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689812, "dur": 25, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689841, "dur": 11, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689854, "dur": 19, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689875, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689903, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689907, "dur": 23, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689932, "dur": 21, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689955, "dur": 22, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130689980, "dur": 22, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690004, "dur": 21, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690027, "dur": 21, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690051, "dur": 23, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690077, "dur": 23, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690102, "dur": 21, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690126, "dur": 23, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690152, "dur": 19, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690174, "dur": 63, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690239, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690241, "dur": 36, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690279, "dur": 1, "ph": "X", "name": "ProcessMessages 1270", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690281, "dur": 22, "ph": "X", "name": "ReadAsync 1270", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690306, "dur": 21, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690330, "dur": 20, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690352, "dur": 23, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690379, "dur": 21, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690401, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690403, "dur": 27, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690434, "dur": 22, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690459, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690460, "dur": 20, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690482, "dur": 20, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690505, "dur": 24, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690531, "dur": 20, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690555, "dur": 26, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690583, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690585, "dur": 36, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690624, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690626, "dur": 29, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690657, "dur": 1, "ph": "X", "name": "ProcessMessages 273", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690659, "dur": 18, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690679, "dur": 21, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690702, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690706, "dur": 39, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690747, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690749, "dur": 32, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690784, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690786, "dur": 36, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690824, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690826, "dur": 30, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690858, "dur": 3, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690861, "dur": 61, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690924, "dur": 1, "ph": "X", "name": "ProcessMessages 940", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690926, "dur": 37, "ph": "X", "name": "ReadAsync 940", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690964, "dur": 1, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690965, "dur": 32, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130690999, "dur": 27, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691029, "dur": 28, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691059, "dur": 25, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691087, "dur": 25, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691114, "dur": 24, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691141, "dur": 26, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691168, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691171, "dur": 22, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691197, "dur": 24, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691224, "dur": 28, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691254, "dur": 23, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691279, "dur": 1, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691281, "dur": 32, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691318, "dur": 32, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691356, "dur": 4, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691361, "dur": 27, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691390, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691391, "dur": 20, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691415, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691417, "dur": 20, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691440, "dur": 18, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691462, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691490, "dur": 21, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691515, "dur": 27, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691543, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691546, "dur": 20, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691570, "dur": 23, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691595, "dur": 21, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691618, "dur": 21, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691641, "dur": 35, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691679, "dur": 23, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691703, "dur": 2, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691706, "dur": 23, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691732, "dur": 21, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691754, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691756, "dur": 20, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691778, "dur": 22, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691803, "dur": 21, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691827, "dur": 20, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691850, "dur": 21, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691874, "dur": 19, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691897, "dur": 19, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691919, "dur": 36, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691957, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691990, "dur": 1, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130691991, "dur": 23, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692017, "dur": 20, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692039, "dur": 19, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692061, "dur": 22, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692085, "dur": 17, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692104, "dur": 19, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692126, "dur": 18, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692148, "dur": 39, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692189, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692217, "dur": 21, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692240, "dur": 1, "ph": "X", "name": "ProcessMessages 125", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692242, "dur": 28, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692274, "dur": 20, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692296, "dur": 22, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692320, "dur": 20, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692342, "dur": 17, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692361, "dur": 21, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692384, "dur": 21, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692408, "dur": 22, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692432, "dur": 20, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692454, "dur": 22, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692478, "dur": 20, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692501, "dur": 19, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692523, "dur": 20, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692546, "dur": 20, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692569, "dur": 21, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692592, "dur": 20, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692615, "dur": 19, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692636, "dur": 22, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692662, "dur": 23, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692687, "dur": 22, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692713, "dur": 19, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692734, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692736, "dur": 21, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692759, "dur": 20, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692781, "dur": 21, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692805, "dur": 22, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692828, "dur": 25, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692857, "dur": 22, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692882, "dur": 23, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692907, "dur": 21, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692933, "dur": 21, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692956, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130692978, "dur": 43, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693023, "dur": 2, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693026, "dur": 27, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693056, "dur": 23, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693081, "dur": 21, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693104, "dur": 21, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693128, "dur": 25, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693156, "dur": 1, "ph": "X", "name": "ProcessMessages 202", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693158, "dur": 45, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693205, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693230, "dur": 22, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693255, "dur": 20, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693277, "dur": 20, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693299, "dur": 21, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693323, "dur": 20, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693345, "dur": 21, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693368, "dur": 21, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693391, "dur": 20, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693413, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693439, "dur": 21, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693462, "dur": 19, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693483, "dur": 21, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693506, "dur": 21, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693530, "dur": 21, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693553, "dur": 19, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693609, "dur": 42, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693653, "dur": 2, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693656, "dur": 45, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693704, "dur": 3, "ph": "X", "name": "ProcessMessages 947", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693709, "dur": 31, "ph": "X", "name": "ReadAsync 947", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693741, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693743, "dur": 23, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693768, "dur": 20, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693791, "dur": 19, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693813, "dur": 68, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693883, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693922, "dur": 28, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693953, "dur": 21, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130693978, "dur": 30, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694009, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694011, "dur": 21, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694035, "dur": 32, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694071, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694073, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694105, "dur": 1, "ph": "X", "name": "ProcessMessages 862", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694108, "dur": 28, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694137, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694139, "dur": 26, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694168, "dur": 36, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694205, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694208, "dur": 23, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694233, "dur": 21, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694258, "dur": 29, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694289, "dur": 28, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694319, "dur": 19, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694339, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694341, "dur": 37, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694381, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694383, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694416, "dur": 1, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694419, "dur": 28, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694449, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694451, "dur": 31, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694484, "dur": 1, "ph": "X", "name": "ProcessMessages 749", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694485, "dur": 25, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694512, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694514, "dur": 27, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694542, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694544, "dur": 28, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694573, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694575, "dur": 21, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694599, "dur": 33, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694635, "dur": 23, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694659, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694664, "dur": 38, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694705, "dur": 29, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694736, "dur": 20, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694759, "dur": 27, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694789, "dur": 22, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694813, "dur": 20, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694835, "dur": 30, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694868, "dur": 22, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694892, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694894, "dur": 23, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694919, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694949, "dur": 22, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694974, "dur": 15, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130694990, "dur": 19, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695011, "dur": 19, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695033, "dur": 25, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695060, "dur": 10, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695072, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695099, "dur": 20, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695121, "dur": 19, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695142, "dur": 20, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695165, "dur": 25, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695192, "dur": 20, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695215, "dur": 21, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695238, "dur": 24, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695265, "dur": 21, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695288, "dur": 22, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695312, "dur": 26, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695339, "dur": 2, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695341, "dur": 38, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695383, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695385, "dur": 39, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695426, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695428, "dur": 28, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695458, "dur": 22, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695482, "dur": 22, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695506, "dur": 20, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695529, "dur": 16, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695547, "dur": 20, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695570, "dur": 23, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695595, "dur": 19, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695617, "dur": 20, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695639, "dur": 19, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695660, "dur": 2, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695663, "dur": 22, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695688, "dur": 24, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695716, "dur": 20, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695739, "dur": 26, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695767, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695769, "dur": 41, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695814, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695817, "dur": 32, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695852, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695854, "dur": 30, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695886, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695889, "dur": 26, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695917, "dur": 33, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695954, "dur": 2, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130695959, "dur": 40, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696001, "dur": 1, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696003, "dur": 33, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696038, "dur": 1, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696040, "dur": 37, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696078, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696080, "dur": 31, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696113, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696114, "dur": 40, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696159, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696209, "dur": 3, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696213, "dur": 34, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696249, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696251, "dur": 77, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696331, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696369, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696371, "dur": 34, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696407, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696408, "dur": 67, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696479, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696513, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696515, "dur": 24, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696542, "dur": 75, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696620, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696677, "dur": 2, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696680, "dur": 40, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696722, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696725, "dur": 40, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696768, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696811, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696813, "dur": 44, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696859, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696861, "dur": 67, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696940, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130696944, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130697008, "dur": 2, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130697010, "dur": 31, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130697045, "dur": 124, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130697172, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130697216, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130697218, "dur": 36, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130697256, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130697258, "dur": 31, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130697293, "dur": 59, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130697355, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130697403, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130697405, "dur": 54, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130697461, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130697464, "dur": 40, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130697507, "dur": 172, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130697681, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130697683, "dur": 53, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130697737, "dur": 3, "ph": "X", "name": "ProcessMessages 1732", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130697741, "dur": 46, "ph": "X", "name": "ReadAsync 1732", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130697790, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130697793, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130697825, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130697826, "dur": 24, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130697854, "dur": 30, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130697887, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130697888, "dur": 55, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130697945, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130697977, "dur": 27, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698006, "dur": 74, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698083, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698118, "dur": 27, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698147, "dur": 22, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698172, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698174, "dur": 50, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698226, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698251, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698252, "dur": 19, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698274, "dur": 21, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698298, "dur": 68, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698369, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698371, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698408, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698411, "dur": 37, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698450, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698451, "dur": 56, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698511, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698542, "dur": 27, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698572, "dur": 22, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698596, "dur": 61, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698660, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698684, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698686, "dur": 28, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698716, "dur": 19, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698737, "dur": 18, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698758, "dur": 59, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698818, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698820, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698849, "dur": 21, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698873, "dur": 20, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698895, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698896, "dur": 67, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698965, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130698979, "dur": 20, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699001, "dur": 1, "ph": "X", "name": "ProcessMessages 125", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699004, "dur": 29, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699036, "dur": 22, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699059, "dur": 25, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699087, "dur": 19, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699107, "dur": 68, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699178, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699207, "dur": 19, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699228, "dur": 21, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699252, "dur": 24, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699277, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699278, "dur": 23, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699305, "dur": 21, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699331, "dur": 31, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699364, "dur": 20, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699389, "dur": 122, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699516, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699518, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699581, "dur": 1, "ph": "X", "name": "ProcessMessages 1029", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699584, "dur": 46, "ph": "X", "name": "ReadAsync 1029", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699632, "dur": 25, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699660, "dur": 20, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699682, "dur": 22, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699707, "dur": 22, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699731, "dur": 20, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699753, "dur": 22, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699777, "dur": 19, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699798, "dur": 21, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699821, "dur": 88, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699914, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699969, "dur": 1, "ph": "X", "name": "ProcessMessages 844", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130699971, "dur": 39, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700012, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700014, "dur": 52, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700068, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700093, "dur": 19, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700114, "dur": 21, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700138, "dur": 19, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700159, "dur": 19, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700180, "dur": 19, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700202, "dur": 17, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700220, "dur": 17, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700239, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700262, "dur": 71, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700335, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700364, "dur": 11, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700377, "dur": 18, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700396, "dur": 29, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700427, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700430, "dur": 84, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700518, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700554, "dur": 64, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700619, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700621, "dur": 32, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700655, "dur": 36, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700696, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700733, "dur": 59, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700794, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700796, "dur": 44, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700841, "dur": 1, "ph": "X", "name": "ProcessMessages 1394", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700843, "dur": 19, "ph": "X", "name": "ReadAsync 1394", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700865, "dur": 21, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700889, "dur": 18, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700909, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700912, "dur": 83, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130700997, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701022, "dur": 21, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701044, "dur": 25, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701072, "dur": 59, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701135, "dur": 33, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701169, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701255, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701259, "dur": 37, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701297, "dur": 1, "ph": "X", "name": "ProcessMessages 641", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701299, "dur": 44, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701345, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701372, "dur": 18, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701392, "dur": 37, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701433, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701435, "dur": 38, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701476, "dur": 1, "ph": "X", "name": "ProcessMessages 146", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701478, "dur": 46, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701526, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701561, "dur": 27, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701591, "dur": 20, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701613, "dur": 74, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701690, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701712, "dur": 23, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701737, "dur": 19, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701759, "dur": 21, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701782, "dur": 21, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701805, "dur": 73, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701880, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701909, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701912, "dur": 28, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701942, "dur": 21, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130701965, "dur": 66, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702034, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702064, "dur": 24, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702089, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702091, "dur": 19, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702112, "dur": 74, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702188, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702214, "dur": 43, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702261, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702263, "dur": 31, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702297, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702299, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702326, "dur": 62, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702390, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702418, "dur": 24, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702445, "dur": 21, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702470, "dur": 55, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702527, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702552, "dur": 25, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702580, "dur": 25, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702607, "dur": 63, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702674, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702723, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702725, "dur": 34, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702760, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702762, "dur": 68, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702833, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702870, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702872, "dur": 19, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702894, "dur": 54, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702951, "dur": 20, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130702972, "dur": 38, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703013, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703035, "dur": 23, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703060, "dur": 20, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703083, "dur": 67, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703151, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703176, "dur": 18, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703197, "dur": 21, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703219, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703221, "dur": 17, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703241, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703297, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703325, "dur": 20, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703348, "dur": 23, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703373, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703375, "dur": 15, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703392, "dur": 62, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703456, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703491, "dur": 27, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703521, "dur": 18, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703542, "dur": 66, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703610, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703636, "dur": 22, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703660, "dur": 21, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703683, "dur": 76, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703761, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703777, "dur": 20, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703800, "dur": 27, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703830, "dur": 66, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703898, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703925, "dur": 22, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703950, "dur": 28, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703980, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130703984, "dur": 55, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704042, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704081, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704083, "dur": 36, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704121, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704123, "dur": 28, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704154, "dur": 61, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704217, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704245, "dur": 25, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704272, "dur": 28, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704301, "dur": 2, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704304, "dur": 90, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704400, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704403, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704454, "dur": 2, "ph": "X", "name": "ProcessMessages 1227", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704457, "dur": 48, "ph": "X", "name": "ReadAsync 1227", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704509, "dur": 43, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704553, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704556, "dur": 29, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704587, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704588, "dur": 49, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704641, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704675, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704679, "dur": 34, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704715, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704717, "dur": 31, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704750, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704751, "dur": 54, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704809, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704844, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704846, "dur": 33, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704882, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704883, "dur": 26, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704912, "dur": 52, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130704967, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705004, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705006, "dur": 46, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705053, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705055, "dur": 60, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705118, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705145, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705147, "dur": 37, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705185, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705186, "dur": 35, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705223, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705226, "dur": 36, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705263, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705265, "dur": 91, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705360, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705363, "dur": 60, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705426, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705429, "dur": 44, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705475, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705477, "dur": 37, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705516, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705518, "dur": 39, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705558, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705561, "dur": 38, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705600, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705602, "dur": 35, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705639, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705641, "dur": 31, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705673, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705675, "dur": 71, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705749, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705781, "dur": 43, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705827, "dur": 1, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705830, "dur": 51, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705883, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705885, "dur": 31, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705917, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705919, "dur": 22, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705944, "dur": 21, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705967, "dur": 21, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130705991, "dur": 102, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706097, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706100, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706146, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706148, "dur": 160, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706314, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706345, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706349, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706387, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706391, "dur": 33, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706426, "dur": 2, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706429, "dur": 29, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706461, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706463, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706500, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706503, "dur": 49, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706555, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706558, "dur": 52, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706613, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706617, "dur": 45, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706664, "dur": 2, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706667, "dur": 37, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706708, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706712, "dur": 37, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706751, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706754, "dur": 30, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706786, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706788, "dur": 40, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706830, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706832, "dur": 38, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706877, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706908, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130706990, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130707029, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130707031, "dur": 34, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130707068, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130707070, "dur": 34, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130707107, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130707109, "dur": 30, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130707142, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130707144, "dur": 30, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130707176, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130707178, "dur": 93, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130707275, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130707312, "dur": 2, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130707315, "dur": 536, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130707853, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130707856, "dur": 82, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130707940, "dur": 17, "ph": "X", "name": "ProcessMessages 2528", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130707960, "dur": 40, "ph": "X", "name": "ReadAsync 2528", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708002, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708005, "dur": 37, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708045, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708048, "dur": 32, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708082, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708086, "dur": 32, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708121, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708123, "dur": 42, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708168, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708171, "dur": 27, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708201, "dur": 2, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708204, "dur": 40, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708248, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708251, "dur": 37, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708290, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708293, "dur": 43, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708339, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708343, "dur": 30, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708375, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708378, "dur": 33, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708413, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708415, "dur": 25, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708444, "dur": 116, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708564, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708604, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708607, "dur": 34, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708643, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708646, "dur": 31, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708679, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708682, "dur": 27, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708712, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708714, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130708743, "dur": 2127, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130710874, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130710876, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130710914, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130710916, "dur": 508, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130711426, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130711429, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130711459, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130711460, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130711490, "dur": 154, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130711648, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130711679, "dur": 2843, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130714526, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130714529, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130714569, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130714595, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130714597, "dur": 71, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130714669, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130714690, "dur": 267, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130714959, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130714975, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130715005, "dur": 193, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130715202, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130715235, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130715237, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130715263, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130715265, "dur": 34, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130715303, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130715331, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130715333, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130715356, "dur": 154, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130715514, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130715538, "dur": 189, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130715729, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130715754, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130715803, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130715827, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130715849, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130715874, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130715898, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130715930, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130715954, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130715956, "dur": 18, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130715976, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130716075, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130716097, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130716117, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130716167, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130716191, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130716193, "dur": 20, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130716216, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130716244, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130716269, "dur": 122, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130716396, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130716432, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130716433, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130716470, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130716472, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130716515, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130716553, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130716555, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130716594, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130716653, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130716677, "dur": 106, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130716787, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130716806, "dur": 142, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130716953, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130716992, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130716994, "dur": 37, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130717034, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130717036, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130717063, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130717065, "dur": 21, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130717089, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130717118, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130717120, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130717145, "dur": 20, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130717166, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130717169, "dur": 39, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130717211, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130717248, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130717250, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130717295, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130717327, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130717360, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130717423, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130717455, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130717457, "dur": 63, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130717523, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130717555, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130717557, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130717582, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130717610, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130717633, "dur": 191, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130717827, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130717856, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130717892, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130717893, "dur": 155, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130718052, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130718053, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130718079, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130718106, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130718160, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130718187, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130718213, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130718215, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130718269, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130718291, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130718317, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130718357, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130718376, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130718422, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130718444, "dur": 65, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130718511, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130718514, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130718538, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130718577, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130718605, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130718607, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130718629, "dur": 153, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130718783, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130718812, "dur": 168, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130718985, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130719017, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130719019, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130719048, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130719049, "dur": 49, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130719101, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130719103, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130719128, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130719132, "dur": 127, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130719263, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130719288, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130719290, "dur": 115, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130719409, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130719434, "dur": 26, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130719464, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130719506, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130719535, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130719537, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130719560, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130719583, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130719605, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130719625, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130719694, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130719716, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130719738, "dur": 546, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130720289, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130720308, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130720309, "dur": 90, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130720401, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130720403, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130720438, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130720441, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130720481, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130720511, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130720513, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130720545, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130720572, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130720627, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130720649, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130720689, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130720718, "dur": 168, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130720891, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130720920, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130720942, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130721002, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130721027, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130721070, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130721089, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130721112, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130721195, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130721222, "dur": 113, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130721339, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130721362, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130721387, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130721401, "dur": 263, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130721666, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130721694, "dur": 75, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130721771, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130721791, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130721813, "dur": 185, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130722002, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130722031, "dur": 407, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130722441, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130722475, "dur": 99, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130722576, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130722600, "dur": 1179, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130723784, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130723787, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130723836, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130723838, "dur": 65, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130723907, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130723934, "dur": 636, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130724574, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130724621, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130724624, "dur": 122, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130724749, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130724751, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130724793, "dur": 96, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130724892, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130724894, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130724933, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130724936, "dur": 124, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130725063, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130725082, "dur": 391, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130725478, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130725511, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130725514, "dur": 56497, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130782021, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130782026, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130782087, "dur": 31, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130782120, "dur": 4089, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130786222, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130786227, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130786264, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130786266, "dur": 52, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130786324, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130786354, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130786356, "dur": 131, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130786491, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130786493, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130786528, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130786551, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130786554, "dur": 16, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130786573, "dur": 1858, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130788436, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130788439, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130788466, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130788510, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130788539, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130788571, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130788602, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130788603, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130788627, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130788655, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130788677, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130788773, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130788801, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130788803, "dur": 384, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130789190, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130789242, "dur": 576, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130789823, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130789846, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130789848, "dur": 1113, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130790965, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130790991, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130790994, "dur": 71, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130791068, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130791091, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130791093, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130791116, "dur": 255, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130791374, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130791410, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130791437, "dur": 258, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130791700, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130791725, "dur": 188, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130791915, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130791938, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130791996, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130792032, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130792033, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130792061, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130792142, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130792164, "dur": 1047, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793215, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793256, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793302, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793304, "dur": 27, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793334, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793359, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793388, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793419, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793420, "dur": 22, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793446, "dur": 18, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793467, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793488, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793517, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793519, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793550, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793552, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793575, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793594, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793623, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793625, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793650, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793681, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793705, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793731, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793733, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793769, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793771, "dur": 24, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793797, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793799, "dur": 21, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793822, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793825, "dur": 30, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793857, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793859, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793882, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793884, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793907, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793909, "dur": 26, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793936, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793937, "dur": 21, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793960, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793962, "dur": 29, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793992, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130793994, "dur": 29, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130794026, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130794028, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130794050, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130794098, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130794100, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130794122, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130794143, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130794169, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130794220, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130794251, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130794253, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130794276, "dur": 27, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130794306, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130794329, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130794354, "dur": 78, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130794434, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130794464, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061130794466, "dur": 247564, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061131042039, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061131042043, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061131042130, "dur": 39, "ph": "X", "name": "ReadAsync 9335", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061131042172, "dur": 21, "ph": "X", "name": "ProcessMessages 2894", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061131042194, "dur": 36720, "ph": "X", "name": "ReadAsync 2894", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061131078932, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061131078939, "dur": 101, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061131079045, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061131079050, "dur": 164724, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061131243786, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061131243792, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061131243843, "dur": 38, "ph": "X", "name": "ProcessMessages 1133", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061131243883, "dur": 15910, "ph": "X", "name": "ReadAsync 1133", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061131259805, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061131259810, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061131259878, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061131259886, "dur": 2071025, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061133330921, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061133330927, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061133331018, "dur": 34, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061133331056, "dur": 5286, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061133336350, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061133336354, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061133336425, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061133336434, "dur": 1516, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061133337956, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061133337963, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061133338009, "dur": 32, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061133338044, "dur": 163795, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061133501855, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061133501861, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061133501922, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061133501930, "dur": 888, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061133502824, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061133502827, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061133502875, "dur": 34, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061133502911, "dur": 1497, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061133504414, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061133504418, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061133504459, "dur": 2, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 2208, "tid": 150323855360, "ts": 1750061133504463, "dur": 5963, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 2208, "tid": 163, "ts": 1750061133511119, "dur": 4562, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 2208, "tid": 146028888064, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 2208, "tid": 146028888064, "ts": 1750061130663747, "dur": 65397, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 2208, "tid": 146028888064, "ts": 1750061130729145, "dur": 2, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 2208, "tid": 146028888064, "ts": 1750061130729147, "dur": 72, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 2208, "tid": 163, "ts": 1750061133515685, "dur": 14, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 2208, "tid": 141733920768, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 2208, "tid": 141733920768, "ts": 1750061130660560, "dur": 2849922, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 2208, "tid": 141733920768, "ts": 1750061130660720, "dur": 2674, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 2208, "tid": 141733920768, "ts": 1750061133510489, "dur": 55, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 2208, "tid": 141733920768, "ts": 1750061133510502, "dur": 24, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 2208, "tid": 141733920768, "ts": 1750061133510545, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 2208, "tid": 163, "ts": 1750061133515705, "dur": 21, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1750061130679592, "dur": 54, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750061130679676, "dur": 1510, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750061130681196, "dur": 1068, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750061130682461, "dur": 127, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1750061130682588, "dur": 605, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750061130683308, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_025D68ED9798F53F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750061130683215, "dur": 22917, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750061130706148, "dur": 2796788, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750061133502938, "dur": 412, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750061133504366, "dur": 55, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750061133504441, "dur": 884, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1750061130683697, "dur": 22471, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130706208, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130706460, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_42BE61A2F375B465.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750061130706512, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130706715, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_13AFD6B0BB3B8686.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750061130706960, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_3EE9BB20E930FDFD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750061130707402, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750061130707931, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750061130708106, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750061130708488, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130708728, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130708971, "dur": 145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130709116, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130709336, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130709628, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130709832, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130710047, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130710225, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130710728, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130710913, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130711582, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130711801, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130712002, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130712187, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130712372, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130712565, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130712758, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130712947, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130713138, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130713317, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130713512, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130713701, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130713901, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130714199, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130714589, "dur": 724, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130715314, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750061130715550, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130715777, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750061130716230, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130716301, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130716437, "dur": 604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130717041, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130717223, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130717379, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750061130717574, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750061130718125, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130718254, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130718467, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130718874, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750061130719040, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750061130719297, "dur": 337, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130719658, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750061130719782, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750061130720349, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750061130720447, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750061130720745, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130720972, "dur": 3707, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130724680, "dur": 59434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130784116, "dur": 2103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750061130786254, "dur": 2166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750061130788460, "dur": 2158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750061130790620, "dur": 1114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061130791746, "dur": 2547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750061130794335, "dur": 2708615, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130684067, "dur": 22218, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130706293, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_BE0B22B212C39490.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750061130706476, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_4B4D0A166E0D20DC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750061130706589, "dur": 187, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_94280B12CFD84F1A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750061130707655, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750061130707797, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750061130707940, "dur": 259, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1750061130708243, "dur": 179, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750061130708450, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130708682, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130708906, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130709160, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130709499, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130709694, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130709888, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130710075, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130710248, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130710733, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130710958, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130711173, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130711352, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130711603, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130711807, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130712124, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130712327, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130712519, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130712705, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130713268, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130713469, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130713673, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130713856, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130714175, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130714586, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130715072, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750061130715281, "dur": 674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750061130716010, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750061130716218, "dur": 569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750061130716787, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130717107, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750061130717250, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750061130717583, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130717657, "dur": 613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130718271, "dur": 613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130718884, "dur": 5786, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130724671, "dur": 6749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130731422, "dur": 52703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130784129, "dur": 2004, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750061130786135, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130786210, "dur": 2120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750061130788331, "dur": 477, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130788820, "dur": 2249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750061130791119, "dur": 2091, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750061130793351, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130793501, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130793704, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130794024, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061130794300, "dur": 2708639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130683754, "dur": 22433, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130706208, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130706610, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130706945, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_1DB8604BF3C2B15B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750061130707656, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750061130707851, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1750061130708075, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750061130708196, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750061130708424, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130708685, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130709004, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130709222, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130709437, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130709762, "dur": 913, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@e07c4c789cd8\\Editor\\Models\\Deprecated\\PositionSDFDeprecated.cs"}}, {"pid": 12345, "tid": 3, "ts": 1750061130709681, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130710783, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130710987, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130711207, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130711389, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130711588, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130711840, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130712035, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130712212, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130712416, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130712597, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130712799, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130713009, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130713216, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130713403, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130713608, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130713786, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130714179, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130714585, "dur": 507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130715094, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750061130715283, "dur": 558, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130715850, "dur": 552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750061130716403, "dur": 734, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130717229, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130717378, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750061130717571, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750061130718181, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130718314, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130718624, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130718880, "dur": 2384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130721266, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750061130721435, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750061130721850, "dur": 2843, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130724693, "dur": 59424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130784124, "dur": 2021, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750061130786222, "dur": 2450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750061130788702, "dur": 2370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750061130791121, "dur": 2858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750061130794040, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061130794191, "dur": 2541916, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061133336140, "dur": 165, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1750061133336110, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1750061133336354, "dur": 1623, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1750061133337979, "dur": 164979, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130683816, "dur": 22383, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130706207, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_1BD15D364A91220E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750061130706569, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_1BD15D364A91220E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750061130706703, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_7B75EE72BE5BAF62.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750061130706914, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_7D559772BEA705DB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750061130707373, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750061130707934, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750061130708174, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1750061130708371, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750061130708458, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130708665, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130708919, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130709132, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130709329, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130709601, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130709855, "dur": 819, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@e07c4c789cd8\\Editor\\GraphView\\Elements\\VFXElement.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750061130709842, "dur": 993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130710835, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130711286, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130711551, "dur": 823, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Flow\\Units\\UnitEditor.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750061130711504, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130712495, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130712683, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130713000, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130713191, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130713403, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130713585, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130713797, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130713985, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130714268, "dur": 331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130714600, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130715073, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750061130715306, "dur": 522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750061130715829, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130715935, "dur": 700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750061130716636, "dur": 380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130717039, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130717217, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130717389, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130717672, "dur": 576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130718248, "dur": 618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130718867, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750061130719058, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750061130719473, "dur": 5191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130724680, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750061130724945, "dur": 61403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130786350, "dur": 2387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750061130788738, "dur": 1113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061130789864, "dur": 2060, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750061130791968, "dur": 2298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750061130794310, "dur": 2708624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130683864, "dur": 22353, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130706224, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_D44F1D7947A8470C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750061130706472, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130706952, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_8D701D583C70E786.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750061130707868, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1750061130707931, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1750061130708242, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1750061130708454, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130708692, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130708884, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130709110, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130709347, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130709639, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130709854, "dur": 742, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@e07c4c789cd8\\Editor\\GraphView\\Elements\\Controllers\\VFXDataAnchorController.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750061130709853, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130710780, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130710949, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130711130, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130711293, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130711501, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130711693, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130712006, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130712219, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130712414, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130712599, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130712790, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130712970, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130713167, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130713353, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130713576, "dur": 605, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Views\\Merge\\Developer\\DirectoryConflicts\\CycleMoveMenu.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750061130713529, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130714311, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130714645, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130715077, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750061130715272, "dur": 923, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750061130716231, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750061130716463, "dur": 803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750061130717267, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130717482, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130717677, "dur": 543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130718242, "dur": 641, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130718883, "dur": 5777, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130724666, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750061130724935, "dur": 59224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130784163, "dur": 1995, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750061130786233, "dur": 5144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750061130791420, "dur": 2246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750061130793667, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130793853, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130794011, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1750061130794065, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061130794498, "dur": 2708487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130683906, "dur": 22324, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130706237, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_1C3753EAD72C002A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750061130706348, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130706472, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130706581, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_5505F097F171DDCB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750061130706708, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_B34AAB5B4FEDF5AF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750061130706932, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_136791916CB4C4E9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750061130707500, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750061130707676, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1750061130707926, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1750061130708244, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750061130708459, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130708688, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130708902, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130709170, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130709440, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130709696, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130709910, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130710089, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130710269, "dur": 936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130711205, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130711408, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130711586, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130711786, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130711981, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130712193, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130712389, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130712582, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130712876, "dur": 684, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\AndHandler.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750061130712773, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130713655, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130713882, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130714212, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130714592, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130715089, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750061130715302, "dur": 1778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750061130717150, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750061130717327, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750061130717842, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130717910, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130718245, "dur": 627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130718873, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750061130719068, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750061130719608, "dur": 5068, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130724677, "dur": 59443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130784122, "dur": 2037, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750061130786192, "dur": 388, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750061130786583, "dur": 1965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750061130788597, "dur": 2374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750061130791018, "dur": 2247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750061130793408, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130793652, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130793880, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750061130794032, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061130794337, "dur": 2708623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130683972, "dur": 22272, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130706251, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_3F8B9D82C8E678D4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750061130706448, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130707019, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_F75569858C7FEC6C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750061130707705, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750061130707935, "dur": 287, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1750061130708237, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750061130708446, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130708637, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16285879237017002407.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750061130708698, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130708964, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130709242, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130709545, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130709794, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130710004, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130710190, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130710666, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130710863, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130711046, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130711235, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130711412, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130711678, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130711882, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130712069, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130712275, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130712472, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130712660, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130712849, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130713038, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130713224, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130713427, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130713682, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130713905, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130714230, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130714602, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130715095, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750061130715295, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750061130715359, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750061130715561, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750061130716140, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130716276, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750061130716477, "dur": 975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750061130717492, "dur": 424, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130717921, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130718236, "dur": 639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130718875, "dur": 1725, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130720602, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750061130720713, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750061130721257, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750061130721384, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750061130721729, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750061130721820, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750061130722054, "dur": 2654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130724708, "dur": 59442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130784155, "dur": 2001, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750061130786195, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750061130786363, "dur": 2277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750061130788641, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130788698, "dur": 2274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750061130791010, "dur": 2485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750061130793497, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130793608, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130793937, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1750061130794042, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061130794142, "dur": 450946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061131245089, "dur": 2257873, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130684026, "dur": 22241, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130706280, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_D43581CE70417CB3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750061130706970, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_E3FF4E8988E07430.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750061130707097, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_E3FF4E8988E07430.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750061130707810, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750061130707914, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1750061130708185, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750061130708382, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130708672, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130708887, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130709290, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130709921, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130710100, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130710579, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130710775, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130710992, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130711175, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130711366, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130711795, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130711987, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130712185, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130712444, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130712635, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130712856, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130713045, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130713230, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130713615, "dur": 570, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\half3.gen.cs"}}, {"pid": 12345, "tid": 8, "ts": 1750061130713421, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130714200, "dur": 395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130714595, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130715064, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750061130715225, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130715382, "dur": 817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750061130716273, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750061130716616, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750061130717077, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130717376, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750061130717564, "dur": 596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750061130718219, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": 1750061130718748, "dur": 66, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130719155, "dur": 62860, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": 1750061130784115, "dur": 2022, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750061130786138, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130786225, "dur": 2221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750061130788447, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130788650, "dur": 2255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750061130790905, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130791144, "dur": 2414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750061130793559, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130793878, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130793979, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1750061130794055, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061130794384, "dur": 2708572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130684105, "dur": 22203, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130706319, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_76ADE6F2C4A0FDCA.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750061130706413, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_5D5EF2E9E0015A01.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750061130706592, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130706899, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130707622, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750061130707721, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750061130707928, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750061130708042, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750061130708179, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750061130708465, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130708709, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130708926, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130709135, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130709323, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130709610, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130709820, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130710022, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130710205, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130710715, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130710898, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130711578, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130711786, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130711973, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130712157, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130712349, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130712928, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130713136, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130713322, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130713518, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130713761, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130713963, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130714254, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130714601, "dur": 473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130715075, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750061130715285, "dur": 504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750061130715790, "dur": 507, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130716307, "dur": 508, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130716826, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750061130716951, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130717007, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750061130717418, "dur": 673, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130718126, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750061130718351, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750061130718742, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130719043, "dur": 5638, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130724682, "dur": 59509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130784196, "dur": 2281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750061130786478, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130786600, "dur": 1952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750061130788553, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130788645, "dur": 2313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750061130791014, "dur": 2248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750061130793262, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130793583, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130793774, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Editor.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1750061130793866, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061130794012, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1750061130794069, "dur": 250241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061131044330, "dur": 212777, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750061131044312, "dur": 214277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750061131259602, "dur": 174, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061131260084, "dur": 2070830, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750061133336108, "dur": 165618, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750061133336101, "dur": 165627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1750061133501760, "dur": 1092, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1750061130684128, "dur": 22195, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130706329, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_49F05BCF866287C3.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750061130706696, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_77C9B2F6CDEA9940.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750061130706896, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_1E95AC98D1BD4446.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750061130707590, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750061130707740, "dur": 3127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750061130710947, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130711159, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130711332, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130711514, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130711747, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130711983, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130712197, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130712407, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130712620, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130712842, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130713036, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130713206, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130713391, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130713625, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130713854, "dur": 122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130713976, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130714239, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130714594, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130715083, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750061130715296, "dur": 574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750061130715871, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130716008, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750061130716161, "dur": 767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750061130717062, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130717227, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130717381, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130717597, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750061130717850, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130718091, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750061130718561, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130718646, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130718875, "dur": 1688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130720564, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750061130720668, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750061130720943, "dur": 3763, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130724707, "dur": 59435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130784147, "dur": 2052, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750061130786201, "dur": 325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130786536, "dur": 2049, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750061130788586, "dur": 635, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061130789233, "dur": 2916, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750061130792190, "dur": 2244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750061130794490, "dur": 2708475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130684163, "dur": 22172, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130706453, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_B6C5AA3F81E3AFD4.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750061130706650, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_88EB000108787B7B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750061130706786, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_4E7CBDA22A1B8217.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750061130706979, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_C9BF491CE9429A6C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750061130707944, "dur": 232, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1750061130708451, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130708736, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130709155, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130709373, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130709583, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130709784, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130710005, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130710189, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130710691, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130710882, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130711059, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130711242, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130711433, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130711625, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130711812, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130712063, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130712276, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130712487, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130712665, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130712861, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130713059, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130713245, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130713425, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130713640, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130713866, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130714202, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130714652, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130715087, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750061130715299, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750061130715984, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130716214, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130716300, "dur": 799, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130717099, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130717224, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130717396, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130717591, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750061130717897, "dur": 1315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750061130719213, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130719338, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750061130719495, "dur": 1048, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750061130720598, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750061130720751, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750061130721146, "dur": 3528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130724675, "dur": 61579, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130786258, "dur": 4910, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1750061130791169, "dur": 913, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061130792089, "dur": 2151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1750061130794278, "dur": 2708654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130684203, "dur": 22322, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130706531, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_A84A75205A4D3666.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750061130706657, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_9DD7D897C42E415F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750061130706989, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_9CC28255281D0D3B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750061130707628, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750061130707726, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1750061130707932, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1750061130708270, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1750061130708436, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130708637, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750061130708758, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130709079, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130709304, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130709550, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130709745, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130709983, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130710166, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130710638, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130710845, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130711025, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130711203, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130711394, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130711605, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130711813, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130712026, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130712213, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130712806, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130713011, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130713222, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130713410, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130713618, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130713800, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130714178, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130714635, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130715095, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750061130715293, "dur": 2057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750061130717393, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130717590, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750061130717867, "dur": 798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750061130718666, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130718867, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750061130719061, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750061130719577, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750061130719742, "dur": 738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750061130720558, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750061130720700, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061130721049, "dur": 1403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750061130722508, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750061130722627, "dur": 1144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750061130723818, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750061130723945, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750061130724660, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750061130724783, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750061130725107, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750061130725587, "dur": 317387, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750061131044610, "dur": 33466, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 12, "ts": 1750061131044307, "dur": 33877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750061131078772, "dur": 106, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061131079202, "dur": 164577, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750061131245084, "dur": 2257883, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130684236, "dur": 22300, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130706593, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_7461CA82F1A7BC76.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750061130706901, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130707062, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_214806B231DDE102.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750061130707548, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750061130707601, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750061130707814, "dur": 204, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750061130708181, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750061130708267, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750061130708371, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750061130708476, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130708704, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130709135, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130709352, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130709599, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130709817, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130709998, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130710187, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130710856, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130711042, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130711220, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130711426, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130711614, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130711834, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130712046, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130712231, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130712436, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130712615, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130712801, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130713012, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130713187, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130713380, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130713559, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130713787, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130714313, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130714590, "dur": 701, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130715292, "dur": 1378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750061130716698, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750061130717210, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130717394, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130717666, "dur": 585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130718251, "dur": 632, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130718883, "dur": 5785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130724668, "dur": 4607, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130730555, "dur": 159, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/6000.0.50f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 13, "ts": 1750061130730715, "dur": 666, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/6000.0.50f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 13, "ts": 1750061130729277, "dur": 2138, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130731416, "dur": 52716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130784140, "dur": 2416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750061130786605, "dur": 2014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750061130788620, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130788688, "dur": 2166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750061130790855, "dur": 596, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130791456, "dur": 2283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750061130793817, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130794028, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061130794301, "dur": 2708647, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130684269, "dur": 22330, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130706908, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_E6B906AFC5CA6A31.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750061130707574, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1750061130707932, "dur": 221, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1750061130708240, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1750061130708459, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130708674, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130708900, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130709183, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130709462, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130709700, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130709988, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130710170, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130710649, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130710886, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130711073, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130711276, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130711496, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130711737, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130711928, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130712105, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130712301, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130712495, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130712687, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130712907, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130713107, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130713299, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130713502, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130713736, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130713941, "dur": 373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130714315, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130714587, "dur": 480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130715069, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750061130715237, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130715288, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750061130715348, "dur": 839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750061130716250, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750061130716460, "dur": 1095, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750061130717620, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750061130717912, "dur": 506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750061130718469, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130718874, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130719342, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750061130719501, "dur": 1001, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750061130720549, "dur": 4123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130724673, "dur": 59449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130784124, "dur": 2034, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750061130786214, "dur": 2278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750061130788493, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130788554, "dur": 2366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750061130790921, "dur": 1117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061130792054, "dur": 2206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750061130794312, "dur": 2708630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061130684313, "dur": 22326, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061130706924, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_1E5BE8A4F6F01B45.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750061130707470, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750061130707661, "dur": 3768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750061130711496, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750061130711691, "dur": 2829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750061130714634, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750061130714717, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750061130715063, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750061130715238, "dur": 650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750061130715920, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750061130716226, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750061130716469, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750061130716960, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061130717211, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061130717271, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061130717380, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061130717595, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750061130717926, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061130718089, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750061130718643, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061130718869, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750061130719074, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750061130719469, "dur": 5235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061130724704, "dur": 61499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061130786208, "dur": 2231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750061130788441, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061130788685, "dur": 2419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750061130791157, "dur": 2614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750061130793941, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1750061130794025, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061130794287, "dur": 2708643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130684347, "dur": 22325, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130707596, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130707730, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130707906, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1750061130708062, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750061130708406, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130708698, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130708982, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130709785, "dur": 817, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@e07c4c789cd8\\Editor\\TemplateWindow\\VFXTemplateWindow.cs"}}, {"pid": 12345, "tid": 16, "ts": 1750061130709452, "dur": 1190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130710643, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130710851, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130711087, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130711291, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130711485, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130711735, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130711961, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130712532, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130712729, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130712935, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130713138, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130713316, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130713565, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130713754, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130713944, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130714309, "dur": 330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130714640, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130715091, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750061130715293, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750061130715375, "dur": 678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1750061130716054, "dur": 498, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130716596, "dur": 418, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130717036, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130717221, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130717382, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130717601, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750061130717891, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1750061130718347, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130718404, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130718881, "dur": 5814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130724696, "dur": 59476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130784178, "dur": 2365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750061130786588, "dur": 2001, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750061130788619, "dur": 2246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750061130790866, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061130791030, "dur": 3318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750061130794377, "dur": 2708595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750061133509103, "dur": 1059, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 2208, "tid": 163, "ts": 1750061133515765, "dur": 23, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 2208, "tid": 163, "ts": 1750061133515829, "dur": 3973, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 2208, "tid": 163, "ts": 1750061133511062, "dur": 8795, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}