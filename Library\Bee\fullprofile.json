{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 2208, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 2208, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 2208, "tid": 173, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 2208, "tid": 173, "ts": 1750061413332330, "dur": 11, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 2208, "tid": 173, "ts": 1750061413332355, "dur": 4, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 2208, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 2208, "tid": 1, "ts": 1750061413101006, "dur": 10932, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 2208, "tid": 1, "ts": 1750061413111944, "dur": 86982, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 2208, "tid": 1, "ts": 1750061413198928, "dur": 27235, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 2208, "tid": 173, "ts": 1750061413332362, "dur": 13, "ph": "X", "name": "", "args": {}}, {"pid": 2208, "tid": 163208757248, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413100906, "dur": 14298, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413115206, "dur": 216678, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413115214, "dur": 541, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413115836, "dur": 277, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413116115, "dur": 1779, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413117899, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413117902, "dur": 100, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413118003, "dur": 6, "ph": "X", "name": "ProcessMessages 5214", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413118010, "dur": 35, "ph": "X", "name": "ReadAsync 5214", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413118047, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413118049, "dur": 85, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413118137, "dur": 1, "ph": "X", "name": "ProcessMessages 1204", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413118139, "dur": 37, "ph": "X", "name": "ReadAsync 1204", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413118177, "dur": 1, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413118207, "dur": 42, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413118250, "dur": 1, "ph": "X", "name": "ProcessMessages 1315", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413118252, "dur": 26, "ph": "X", "name": "ReadAsync 1315", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413118340, "dur": 74, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413118416, "dur": 1, "ph": "X", "name": "ProcessMessages 1509", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413118417, "dur": 60, "ph": "X", "name": "ReadAsync 1509", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413118479, "dur": 1, "ph": "X", "name": "ProcessMessages 1065", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413118480, "dur": 45, "ph": "X", "name": "ReadAsync 1065", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413118530, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413118532, "dur": 51, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413118640, "dur": 1, "ph": "X", "name": "ProcessMessages 1265", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413118643, "dur": 92, "ph": "X", "name": "ReadAsync 1265", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413118764, "dur": 2, "ph": "X", "name": "ProcessMessages 2571", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413118766, "dur": 63, "ph": "X", "name": "ReadAsync 2571", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413118904, "dur": 1, "ph": "X", "name": "ProcessMessages 1104", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413118906, "dur": 43, "ph": "X", "name": "ReadAsync 1104", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413118951, "dur": 1, "ph": "X", "name": "ProcessMessages 2340", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413118953, "dur": 23, "ph": "X", "name": "ReadAsync 2340", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413118978, "dur": 21, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413119001, "dur": 21, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413119024, "dur": 24, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413119050, "dur": 20, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413119072, "dur": 17, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413119091, "dur": 66, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413119234, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413119236, "dur": 88, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413119325, "dur": 2, "ph": "X", "name": "ProcessMessages 3119", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413119328, "dur": 103, "ph": "X", "name": "ReadAsync 3119", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413119504, "dur": 1, "ph": "X", "name": "ProcessMessages 1501", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413119507, "dur": 44, "ph": "X", "name": "ReadAsync 1501", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413119553, "dur": 2, "ph": "X", "name": "ProcessMessages 3486", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413119556, "dur": 20, "ph": "X", "name": "ReadAsync 3486", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413119578, "dur": 18, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413119598, "dur": 18, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413119619, "dur": 20, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413119640, "dur": 20, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413119728, "dur": 87, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413119887, "dur": 1, "ph": "X", "name": "ProcessMessages 1800", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413119889, "dur": 127, "ph": "X", "name": "ReadAsync 1800", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413120017, "dur": 3, "ph": "X", "name": "ProcessMessages 4775", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413120084, "dur": 30, "ph": "X", "name": "ReadAsync 4775", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413120116, "dur": 1, "ph": "X", "name": "ProcessMessages 1160", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413120117, "dur": 19, "ph": "X", "name": "ReadAsync 1160", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413120217, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413120219, "dur": 97, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413120318, "dur": 1, "ph": "X", "name": "ProcessMessages 1399", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413120320, "dur": 33, "ph": "X", "name": "ReadAsync 1399", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413120354, "dur": 47, "ph": "X", "name": "ProcessMessages 1110", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413120402, "dur": 70, "ph": "X", "name": "ReadAsync 1110", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413120475, "dur": 34, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413120510, "dur": 1, "ph": "X", "name": "ProcessMessages 1312", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413120511, "dur": 29, "ph": "X", "name": "ReadAsync 1312", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413120543, "dur": 22, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413120567, "dur": 26, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413120596, "dur": 101, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413120698, "dur": 1, "ph": "X", "name": "ProcessMessages 1947", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413120700, "dur": 110, "ph": "X", "name": "ReadAsync 1947", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413120897, "dur": 118, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413121090, "dur": 2, "ph": "X", "name": "ProcessMessages 3444", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413121093, "dur": 45, "ph": "X", "name": "ReadAsync 3444", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413121139, "dur": 2, "ph": "X", "name": "ProcessMessages 3218", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413121313, "dur": 208, "ph": "X", "name": "ReadAsync 3218", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413121622, "dur": 2, "ph": "X", "name": "ProcessMessages 3070", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413121625, "dur": 115, "ph": "X", "name": "ReadAsync 3070", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413121742, "dur": 3, "ph": "X", "name": "ProcessMessages 4989", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413121747, "dur": 31, "ph": "X", "name": "ReadAsync 4989", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413121779, "dur": 1, "ph": "X", "name": "ProcessMessages 858", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413121781, "dur": 20, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413121801, "dur": 75, "ph": "X", "name": "ProcessMessages 107", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413121877, "dur": 48, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413121930, "dur": 3, "ph": "X", "name": "ProcessMessages 1506", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413121935, "dur": 199, "ph": "X", "name": "ReadAsync 1506", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413122135, "dur": 2, "ph": "X", "name": "ProcessMessages 2286", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413122138, "dur": 34, "ph": "X", "name": "ReadAsync 2286", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413122173, "dur": 1, "ph": "X", "name": "ProcessMessages 1389", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413122175, "dur": 157, "ph": "X", "name": "ReadAsync 1389", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413122335, "dur": 3, "ph": "X", "name": "ProcessMessages 1533", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413122430, "dur": 86, "ph": "X", "name": "ReadAsync 1533", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413122518, "dur": 2, "ph": "X", "name": "ProcessMessages 2484", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413122521, "dur": 37, "ph": "X", "name": "ReadAsync 2484", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413122561, "dur": 26, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413122611, "dur": 215, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413122829, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413122872, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413122874, "dur": 48, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413122924, "dur": 1, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413122926, "dur": 103, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123031, "dur": 2, "ph": "X", "name": "ProcessMessages 1164", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123035, "dur": 41, "ph": "X", "name": "ReadAsync 1164", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123077, "dur": 1, "ph": "X", "name": "ProcessMessages 916", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123079, "dur": 35, "ph": "X", "name": "ReadAsync 916", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123116, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123118, "dur": 35, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123154, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123156, "dur": 142, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123303, "dur": 3, "ph": "X", "name": "ProcessMessages 1787", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123307, "dur": 39, "ph": "X", "name": "ReadAsync 1787", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123348, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123350, "dur": 39, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123391, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123393, "dur": 40, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123435, "dur": 1, "ph": "X", "name": "ProcessMessages 1068", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123437, "dur": 91, "ph": "X", "name": "ReadAsync 1068", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123530, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123532, "dur": 59, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123593, "dur": 1, "ph": "X", "name": "ProcessMessages 1643", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123596, "dur": 125, "ph": "X", "name": "ReadAsync 1643", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123723, "dur": 2, "ph": "X", "name": "ProcessMessages 2066", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123727, "dur": 42, "ph": "X", "name": "ReadAsync 2066", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123770, "dur": 1, "ph": "X", "name": "ProcessMessages 1008", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123772, "dur": 32, "ph": "X", "name": "ReadAsync 1008", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123806, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123808, "dur": 37, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123847, "dur": 1, "ph": "X", "name": "ProcessMessages 881", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123849, "dur": 40, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123891, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123893, "dur": 23, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123918, "dur": 19, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123939, "dur": 15, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123956, "dur": 20, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413123977, "dur": 17, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413124028, "dur": 54, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413124083, "dur": 1, "ph": "X", "name": "ProcessMessages 1339", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413124189, "dur": 33, "ph": "X", "name": "ReadAsync 1339", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413124223, "dur": 2, "ph": "X", "name": "ProcessMessages 3017", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413124252, "dur": 96, "ph": "X", "name": "ReadAsync 3017", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413124350, "dur": 101, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413124491, "dur": 1, "ph": "X", "name": "ProcessMessages 1223", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413124493, "dur": 64, "ph": "X", "name": "ReadAsync 1223", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413124593, "dur": 1, "ph": "X", "name": "ProcessMessages 2141", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413124673, "dur": 44, "ph": "X", "name": "ReadAsync 2141", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413124792, "dur": 2, "ph": "X", "name": "ProcessMessages 3183", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413124862, "dur": 44, "ph": "X", "name": "ReadAsync 3183", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413124908, "dur": 2, "ph": "X", "name": "ProcessMessages 2716", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413124911, "dur": 118, "ph": "X", "name": "ReadAsync 2716", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413125066, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413125068, "dur": 243, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413125387, "dur": 1, "ph": "X", "name": "ProcessMessages 1267", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413125508, "dur": 88, "ph": "X", "name": "ReadAsync 1267", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413125598, "dur": 3, "ph": "X", "name": "ProcessMessages 4275", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413125640, "dur": 149, "ph": "X", "name": "ReadAsync 4275", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413125791, "dur": 40, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413125832, "dur": 1, "ph": "X", "name": "ProcessMessages 1438", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413125834, "dur": 46, "ph": "X", "name": "ReadAsync 1438", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413125962, "dur": 79, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413126043, "dur": 1, "ph": "X", "name": "ProcessMessages 1933", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413126045, "dur": 161, "ph": "X", "name": "ReadAsync 1933", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413126243, "dur": 1, "ph": "X", "name": "ProcessMessages 1463", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413126245, "dur": 40, "ph": "X", "name": "ReadAsync 1463", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413126287, "dur": 1, "ph": "X", "name": "ProcessMessages 1320", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413126288, "dur": 247, "ph": "X", "name": "ReadAsync 1320", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413126563, "dur": 140, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413126703, "dur": 3, "ph": "X", "name": "ProcessMessages 5259", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413126821, "dur": 182, "ph": "X", "name": "ReadAsync 5259", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413127084, "dur": 104, "ph": "X", "name": "ProcessMessages 5046", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413127351, "dur": 330, "ph": "X", "name": "ReadAsync 5046", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413127685, "dur": 2, "ph": "X", "name": "ProcessMessages 1473", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413129014, "dur": 388, "ph": "X", "name": "ReadAsync 1473", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413129404, "dur": 23, "ph": "X", "name": "ProcessMessages 20503", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413129492, "dur": 277, "ph": "X", "name": "ReadAsync 20503", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413129919, "dur": 324, "ph": "X", "name": "ProcessMessages 2687", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413130304, "dur": 185, "ph": "X", "name": "ReadAsync 2687", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413130526, "dur": 7, "ph": "X", "name": "ProcessMessages 9697", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413130563, "dur": 581, "ph": "X", "name": "ReadAsync 9697", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413131323, "dur": 1, "ph": "X", "name": "ProcessMessages 1171", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413131326, "dur": 359, "ph": "X", "name": "ReadAsync 1171", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413131855, "dur": 63, "ph": "X", "name": "ProcessMessages 4130", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413132215, "dur": 389, "ph": "X", "name": "ReadAsync 4130", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413132649, "dur": 5, "ph": "X", "name": "ProcessMessages 9343", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413132697, "dur": 245, "ph": "X", "name": "ReadAsync 9343", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413133194, "dur": 2, "ph": "X", "name": "ProcessMessages 2907", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413133324, "dur": 213, "ph": "X", "name": "ReadAsync 2907", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413133597, "dur": 4, "ph": "X", "name": "ProcessMessages 6676", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413133645, "dur": 288, "ph": "X", "name": "ReadAsync 6676", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413134013, "dur": 46, "ph": "X", "name": "ProcessMessages 3742", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413134274, "dur": 316, "ph": "X", "name": "ReadAsync 3742", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413134601, "dur": 81, "ph": "X", "name": "ProcessMessages 8397", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413134720, "dur": 122, "ph": "X", "name": "ReadAsync 8397", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413134844, "dur": 2, "ph": "X", "name": "ProcessMessages 3562", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413135021, "dur": 706, "ph": "X", "name": "ReadAsync 3562", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413135760, "dur": 38, "ph": "X", "name": "ProcessMessages 6212", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413135799, "dur": 122, "ph": "X", "name": "ReadAsync 6212", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413136042, "dur": 2, "ph": "X", "name": "ProcessMessages 2930", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413136077, "dur": 110, "ph": "X", "name": "ReadAsync 2930", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413136203, "dur": 3, "ph": "X", "name": "ProcessMessages 2885", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413136267, "dur": 142, "ph": "X", "name": "ReadAsync 2885", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413136410, "dur": 1, "ph": "X", "name": "ProcessMessages 1414", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413136447, "dur": 145, "ph": "X", "name": "ReadAsync 1414", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413136718, "dur": 1, "ph": "X", "name": "ProcessMessages 1141", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413136721, "dur": 231, "ph": "X", "name": "ReadAsync 1141", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413137011, "dur": 2, "ph": "X", "name": "ProcessMessages 2753", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413137013, "dur": 84, "ph": "X", "name": "ReadAsync 2753", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413137148, "dur": 2, "ph": "X", "name": "ProcessMessages 3046", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413137151, "dur": 347, "ph": "X", "name": "ReadAsync 3046", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413137582, "dur": 1, "ph": "X", "name": "ProcessMessages 1352", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413137764, "dur": 178, "ph": "X", "name": "ReadAsync 1352", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413138200, "dur": 81, "ph": "X", "name": "ProcessMessages 5866", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413138374, "dur": 614, "ph": "X", "name": "ReadAsync 5866", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413139104, "dur": 103, "ph": "X", "name": "ProcessMessages 8074", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413139244, "dur": 623, "ph": "X", "name": "ReadAsync 8074", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413139921, "dur": 24, "ph": "X", "name": "ProcessMessages 3916", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413140090, "dur": 124, "ph": "X", "name": "ReadAsync 3916", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413140312, "dur": 15, "ph": "X", "name": "ProcessMessages 2300", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413140518, "dur": 712, "ph": "X", "name": "ReadAsync 2300", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413141266, "dur": 53, "ph": "X", "name": "ProcessMessages 1140", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413141407, "dur": 1177, "ph": "X", "name": "ReadAsync 1140", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413142797, "dur": 480, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413143489, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413143491, "dur": 217, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413143753, "dur": 114, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413143869, "dur": 252, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413144175, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413144177, "dur": 2179, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413146423, "dur": 50, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413146628, "dur": 401, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413147123, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413147127, "dur": 150, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413147281, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413147284, "dur": 788, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413148361, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413148460, "dur": 344, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413148922, "dur": 168, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413149259, "dur": 221, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413149557, "dur": 2, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413149824, "dur": 67, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413149972, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413150067, "dur": 29, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413150175, "dur": 111, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413150397, "dur": 541, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413151124, "dur": 59, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413151304, "dur": 317, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413151770, "dur": 44, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413151866, "dur": 596, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413152489, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413152647, "dur": 319, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413153569, "dur": 39, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413153682, "dur": 145, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413153829, "dur": 60, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413154022, "dur": 195, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413154284, "dur": 303, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413154603, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413154605, "dur": 274, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413154961, "dur": 64, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413155056, "dur": 188, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413155310, "dur": 37, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413155390, "dur": 538, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413156141, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413156174, "dur": 54221, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413210403, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413210408, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413210472, "dur": 27, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413210500, "dur": 5036, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413215544, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413215552, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413215582, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413215584, "dur": 172, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413215761, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413215786, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413215860, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413215890, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413215923, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413215954, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413215956, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413216034, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413216063, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413216066, "dur": 221, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413216290, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413216306, "dur": 197, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413216508, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413216537, "dur": 707, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413217250, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413217276, "dur": 106, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413217387, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413217415, "dur": 519, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413217938, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413217961, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413217963, "dur": 216, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413218182, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413218184, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413218215, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413218217, "dur": 141, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413218361, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413218389, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413218415, "dur": 141, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413218559, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413218585, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413218624, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413218649, "dur": 248, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413218900, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413218934, "dur": 298, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413219235, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413219275, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413219277, "dur": 417, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413219697, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413219720, "dur": 1091, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413220816, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413220854, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413220856, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413220909, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413220937, "dur": 98, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413221040, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413221066, "dur": 103, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413221174, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413221199, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413221238, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413221258, "dur": 118, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413221380, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413221400, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413221453, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413221487, "dur": 93, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413221584, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413221620, "dur": 210, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413221835, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413221873, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413221875, "dur": 718, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413222599, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413222628, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413222653, "dur": 573, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413223230, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413223277, "dur": 92, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413223372, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413223399, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413223425, "dur": 171, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413223599, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413223623, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413223646, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413223685, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413223717, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413223739, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413223776, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413223806, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413223838, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413223873, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413223875, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413223910, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413223912, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413223943, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413223982, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413224009, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413224011, "dur": 24, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413224037, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413224038, "dur": 21, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413224062, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413224063, "dur": 49, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413224115, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413224116, "dur": 22, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413224140, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413224142, "dur": 19, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413224164, "dur": 19, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413224184, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413224186, "dur": 19, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413224209, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413224234, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413224235, "dur": 36, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413224275, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413224277, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413224314, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413224352, "dur": 149, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413224506, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413224538, "dur": 296, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413224838, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413224876, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413224933, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413224964, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413224986, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413225014, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413225039, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413225071, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413225101, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413225131, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413225132, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413225162, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413225164, "dur": 76, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413225243, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413225266, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413225287, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413225308, "dur": 96274, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413321596, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413321601, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413321684, "dur": 40, "ph": "X", "name": "ReadAsync 9271", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413321727, "dur": 24, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413321753, "dur": 3035, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413324794, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413324797, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 163208757248, "ts": 1750061413324819, "dur": 7057, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 2208, "tid": 173, "ts": 1750061413332377, "dur": 762, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 2208, "tid": 158913789952, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 2208, "tid": 158913789952, "ts": 1750061413099939, "dur": 126235, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 2208, "tid": 158913789952, "ts": 1750061413226175, "dur": 27, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 2208, "tid": 173, "ts": 1750061413333141, "dur": 8, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 2208, "tid": 154618822656, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 2208, "tid": 154618822656, "ts": 1750061413096715, "dur": 235200, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 2208, "tid": 154618822656, "ts": 1750061413096892, "dur": 2986, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 2208, "tid": 154618822656, "ts": 1750061413331921, "dur": 45, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 2208, "tid": 154618822656, "ts": 1750061413331931, "dur": 21, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 2208, "tid": 173, "ts": 1750061413333151, "dur": 8, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1750061413115441, "dur": 1286, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750061413116737, "dur": 809, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750061413117655, "dur": 68, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1750061413117724, "dur": 358, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750061413118426, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_332FA14DD40CDB89.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750061413122144, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750061413122962, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750061413126087, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750061413128663, "dur": 1097, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750061413130924, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750061413134891, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12900333509587573630.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750061413136029, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750061413138996, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750061413118102, "dur": 20960, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750061413139075, "dur": 184643, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750061413323720, "dur": 374, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750061413324094, "dur": 71, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750061413325205, "dur": 59, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750061413325288, "dur": 1146, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1750061413118615, "dur": 20513, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413139132, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_D44F1D7947A8470C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750061413139527, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_3EE9BB20E930FDFD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750061413140144, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750061413140227, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750061413140449, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1750061413140617, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750061413140704, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750061413140866, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413141092, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413141268, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413141490, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413141813, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413142285, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413142480, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413142890, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413143091, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413143276, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413143473, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413143662, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413143851, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413144036, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413144257, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413144554, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413144761, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413144985, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413145198, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413145360, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413145562, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413145738, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413145920, "dur": 100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413146020, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413146296, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413146730, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413147192, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750061413147427, "dur": 1174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750061413148654, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413148774, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413148881, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413149057, "dur": 517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413149575, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750061413149749, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750061413150138, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413150399, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413150960, "dur": 3983, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413154949, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750061413155100, "dur": 58380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413213483, "dur": 3055, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750061413216540, "dur": 1894, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413218447, "dur": 2878, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750061413221326, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413221574, "dur": 2761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750061413224405, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413224497, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413224742, "dur": 582, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413225333, "dur": 1394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413227518, "dur": 93, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/6000.0.50f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 1, "ts": 1750061413227612, "dur": 607, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/6000.0.50f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 1, "ts": 1750061413226727, "dur": 1526, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750061413228253, "dur": 95505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413118604, "dur": 20516, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413139124, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_1BD15D364A91220E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750061413139271, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_42BE61A2F375B465.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750061413139538, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_E3FF4E8988E07430.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750061413139933, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750061413140195, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1750061413140382, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750061413140485, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1750061413140709, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750061413140904, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413141070, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413141267, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413141472, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413141655, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413141926, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413142127, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413142324, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413142754, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413142936, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413143248, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413143426, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413143630, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413143820, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413144000, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413144268, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413144523, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413144769, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413144952, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413145150, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413145355, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413145554, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413145730, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413145959, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413146212, "dur": 74, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413146294, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413146747, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413147198, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750061413147389, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750061413147459, "dur": 1115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750061413148575, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413148776, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413148836, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413149051, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750061413149184, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750061413149587, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413149700, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413149856, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750061413150020, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750061413150510, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413150924, "dur": 1945, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413152870, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750061413153015, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750061413153408, "dur": 1548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413154956, "dur": 58548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413213509, "dur": 3240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750061413216801, "dur": 2863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750061413219665, "dur": 1733, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750061413221408, "dur": 2285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750061413223735, "dur": 2024, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750061413225798, "dur": 97987, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413118773, "dur": 20441, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413139215, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_05821C8CF4745330.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750061413139276, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413139493, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413140098, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750061413140177, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1750061413140460, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1750061413140672, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750061413140859, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413141102, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413141302, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413141494, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413141698, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413141989, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413142165, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413142371, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413142784, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413142993, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413143152, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413143332, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413143594, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413143775, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413143951, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413144137, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413144369, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413144545, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413144782, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413144965, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413145216, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413145386, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413145589, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413145799, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413146024, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413146295, "dur": 424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413146748, "dur": 643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413147392, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750061413147599, "dur": 863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750061413148463, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413148549, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413148772, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413149052, "dur": 532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413149584, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413149858, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413150128, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413150397, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413150913, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750061413151055, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750061413151448, "dur": 3534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413154982, "dur": 58547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413213531, "dur": 2944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750061413216476, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413216543, "dur": 2402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750061413218947, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413219065, "dur": 2605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750061413221671, "dur": 1420, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750061413223116, "dur": 2318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750061413225506, "dur": 98215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413118577, "dur": 20530, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413139263, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_025D68ED9798F53F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750061413139504, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_136791916CB4C4E9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750061413140158, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750061413140638, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1750061413140880, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413141072, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413141284, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413141525, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413141747, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413141982, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413142170, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413142363, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413142815, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413142997, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413143174, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413143340, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413143663, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413143869, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413144096, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413144553, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413144793, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413144966, "dur": 386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413145353, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413145518, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413145697, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413145876, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413146058, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413146363, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413146749, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413147177, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750061413147382, "dur": 1007, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750061413148390, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413148493, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750061413148735, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750061413149146, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750061413149311, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750061413149727, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413149854, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750061413150014, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750061413150391, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1750061413150804, "dur": 65, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413151169, "dur": 59720, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1750061413213467, "dur": 2883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750061413216397, "dur": 2237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750061413218679, "dur": 2824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750061413221545, "dur": 2137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750061413223684, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413223918, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413224189, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413224458, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413224761, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413224987, "dur": 772, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750061413225772, "dur": 97963, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413118531, "dur": 20557, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413139276, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_C5179C2B98A35602.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750061413139508, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413140188, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1750061413140464, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1750061413140705, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750061413140901, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413141029, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750061413141104, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413141360, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413141657, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413141922, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413142130, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413142297, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413142745, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413142953, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413143341, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413143536, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413143724, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413143997, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413144251, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413144456, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413144644, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413144947, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413145137, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413145340, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413145540, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413145719, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413146127, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413146389, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413146728, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413147185, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750061413147379, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413147442, "dur": 1214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750061413148773, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413149053, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413149581, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413149864, "dur": 530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413150394, "dur": 578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413150973, "dur": 4005, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413154978, "dur": 58544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413213528, "dur": 2827, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750061413216356, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413216422, "dur": 2404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750061413218870, "dur": 2755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750061413221676, "dur": 2619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750061413224347, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413224409, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413224485, "dur": 497, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413224991, "dur": 800, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750061413225791, "dur": 97958, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413118668, "dur": 20478, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413139268, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_1DC9F96436961A23.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750061413139501, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413140211, "dur": 157, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750061413140657, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750061413140858, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413141089, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413141343, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413141536, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413141762, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413141970, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413142156, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413142356, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413142835, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413143025, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413143259, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413143457, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413143633, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413143944, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413144240, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413144454, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413144635, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413144956, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413145151, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413145338, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413145693, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413145873, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413146043, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413146292, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413146724, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413147189, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750061413147436, "dur": 1038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750061413148475, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413148584, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413148779, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750061413148946, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413148998, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750061413149578, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750061413149724, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413150023, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750061413150428, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413150965, "dur": 4004, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413154970, "dur": 58543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413213515, "dur": 2344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750061413215860, "dur": 397, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413216269, "dur": 2297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750061413218567, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413218703, "dur": 2792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750061413221496, "dur": 436, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413221943, "dur": 2120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750061413224065, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413224195, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413224370, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413224611, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413224785, "dur": 802, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750061413225611, "dur": 98105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413118743, "dur": 20452, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413139200, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_BE0B22B212C39490.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750061413139494, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_1E95AC98D1BD4446.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750061413140066, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750061413140179, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1750061413140668, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750061413140822, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3064155424177982801.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750061413140908, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413141081, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413141299, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413141558, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413141829, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413142029, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413142198, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413142477, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413142920, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413143115, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413143284, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413143470, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413143653, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413143862, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413144056, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413144269, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413144495, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413144766, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413144958, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413145173, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413145527, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413145743, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413146327, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413146725, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413147179, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750061413147406, "dur": 999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750061413148406, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413148512, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413148772, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413149051, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413149579, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413149864, "dur": 531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413150395, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413150916, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413150999, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750061413151143, "dur": 999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750061413152188, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750061413152285, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750061413152865, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750061413153008, "dur": 444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750061413153484, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750061413153574, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750061413153815, "dur": 1137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413154952, "dur": 58512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413213475, "dur": 4048, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750061413217525, "dur": 1136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413218675, "dur": 3144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750061413221820, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413221893, "dur": 2506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750061413224401, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413224613, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413224765, "dur": 678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750061413225460, "dur": 98263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413118819, "dur": 20409, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413139351, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_FB3B15524EEAB01A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750061413139498, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_7D559772BEA705DB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750061413140209, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750061413140531, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1750061413140655, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750061413140865, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413141100, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413141370, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413141604, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413141848, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413142060, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413142249, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413142455, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413142915, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413143127, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413143348, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413143648, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413144345, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413144639, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413144848, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413145042, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413145229, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413145406, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413145627, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413145824, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413146376, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413146722, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413147175, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750061413147368, "dur": 1107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750061413148510, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_621CDDF9C514DF8F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750061413148587, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750061413148770, "dur": 757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750061413149528, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413149706, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413149857, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750061413150036, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750061413150492, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413150691, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413150918, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750061413151041, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750061413151529, "dur": 3454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413154983, "dur": 58896, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413213881, "dur": 2892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750061413216805, "dur": 2884, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750061413219746, "dur": 3272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750061413223020, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413223147, "dur": 2217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750061413225365, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413225455, "dur": 2808, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750061413228264, "dur": 95464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061413118877, "dur": 20365, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061413139357, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_CDEA261D3E32C133.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750061413139518, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_1DB8604BF3C2B15B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750061413139965, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750061413140108, "dur": 3774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750061413143939, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750061413144177, "dur": 2488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750061413146746, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750061413146832, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750061413147192, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750061413147399, "dur": 1075, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750061413148475, "dur": 537, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061413149048, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750061413149244, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750061413149853, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750061413150000, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750061413150226, "dur": 604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750061413150910, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750061413151053, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750061413151509, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750061413151607, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750061413152147, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750061413152250, "dur": 1038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750061413153320, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750061413153410, "dur": 865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750061413154306, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750061413154393, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750061413154942, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750061413155052, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750061413155287, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750061413155550, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750061413155871, "dur": 166339, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750061413118925, "dur": 20365, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413139499, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413140183, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1750061413140265, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1750061413140369, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1750061413140681, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1750061413140862, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413141097, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413141313, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413141721, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413141968, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413142287, "dur": 853, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@e07c4c789cd8\\Editor\\Expressions\\VFXExpressionSampleSDF.cs"}}, {"pid": 12345, "tid": 10, "ts": 1750061413142169, "dur": 1014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413143183, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413143351, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413143542, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413143720, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413143955, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413144179, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413144414, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413144610, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413144871, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413145072, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413145299, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413145526, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413145712, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413145916, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413146078, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413146335, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413146734, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413147194, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750061413147399, "dur": 1645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750061413149044, "dur": 478, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413149547, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750061413149720, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750061413150126, "dur": 353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413150482, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413150945, "dur": 4004, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413154949, "dur": 58539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413213493, "dur": 2493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750061413215987, "dur": 375, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413216374, "dur": 2262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750061413218637, "dur": 486, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413219134, "dur": 2876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750061413222011, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413222087, "dur": 2200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750061413224323, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413224781, "dur": 716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750061413225521, "dur": 98210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413118969, "dur": 20289, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413139260, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_36DAB0C55B9C80DA.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750061413139386, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_332FA14DD40CDB89.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750061413139467, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_BEF5CA1EC64E588B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750061413140175, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750061413140441, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1750061413140676, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1750061413140876, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413141078, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413141396, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413141588, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413141861, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413142086, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413142270, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413142457, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413142909, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413143082, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413143272, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413143451, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413143632, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413143832, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413144068, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413144328, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413144747, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413144946, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413145141, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413145342, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413145587, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413145755, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413145984, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413146169, "dur": 82, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413146289, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413146722, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413147172, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750061413147378, "dur": 1237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750061413148615, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413148714, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750061413148879, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413148985, "dur": 450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750061413149493, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413149580, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413149866, "dur": 526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413150412, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413150910, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750061413151051, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750061413151444, "dur": 3530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413154974, "dur": 58535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413213513, "dur": 3003, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1750061413216517, "dur": 1364, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413217900, "dur": 3798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1750061413221749, "dur": 2150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1750061413223901, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413224162, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413224285, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413224352, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413224812, "dur": 834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750061413225647, "dur": 98092, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413119021, "dur": 20223, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413139246, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_100C028F850EE8CE.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750061413139523, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_8D701D583C70E786.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750061413140229, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750061413140432, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750061413140672, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1750061413140882, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413141076, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413141283, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413141587, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413141799, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413142015, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413142189, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413142364, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413142840, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413143180, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413143354, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413143537, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413143717, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413143981, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413144263, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413144624, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413145204, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413145377, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413145608, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413145798, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413145999, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413146291, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413146723, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413147173, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750061413147377, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413147646, "dur": 1821, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750061413149467, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413149572, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750061413149707, "dur": 1095, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750061413150802, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413150995, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750061413151134, "dur": 894, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750061413152082, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750061413152177, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750061413152484, "dur": 2503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413154988, "dur": 58506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413213497, "dur": 3081, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750061413216579, "dur": 1163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750061413217753, "dur": 4530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750061413222339, "dur": 3265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750061413225657, "dur": 98084, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413119044, "dur": 20187, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413139279, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_46081861FEA49A1E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750061413140136, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750061413140225, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750061413140514, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1750061413140682, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750061413140891, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413141095, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413141333, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413141612, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413141852, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413142245, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413142434, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413142875, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413143072, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413143239, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413143403, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413143596, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413143958, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413144144, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413144404, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413144593, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413144850, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413145061, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413145256, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413145435, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413145729, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413145914, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413146108, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413146355, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413146732, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413147202, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750061413147460, "dur": 1215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750061413148787, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413149053, "dur": 523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413149577, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750061413149711, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413149822, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750061413150242, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413150396, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413150918, "dur": 1233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413152153, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750061413152302, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750061413152695, "dur": 2272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413154968, "dur": 58604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413213574, "dur": 3036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750061413216611, "dur": 392, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413217012, "dur": 2307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750061413219320, "dur": 875, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413220205, "dur": 3206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750061413223412, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750061413223725, "dur": 2001, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750061413225763, "dur": 97993, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413119085, "dur": 20206, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413139551, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_C9BF491CE9429A6C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750061413139866, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1750061413140040, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1750061413140175, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1750061413140682, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1750061413140898, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413141087, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413141325, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413141489, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413141705, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413141987, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413142162, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413142367, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413142795, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413142998, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413143202, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413143833, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413144184, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413144414, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413144619, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413144857, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413145050, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413145241, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413145472, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413145667, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413145859, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413146133, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413146370, "dur": 365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413146735, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413147203, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750061413147394, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413147478, "dur": 938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750061413148417, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413148486, "dur": 425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750061413148961, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413149051, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750061413149259, "dur": 433, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413149695, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750061413150131, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413150410, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413150912, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750061413151058, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750061413151386, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413151564, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750061413151708, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750061413152393, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750061413152552, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750061413152909, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413153068, "dur": 1890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413154958, "dur": 58540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413213501, "dur": 3011, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750061413216548, "dur": 2296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750061413218899, "dur": 3812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750061413222712, "dur": 430, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750061413223150, "dur": 2393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750061413225603, "dur": 98117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413119114, "dur": 20122, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413139408, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_77C9B2F6CDEA9940.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750061413139566, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_9CC28255281D0D3B.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750061413140011, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750061413140165, "dur": 2890, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750061413143140, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413143313, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413143608, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413143816, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413144023, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413144240, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413144473, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413144798, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413144988, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413145209, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413145389, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413145560, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413145747, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413145946, "dur": 102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413146049, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413146344, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413146728, "dur": 451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413147183, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750061413147389, "dur": 917, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750061413148309, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750061413148844, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750061413148978, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750061413149383, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413149609, "dur": 352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413149965, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750061413150154, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750061413150541, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413150935, "dur": 4012, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413154948, "dur": 58520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413213470, "dur": 2498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750061413216013, "dur": 2369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750061413218383, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413218449, "dur": 2832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750061413221316, "dur": 2820, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750061413224137, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413224260, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413224337, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413224682, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413224804, "dur": 833, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750061413225655, "dur": 98059, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413119145, "dur": 20087, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413139233, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_5D5EF2E9E0015A01.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750061413139502, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_1E5BE8A4F6F01B45.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750061413140080, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413140190, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1750061413140509, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1750061413140894, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413141074, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413141299, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413141651, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413141889, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413142178, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413142442, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413142870, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413143065, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413143245, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413143443, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413143621, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413143805, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413143998, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413144185, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413144436, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413144664, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413144903, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413145091, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413145285, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413145493, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413145668, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413145901, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413146178, "dur": 75, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413146292, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413146726, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413147205, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750061413147410, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750061413147583, "dur": 1207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413148795, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1750061413149293, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413149574, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750061413149729, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1750061413150211, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413150447, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413150950, "dur": 3990, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413154942, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750061413155130, "dur": 58344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413213511, "dur": 2829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750061413216341, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413216429, "dur": 2595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750061413219025, "dur": 369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750061413219404, "dur": 3680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750061413223124, "dur": 2486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750061413225659, "dur": 98078, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750061413330919, "dur": 1098, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 2208, "tid": 173, "ts": 1750061413333197, "dur": 1429, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 2208, "tid": 173, "ts": 1750061413334662, "dur": 7201, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 2208, "tid": 173, "ts": 1750061413332343, "dur": 9552, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}