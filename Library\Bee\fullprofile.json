{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 2208, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 2208, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 2208, "tid": 146, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 2208, "tid": 146, "ts": 1750060998473409, "dur": 11, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 2208, "tid": 146, "ts": 1750060998473433, "dur": 6, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 2208, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 2208, "tid": 1, "ts": 1750060998198100, "dur": 2131, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 2208, "tid": 1, "ts": 1750060998200237, "dur": 29949, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 2208, "tid": 1, "ts": 1750060998230189, "dur": 35292, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 2208, "tid": 146, "ts": 1750060998473442, "dur": 11, "ph": "X", "name": "", "args": {}}, {"pid": 2208, "tid": 124554051584, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998198045, "dur": 15779, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998213825, "dur": 258971, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998213836, "dur": 120, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998213961, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998213964, "dur": 100, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998214069, "dur": 285, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998214359, "dur": 14, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998214376, "dur": 2927, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998217312, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998217317, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998217410, "dur": 4, "ph": "X", "name": "ProcessMessages 1340", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998217417, "dur": 55, "ph": "X", "name": "ReadAsync 1340", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998217474, "dur": 1, "ph": "X", "name": "ProcessMessages 1113", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998217476, "dur": 51, "ph": "X", "name": "ReadAsync 1113", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998217530, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998217532, "dur": 44, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998217579, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998217581, "dur": 42, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998217625, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998217628, "dur": 38, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998217667, "dur": 1, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998217669, "dur": 45, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998217719, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998217757, "dur": 1, "ph": "X", "name": "ProcessMessages 718", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998217759, "dur": 36, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998217798, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998217801, "dur": 49, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998217852, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998217854, "dur": 41, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998217898, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998217903, "dur": 39, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998217945, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998217949, "dur": 40, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998217990, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998217992, "dur": 41, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218036, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218038, "dur": 43, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218084, "dur": 48, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218139, "dur": 2, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218142, "dur": 43, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218189, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218191, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218236, "dur": 1, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218237, "dur": 31, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218271, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218273, "dur": 33, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218309, "dur": 49, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218364, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218366, "dur": 33, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218402, "dur": 2, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218406, "dur": 52, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218461, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218463, "dur": 40, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218505, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218507, "dur": 24, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218535, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218536, "dur": 29, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218568, "dur": 21, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218593, "dur": 24, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218631, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218633, "dur": 28, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218663, "dur": 1, "ph": "X", "name": "ProcessMessages 824", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218664, "dur": 23, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218690, "dur": 21, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218713, "dur": 24, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218740, "dur": 24, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218766, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218767, "dur": 23, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218794, "dur": 21, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218818, "dur": 23, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218845, "dur": 26, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218874, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218875, "dur": 33, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218911, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218913, "dur": 28, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218943, "dur": 24, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218969, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998218994, "dur": 22, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219019, "dur": 23, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219045, "dur": 27, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219073, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219075, "dur": 23, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219100, "dur": 28, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219131, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219133, "dur": 41, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219176, "dur": 1, "ph": "X", "name": "ProcessMessages 751", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219177, "dur": 24, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219206, "dur": 30, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219241, "dur": 28, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219272, "dur": 21, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219295, "dur": 26, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219323, "dur": 13, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219338, "dur": 33, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219373, "dur": 1, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219376, "dur": 25, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219404, "dur": 28, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219435, "dur": 24, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219462, "dur": 25, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219490, "dur": 23, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219516, "dur": 25, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219544, "dur": 22, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219568, "dur": 22, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219595, "dur": 21, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219618, "dur": 20, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219641, "dur": 23, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219666, "dur": 23, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219693, "dur": 22, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219717, "dur": 20, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219740, "dur": 23, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219766, "dur": 22, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219791, "dur": 26, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219820, "dur": 22, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219845, "dur": 24, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219871, "dur": 22, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219897, "dur": 21, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219921, "dur": 31, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219957, "dur": 25, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998219986, "dur": 24, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220013, "dur": 24, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220039, "dur": 21, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220062, "dur": 24, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220089, "dur": 17, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220109, "dur": 37, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220150, "dur": 35, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220188, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220190, "dur": 42, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220236, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220241, "dur": 35, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220280, "dur": 34, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220318, "dur": 30, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220349, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220351, "dur": 44, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220397, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220400, "dur": 57, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220460, "dur": 2, "ph": "X", "name": "ProcessMessages 820", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220463, "dur": 31, "ph": "X", "name": "ReadAsync 820", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220496, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220499, "dur": 68, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220571, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220604, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220606, "dur": 35, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220644, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220645, "dur": 34, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220682, "dur": 25, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220715, "dur": 21, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220738, "dur": 21, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220762, "dur": 23, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220788, "dur": 24, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220815, "dur": 22, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220841, "dur": 27, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220873, "dur": 18, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220894, "dur": 30, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220926, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220931, "dur": 24, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220957, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220958, "dur": 19, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998220982, "dur": 24, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221009, "dur": 25, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221038, "dur": 23, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221063, "dur": 17, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221082, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221084, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221106, "dur": 22, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221132, "dur": 24, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221158, "dur": 23, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221184, "dur": 31, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221218, "dur": 31, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221252, "dur": 60, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221313, "dur": 1, "ph": "X", "name": "ProcessMessages 942", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221315, "dur": 25, "ph": "X", "name": "ReadAsync 942", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221343, "dur": 22, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221367, "dur": 57, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221427, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221431, "dur": 25, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221459, "dur": 25, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221489, "dur": 23, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221514, "dur": 22, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221539, "dur": 21, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221563, "dur": 21, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221585, "dur": 1, "ph": "X", "name": "ProcessMessages 195", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221587, "dur": 22, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221611, "dur": 22, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221636, "dur": 20, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221658, "dur": 21, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221682, "dur": 21, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221706, "dur": 12, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221719, "dur": 74, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221795, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221827, "dur": 27, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221857, "dur": 31, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221890, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221891, "dur": 20, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221913, "dur": 18, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221934, "dur": 21, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221958, "dur": 25, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221985, "dur": 2, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998221987, "dur": 24, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222014, "dur": 22, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222038, "dur": 24, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222064, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222066, "dur": 29, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222097, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222099, "dur": 33, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222135, "dur": 22, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222159, "dur": 25, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222187, "dur": 22, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222211, "dur": 25, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222238, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222240, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222266, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222268, "dur": 32, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222302, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222303, "dur": 36, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222343, "dur": 28, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222374, "dur": 33, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222410, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222412, "dur": 33, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222447, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222448, "dur": 31, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222482, "dur": 22, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222507, "dur": 21, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222530, "dur": 22, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222554, "dur": 21, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222577, "dur": 20, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222600, "dur": 24, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222626, "dur": 22, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222651, "dur": 26, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222681, "dur": 2, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222684, "dur": 26, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222713, "dur": 21, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222738, "dur": 23, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222763, "dur": 22, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222789, "dur": 25, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222817, "dur": 25, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222844, "dur": 17, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222863, "dur": 22, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222887, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222889, "dur": 23, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222914, "dur": 22, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222938, "dur": 20, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222960, "dur": 20, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998222983, "dur": 19, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223004, "dur": 21, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223027, "dur": 22, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223055, "dur": 23, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223080, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223081, "dur": 22, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223105, "dur": 24, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223131, "dur": 21, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223154, "dur": 12, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223168, "dur": 25, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223195, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223197, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223223, "dur": 21, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223246, "dur": 24, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223274, "dur": 29, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223305, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223307, "dur": 31, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223339, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223341, "dur": 25, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223367, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223369, "dur": 195, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223567, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223602, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223603, "dur": 31, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223637, "dur": 25, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223665, "dur": 20, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223687, "dur": 24, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223713, "dur": 21, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223736, "dur": 29, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223768, "dur": 30, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223800, "dur": 22, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223825, "dur": 29, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223856, "dur": 20, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223879, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223881, "dur": 45, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223928, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223951, "dur": 24, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223977, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998223979, "dur": 21, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224000, "dur": 1, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224002, "dur": 36, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224042, "dur": 23, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224067, "dur": 23, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224093, "dur": 21, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224116, "dur": 22, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224140, "dur": 41, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224183, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224208, "dur": 17, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224227, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224228, "dur": 18, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224248, "dur": 22, "ph": "X", "name": "ReadAsync 122", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224273, "dur": 19, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224294, "dur": 11, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224308, "dur": 21, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224331, "dur": 20, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224355, "dur": 21, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224378, "dur": 17, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224397, "dur": 22, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224422, "dur": 29, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224452, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224470, "dur": 18, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224490, "dur": 20, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224515, "dur": 23, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224542, "dur": 19, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224563, "dur": 33, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224601, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224604, "dur": 27, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224633, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224635, "dur": 29, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224667, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224669, "dur": 22, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224694, "dur": 21, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224717, "dur": 21, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224741, "dur": 25, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224769, "dur": 21, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224792, "dur": 22, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224817, "dur": 20, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224840, "dur": 20, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224863, "dur": 21, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224887, "dur": 21, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224910, "dur": 21, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224934, "dur": 22, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224958, "dur": 21, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998224982, "dur": 22, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225006, "dur": 22, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225031, "dur": 21, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225054, "dur": 22, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225078, "dur": 15, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225095, "dur": 24, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225123, "dur": 21, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225146, "dur": 21, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225168, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225170, "dur": 25, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225197, "dur": 17, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225217, "dur": 21, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225240, "dur": 1, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225242, "dur": 19, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225263, "dur": 21, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225287, "dur": 19, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225310, "dur": 22, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225334, "dur": 20, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225356, "dur": 20, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225378, "dur": 21, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225400, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225402, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225432, "dur": 22, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225457, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225459, "dur": 24, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225486, "dur": 23, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225512, "dur": 25, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225540, "dur": 21, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225563, "dur": 18, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225583, "dur": 19, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225605, "dur": 23, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225630, "dur": 27, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225660, "dur": 18, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225680, "dur": 30, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225713, "dur": 13, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225728, "dur": 35, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225765, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225805, "dur": 1, "ph": "X", "name": "ProcessMessages 973", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225807, "dur": 30, "ph": "X", "name": "ReadAsync 973", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225839, "dur": 28, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225869, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225873, "dur": 31, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225906, "dur": 21, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225929, "dur": 25, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225955, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225957, "dur": 27, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998225986, "dur": 23, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226011, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226013, "dur": 20, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226035, "dur": 21, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226058, "dur": 21, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226082, "dur": 21, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226105, "dur": 20, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226128, "dur": 23, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226154, "dur": 19, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226177, "dur": 24, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226203, "dur": 18, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226224, "dur": 21, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226248, "dur": 21, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226272, "dur": 20, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226295, "dur": 20, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226318, "dur": 21, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226342, "dur": 19, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226364, "dur": 21, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226387, "dur": 19, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226409, "dur": 21, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226432, "dur": 20, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226455, "dur": 22, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226480, "dur": 19, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226501, "dur": 29, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226533, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226567, "dur": 23, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226593, "dur": 22, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226618, "dur": 20, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226640, "dur": 19, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226662, "dur": 41, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226707, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226737, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226738, "dur": 24, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226765, "dur": 21, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226789, "dur": 20, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226812, "dur": 30, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226844, "dur": 29, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226877, "dur": 37, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226915, "dur": 1, "ph": "X", "name": "ProcessMessages 723", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226917, "dur": 60, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226979, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998226980, "dur": 36, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227019, "dur": 21, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227043, "dur": 24, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227068, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227070, "dur": 28, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227100, "dur": 24, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227127, "dur": 23, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227153, "dur": 20, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227176, "dur": 20, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227198, "dur": 39, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227240, "dur": 36, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227277, "dur": 1, "ph": "X", "name": "ProcessMessages 1073", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227279, "dur": 72, "ph": "X", "name": "ReadAsync 1073", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227353, "dur": 38, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227392, "dur": 1, "ph": "X", "name": "ProcessMessages 932", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227394, "dur": 31, "ph": "X", "name": "ReadAsync 932", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227427, "dur": 45, "ph": "X", "name": "ReadAsync 905", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227473, "dur": 2, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227476, "dur": 125, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227602, "dur": 1, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227604, "dur": 35, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227640, "dur": 2, "ph": "X", "name": "ProcessMessages 1685", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227643, "dur": 23, "ph": "X", "name": "ReadAsync 1685", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227669, "dur": 23, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227695, "dur": 18, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227716, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227744, "dur": 26, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227773, "dur": 14, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227790, "dur": 25, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227817, "dur": 28, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998227848, "dur": 296, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228148, "dur": 43, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228192, "dur": 1, "ph": "X", "name": "ProcessMessages 1340", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228206, "dur": 53, "ph": "X", "name": "ReadAsync 1340", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228261, "dur": 4, "ph": "X", "name": "ProcessMessages 4201", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228266, "dur": 22, "ph": "X", "name": "ReadAsync 4201", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228290, "dur": 21, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228313, "dur": 32, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228349, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228380, "dur": 2, "ph": "X", "name": "ProcessMessages 718", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228383, "dur": 26, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228411, "dur": 22, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228435, "dur": 24, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228460, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228462, "dur": 23, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228487, "dur": 24, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228513, "dur": 27, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228543, "dur": 26, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228572, "dur": 24, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228600, "dur": 22, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228624, "dur": 32, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228657, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228658, "dur": 26, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228687, "dur": 1, "ph": "X", "name": "ProcessMessages 99", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228688, "dur": 32, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228722, "dur": 1, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228723, "dur": 32, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228758, "dur": 29, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228789, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228790, "dur": 31, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228824, "dur": 26, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228852, "dur": 34, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228887, "dur": 1, "ph": "X", "name": "ProcessMessages 747", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228889, "dur": 27, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228917, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228918, "dur": 29, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228951, "dur": 27, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998228980, "dur": 26, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229008, "dur": 29, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229039, "dur": 28, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229070, "dur": 30, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229103, "dur": 22, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229128, "dur": 29, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229159, "dur": 32, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229194, "dur": 23, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229221, "dur": 20, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229243, "dur": 21, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229267, "dur": 21, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229291, "dur": 47, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229341, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229344, "dur": 56, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229402, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229405, "dur": 48, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229455, "dur": 2, "ph": "X", "name": "ProcessMessages 824", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229459, "dur": 41, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229501, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229504, "dur": 35, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229541, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229543, "dur": 42, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229587, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229589, "dur": 33, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229624, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229627, "dur": 39, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229668, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229670, "dur": 77, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229753, "dur": 2, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229757, "dur": 43, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229802, "dur": 2, "ph": "X", "name": "ProcessMessages 1057", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229805, "dur": 31, "ph": "X", "name": "ReadAsync 1057", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229839, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229841, "dur": 48, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229892, "dur": 36, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229931, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229933, "dur": 35, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229971, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998229973, "dur": 34, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230009, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230012, "dur": 58, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230072, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230075, "dur": 41, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230118, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230120, "dur": 26, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230147, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230149, "dur": 39, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230190, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230192, "dur": 34, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230229, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230231, "dur": 37, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230270, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230271, "dur": 28, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230302, "dur": 31, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230335, "dur": 34, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230372, "dur": 29, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230403, "dur": 33, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230438, "dur": 20, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230461, "dur": 22, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230485, "dur": 31, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230518, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230548, "dur": 41, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230592, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230625, "dur": 26, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230654, "dur": 27, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230683, "dur": 85, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230771, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230810, "dur": 19, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230834, "dur": 91, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230929, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230931, "dur": 45, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230980, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998230982, "dur": 57, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998231044, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998231047, "dur": 94, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998231146, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998231148, "dur": 65, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998231218, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998231221, "dur": 41, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998231264, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998231266, "dur": 125, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998231395, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998231396, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998231465, "dur": 2, "ph": "X", "name": "ProcessMessages 994", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998231468, "dur": 39, "ph": "X", "name": "ReadAsync 994", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998231511, "dur": 50, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998231565, "dur": 2, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998231569, "dur": 45, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998231618, "dur": 2, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998231622, "dur": 40, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998231665, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998231667, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998231724, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998231726, "dur": 42, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998231769, "dur": 1, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998231771, "dur": 23, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998231796, "dur": 160, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998231961, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232008, "dur": 1, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232010, "dur": 37, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232050, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232052, "dur": 72, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232126, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232158, "dur": 23, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232185, "dur": 29, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232217, "dur": 94, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232315, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232358, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232360, "dur": 36, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232397, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232399, "dur": 34, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232436, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232438, "dur": 96, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232538, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232578, "dur": 1, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232581, "dur": 23, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232607, "dur": 25, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232635, "dur": 1, "ph": "X", "name": "ProcessMessages 95", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232637, "dur": 37, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232677, "dur": 1, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232679, "dur": 68, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232752, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232792, "dur": 1, "ph": "X", "name": "ProcessMessages 862", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232793, "dur": 26, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232822, "dur": 22, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232846, "dur": 87, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232938, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232988, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998232990, "dur": 36, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233029, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233032, "dur": 72, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233106, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233137, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233139, "dur": 34, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233175, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233176, "dur": 28, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233207, "dur": 70, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233281, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233314, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233316, "dur": 23, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233342, "dur": 23, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233367, "dur": 64, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233434, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233473, "dur": 26, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233502, "dur": 21, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233525, "dur": 74, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233602, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233634, "dur": 19, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233654, "dur": 1, "ph": "X", "name": "ProcessMessages 233", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233655, "dur": 25, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233683, "dur": 73, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233759, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233785, "dur": 20, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233807, "dur": 21, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233831, "dur": 19, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233854, "dur": 56, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233912, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233946, "dur": 24, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998233973, "dur": 30, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234005, "dur": 25, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234032, "dur": 85, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234120, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234170, "dur": 27, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234198, "dur": 1, "ph": "X", "name": "ProcessMessages 1018", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234201, "dur": 20, "ph": "X", "name": "ReadAsync 1018", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234223, "dur": 21, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234246, "dur": 22, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234271, "dur": 21, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234294, "dur": 32, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234331, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234334, "dur": 67, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234405, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234441, "dur": 2, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234443, "dur": 29, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234475, "dur": 21, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234498, "dur": 69, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234571, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234596, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234598, "dur": 26, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234627, "dur": 24, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234655, "dur": 27, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234685, "dur": 23, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234710, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234711, "dur": 32, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234746, "dur": 17, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234766, "dur": 22, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234790, "dur": 74, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234867, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234895, "dur": 14, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234911, "dur": 27, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234941, "dur": 20, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998234963, "dur": 72, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235037, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235064, "dur": 27, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235094, "dur": 20, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235116, "dur": 22, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235139, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235141, "dur": 24, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235167, "dur": 23, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235191, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235192, "dur": 20, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235215, "dur": 16, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235234, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235255, "dur": 72, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235330, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235355, "dur": 21, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235379, "dur": 20, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235401, "dur": 19, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235421, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235423, "dur": 68, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235494, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235528, "dur": 28, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235558, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235559, "dur": 43, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235604, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235606, "dur": 49, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235657, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235698, "dur": 28, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235728, "dur": 24, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235755, "dur": 22, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235779, "dur": 22, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235805, "dur": 19, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235827, "dur": 19, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235848, "dur": 23, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235874, "dur": 68, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235944, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235975, "dur": 1, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998235977, "dur": 34, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236012, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236014, "dur": 26, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236042, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236044, "dur": 70, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236116, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236149, "dur": 23, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236175, "dur": 20, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236198, "dur": 82, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236282, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236312, "dur": 22, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236336, "dur": 21, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236360, "dur": 20, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236383, "dur": 61, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236446, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236474, "dur": 26, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236501, "dur": 2, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236504, "dur": 20, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236526, "dur": 62, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236590, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236621, "dur": 32, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236655, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236658, "dur": 36, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236695, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236697, "dur": 25, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236723, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236725, "dur": 77, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236805, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236839, "dur": 23, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236864, "dur": 22, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236888, "dur": 70, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236960, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998236990, "dur": 24, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998237016, "dur": 25, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998237044, "dur": 19, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998237066, "dur": 69, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998237137, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998237163, "dur": 26, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998237191, "dur": 17, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998237210, "dur": 37, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998237249, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998237274, "dur": 67, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998237343, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998237367, "dur": 23, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998237393, "dur": 20, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998237415, "dur": 70, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998237488, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998237518, "dur": 22, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998237543, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998237544, "dur": 25, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998237571, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998237573, "dur": 77, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998237653, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998237682, "dur": 28, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998237712, "dur": 31, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998237746, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998237749, "dur": 72, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998237825, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998237857, "dur": 33, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998237892, "dur": 30, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998237924, "dur": 51, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998237979, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238009, "dur": 22, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238033, "dur": 17, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238051, "dur": 18, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238072, "dur": 76, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238150, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238178, "dur": 20, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238200, "dur": 27, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238230, "dur": 25, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238258, "dur": 66, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238326, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238353, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238354, "dur": 29, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238386, "dur": 23, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238411, "dur": 11, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238424, "dur": 76, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238502, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238532, "dur": 23, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238557, "dur": 28, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238586, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238588, "dur": 18, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238608, "dur": 77, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238688, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238717, "dur": 30, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238750, "dur": 21, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238774, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238775, "dur": 80, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238858, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238889, "dur": 29, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238921, "dur": 28, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998238951, "dur": 73, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239029, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239068, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239069, "dur": 28, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239100, "dur": 17, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239120, "dur": 66, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239188, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239212, "dur": 21, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239235, "dur": 19, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239257, "dur": 19, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239279, "dur": 67, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239348, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239377, "dur": 2, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239380, "dur": 33, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239415, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239417, "dur": 20, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239439, "dur": 83, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239525, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239570, "dur": 26, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239599, "dur": 20, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239622, "dur": 70, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239695, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239724, "dur": 18, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239745, "dur": 22, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239769, "dur": 17, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239789, "dur": 63, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239854, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239886, "dur": 31, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239919, "dur": 28, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239951, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239953, "dur": 19, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998239975, "dur": 49, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240026, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240057, "dur": 29, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240088, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240090, "dur": 26, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240119, "dur": 56, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240177, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240215, "dur": 29, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240247, "dur": 61, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240310, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240342, "dur": 25, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240370, "dur": 38, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240410, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240411, "dur": 25, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240438, "dur": 21, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240462, "dur": 20, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240484, "dur": 21, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240508, "dur": 20, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240530, "dur": 23, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240555, "dur": 21, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240579, "dur": 20, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240601, "dur": 22, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240624, "dur": 127, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240754, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240780, "dur": 24, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240806, "dur": 21, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240830, "dur": 10, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240842, "dur": 18, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240863, "dur": 20, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240885, "dur": 18, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240905, "dur": 17, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240924, "dur": 21, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240947, "dur": 21, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240970, "dur": 22, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240993, "dur": 1, "ph": "X", "name": "ProcessMessages 167", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998240994, "dur": 22, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241019, "dur": 16, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241037, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241075, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241100, "dur": 126, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241228, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241251, "dur": 26, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241279, "dur": 20, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241301, "dur": 2, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241304, "dur": 29, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241336, "dur": 20, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241358, "dur": 20, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241380, "dur": 22, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241404, "dur": 19, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241425, "dur": 22, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241448, "dur": 1, "ph": "X", "name": "ProcessMessages 103", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241450, "dur": 19, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241471, "dur": 61, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241535, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241558, "dur": 126, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241689, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241731, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241733, "dur": 66, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241804, "dur": 2, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241808, "dur": 47, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241859, "dur": 3, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241863, "dur": 49, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241917, "dur": 2, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241921, "dur": 59, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241983, "dur": 3, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998241987, "dur": 29, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242018, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242020, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242056, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242058, "dur": 43, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242106, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242112, "dur": 55, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242169, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242171, "dur": 26, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242200, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242203, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242247, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242249, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242309, "dur": 3, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242313, "dur": 47, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242364, "dur": 4, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242369, "dur": 42, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242413, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242416, "dur": 38, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242457, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242459, "dur": 47, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242509, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242512, "dur": 32, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242546, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242549, "dur": 36, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242586, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242588, "dur": 36, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242627, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242629, "dur": 47, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242678, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242681, "dur": 36, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242719, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242721, "dur": 33, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242758, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242761, "dur": 34, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242798, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242800, "dur": 24, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242825, "dur": 2, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242828, "dur": 31, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242861, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242863, "dur": 39, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242904, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242906, "dur": 35, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242943, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242946, "dur": 33, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242981, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998242984, "dur": 30, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243016, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243018, "dur": 28, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243048, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243051, "dur": 34, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243087, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243090, "dur": 34, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243127, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243130, "dur": 43, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243174, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243177, "dur": 41, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243221, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243225, "dur": 29, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243257, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243261, "dur": 42, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243306, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243308, "dur": 27, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243337, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243340, "dur": 36, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243378, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243380, "dur": 36, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243418, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243421, "dur": 37, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243461, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243464, "dur": 37, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243505, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243508, "dur": 36, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243546, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243548, "dur": 39, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243590, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243592, "dur": 31, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243625, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243628, "dur": 32, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243662, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243665, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243698, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243700, "dur": 34, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243738, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243740, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243771, "dur": 105, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243881, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243911, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243913, "dur": 33, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243949, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243951, "dur": 39, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243993, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998243996, "dur": 42, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998244041, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998244043, "dur": 35, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998244081, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998244084, "dur": 30, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998244117, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998244119, "dur": 2623, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998246746, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998246749, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998246787, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998246789, "dur": 488, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998247281, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998247313, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998247316, "dur": 276, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998247595, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998247597, "dur": 3425, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998251034, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998251040, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998251091, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998251094, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998251119, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998251183, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998251185, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998251205, "dur": 314, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998251525, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998251565, "dur": 252, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998251823, "dur": 100, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998251929, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998251934, "dur": 28, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998251966, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998251999, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998252042, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998252045, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998252086, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998252088, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998252113, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998252116, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998252143, "dur": 110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998252257, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998252284, "dur": 215, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998252502, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998252535, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998252537, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998252583, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998252608, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998252610, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998252658, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998252684, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998252686, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998252724, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998252757, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998252759, "dur": 126, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998252890, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998252925, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998252928, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998252959, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998252984, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253027, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253052, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253074, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253129, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253164, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253191, "dur": 24, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253218, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253219, "dur": 21, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253243, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253269, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253296, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253361, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253396, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253434, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253438, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253472, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253526, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253553, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253557, "dur": 109, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253670, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253694, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253697, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253742, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253745, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253773, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253775, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253802, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253855, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253887, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253889, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253921, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253945, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998253968, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998254003, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998254046, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998254048, "dur": 26, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998254077, "dur": 108, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998254188, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998254222, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998254245, "dur": 105, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998254352, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998254379, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998254402, "dur": 100, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998254507, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998254533, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998254535, "dur": 64, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998254602, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998254626, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998254651, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998254680, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998254682, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998254715, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998254717, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998254746, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998254768, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998254859, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998254890, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998254892, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998254921, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998254922, "dur": 244, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998255170, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998255200, "dur": 163, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998255368, "dur": 110, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998255482, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998255511, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998255545, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998255547, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998255579, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998255581, "dur": 129, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998255715, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998255734, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998255762, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998255820, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998255822, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998255848, "dur": 122, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998255974, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998256009, "dur": 75, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998256087, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998256120, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998256163, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998256198, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998256200, "dur": 28, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998256232, "dur": 80, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998256315, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998256348, "dur": 424, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998256777, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998256815, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998256816, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998256872, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998256903, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998256933, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998256991, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998257020, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998257049, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998257064, "dur": 206, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998257272, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998257274, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998257317, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998257319, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998257353, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998257387, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998257411, "dur": 248, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998257661, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998257695, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998257715, "dur": 193, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998257910, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998257939, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998257941, "dur": 79, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998258022, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998258049, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998258071, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998258133, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998258166, "dur": 166, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998258334, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998258357, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998258359, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998258406, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998258439, "dur": 77, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998258518, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998258537, "dur": 237, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998258778, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998258802, "dur": 233, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998259037, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998259057, "dur": 81, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998259140, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998259166, "dur": 468, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998259636, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998259669, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998259672, "dur": 133, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998259809, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998259849, "dur": 100, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998259953, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998259982, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998259984, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998260046, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998260082, "dur": 228, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998260313, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998260349, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998260353, "dur": 70129, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998330495, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998330501, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998330571, "dur": 33, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998330606, "dur": 4837, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998335454, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998335458, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998335492, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998335519, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998335521, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998335567, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998335594, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998335596, "dur": 23, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998335622, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998335710, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998335741, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998335770, "dur": 135, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998335910, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998335944, "dur": 197, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998336146, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998336184, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998336186, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998336242, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998336272, "dur": 565, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998336842, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998336875, "dur": 876, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998337756, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998337788, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998337840, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998337868, "dur": 126, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998338000, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998338030, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998338110, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998338137, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998338224, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998338257, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998338295, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998338332, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998338334, "dur": 102, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998338441, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998338474, "dur": 259, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998338738, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998338766, "dur": 317, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998339085, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998339107, "dur": 82, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998339192, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998339216, "dur": 539, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998339758, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998339795, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998339797, "dur": 318, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998340120, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998340146, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998340147, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998340185, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998340211, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998340242, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998340308, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998340334, "dur": 153, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998340489, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998340518, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998340520, "dur": 834, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998341358, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998341386, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998341412, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998341437, "dur": 506, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998341948, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998341977, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342012, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342050, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342081, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342110, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342138, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342160, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342180, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342199, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342228, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342270, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342272, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342300, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342302, "dur": 19, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342323, "dur": 18, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342344, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342365, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342385, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342387, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342411, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342435, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342437, "dur": 29, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342469, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342471, "dur": 24, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342497, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342499, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342529, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342532, "dur": 24, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342558, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342561, "dur": 28, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342592, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342593, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342626, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342628, "dur": 27, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342657, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342659, "dur": 23, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342685, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342686, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342715, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342716, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342744, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342746, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342778, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342862, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342894, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342921, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998342978, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998343013, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998343039, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998343071, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998343073, "dur": 307, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998343386, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998343410, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998343412, "dur": 26, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998343442, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998343472, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998343496, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998343518, "dur": 114, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998343636, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998343671, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998343672, "dur": 118627, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998462310, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998462315, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998462341, "dur": 19, "ph": "X", "name": "ProcessMessages 9335", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998462375, "dur": 63, "ph": "X", "name": "ReadAsync 9335", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998462443, "dur": 27, "ph": "X", "name": "ProcessMessages 4178", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998462472, "dur": 3621, "ph": "X", "name": "ReadAsync 4178", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998466100, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998466103, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998466139, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 2208, "tid": 124554051584, "ts": 1750060998466141, "dur": 6642, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 2208, "tid": 146, "ts": 1750060998473455, "dur": 2145, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 2208, "tid": 120259084288, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 2208, "tid": 120259084288, "ts": 1750060998197995, "dur": 67515, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 2208, "tid": 120259084288, "ts": 1750060998265512, "dur": 2, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 2208, "tid": 120259084288, "ts": 1750060998265515, "dur": 73, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 2208, "tid": 146, "ts": 1750060998475602, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 2208, "tid": 115964116992, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 2208, "tid": 115964116992, "ts": 1750060998194717, "dur": 278132, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 2208, "tid": 115964116992, "ts": 1750060998194820, "dur": 3116, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 2208, "tid": 115964116992, "ts": 1750060998472856, "dur": 70, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 2208, "tid": 115964116992, "ts": 1750060998472874, "dur": 29, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 2208, "tid": 115964116992, "ts": 1750060998472929, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 2208, "tid": 146, "ts": 1750060998475612, "dur": 6, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1750060998213561, "dur": 1806, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060998215382, "dur": 917, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060998216434, "dur": 87, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1750060998216521, "dur": 456, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060998240153, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1750060998217001, "dur": 24309, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060998241325, "dur": 222249, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060998463576, "dur": 274, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060998463850, "dur": 134, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060998465742, "dur": 75, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060998465842, "dur": 1055, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1750060998217921, "dur": 23491, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060998241419, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_3F8B9D82C8E678D4.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750060998241563, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_1DC9F96436961A23.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750060998241736, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_1DC9F96436961A23.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750060998241884, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_7D559772BEA705DB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750060998242291, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_0854A01FF08BC637.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750060998242466, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750060998242661, "dur": 3792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750060998246524, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060998246735, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060998246956, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060998247538, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060998247733, "dur": 690, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Interface\\SharedEditorTextureDictionary.cs"}}, {"pid": 12345, "tid": 1, "ts": 1750060998247733, "dur": 890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060998248623, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060998248831, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060998249015, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060998249262, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060998249445, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060998249658, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060998250048, "dur": 62, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060998250232, "dur": 561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060998250793, "dur": 549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060998251343, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750060998251562, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060998251823, "dur": 822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750060998252646, "dur": 817, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060998253542, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060998253603, "dur": 197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060998253800, "dur": 644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060998254444, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060998254502, "dur": 831, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060998255333, "dur": 4113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060998259449, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750060998259715, "dur": 73349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060998333068, "dur": 2235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750060998335346, "dur": 3874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750060998339221, "dur": 840, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060998340080, "dur": 2009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1750060998342346, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060998342675, "dur": 728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750060998343420, "dur": 120178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998217958, "dur": 23470, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998241434, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_D43581CE70417CB3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750060998241751, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_9DD7D897C42E415F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750060998241882, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998242553, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750060998242645, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750060998242716, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750060998242840, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750060998243055, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1750060998243299, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750060998243452, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998243788, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750060998243855, "dur": 373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998244228, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998244452, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998244753, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998244966, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998245262, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998245866, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998246067, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998246253, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998246477, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998246742, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998246944, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998247202, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998247498, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998248131, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998248354, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998248581, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998248993, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998249207, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998249414, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998249642, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998249917, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998250356, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998250819, "dur": 548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998251368, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750060998251577, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998251700, "dur": 1948, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750060998253649, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998253806, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750060998253951, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750060998254263, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998254498, "dur": 829, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998255327, "dur": 1233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998256562, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750060998256750, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750060998257095, "dur": 2382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998259477, "dur": 73626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998333108, "dur": 1999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750060998335164, "dur": 2361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750060998337527, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998337589, "dur": 2203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750060998339793, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998339875, "dur": 1740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750060998341616, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750060998341703, "dur": 1672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1750060998343408, "dur": 120169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998218085, "dur": 23410, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998241566, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_42BE61A2F375B465.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750060998241806, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_FD977663BC7CDA59.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750060998241892, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_1E5BE8A4F6F01B45.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750060998242397, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750060998242713, "dur": 172, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1750060998243021, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1750060998243299, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750060998243450, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750060998243521, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998243741, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2400031028012695012.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750060998243849, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998244250, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998244536, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998244777, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998245009, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998245269, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998245439, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998246051, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998246268, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998246502, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998246764, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998246982, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998247211, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998247463, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998247652, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998247839, "dur": 605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998248487, "dur": 572, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\StaticFunctionInvoker_2.cs"}}, {"pid": 12345, "tid": 3, "ts": 1750060998248444, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998249219, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998249412, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998249773, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998249944, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998250389, "dur": 451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998250840, "dur": 509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998251377, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750060998251843, "dur": 560, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998252411, "dur": 811, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750060998253309, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750060998253557, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750060998253999, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750060998254118, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750060998254486, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750060998254621, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750060998254929, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998255236, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998255332, "dur": 2364, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998257697, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750060998257811, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750060998258114, "dur": 1364, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998259478, "dur": 73613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998333097, "dur": 2651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750060998335749, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998336003, "dur": 1938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750060998337990, "dur": 2136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750060998340127, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998340253, "dur": 1911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1750060998342421, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750060998342800, "dur": 120811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998218185, "dur": 23381, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998241572, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_4B4D0A166E0D20DC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060998241790, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_40EACCB6DE9B854E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060998241933, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_C9BF491CE9429A6C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060998242643, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1750060998242734, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1750060998242849, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1750060998243054, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750060998243486, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998243780, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998243972, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998244176, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998244384, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998244619, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998244844, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998245138, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998245344, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998245945, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998246581, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998246775, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998246999, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998247284, "dur": 705, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\OnDropdownValueChanged.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750060998247266, "dur": 1343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998248609, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998248805, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998248993, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998249203, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998249397, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998249618, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998249849, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998250355, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998250827, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998251389, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060998251775, "dur": 757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750060998252533, "dur": 380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998252919, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060998253126, "dur": 641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750060998253835, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060998254005, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750060998254483, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060998254670, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750060998255207, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998255321, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998255597, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750060998255733, "dur": 1023, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750060998256812, "dur": 2651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998259464, "dur": 73653, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998333120, "dur": 2012, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750060998335133, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998335451, "dur": 2493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750060998337978, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750060998338067, "dur": 1968, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750060998340088, "dur": 1881, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1750060998342108, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998342484, "dur": 695, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750060998343196, "dur": 120398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998218195, "dur": 23400, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998241605, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_A84A75205A4D3666.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060998241948, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B739A7DF6FD203B7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060998242433, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750060998242590, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1750060998242914, "dur": 212, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750060998243441, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998243808, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998244235, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998244473, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998245153, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998245377, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998246018, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998246230, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998246444, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998246867, "dur": 1633, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@6e225a61188c\\Editor\\Lighting\\IESImporterEditor.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750060998246698, "dur": 1823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998248521, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998248727, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998248962, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998249164, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998249366, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998249577, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998249770, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998249976, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998250296, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998250826, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998251367, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060998251768, "dur": 543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750060998252366, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060998252638, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060998252848, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998252912, "dur": 772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750060998253684, "dur": 451, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998254161, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998254489, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060998254654, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750060998254936, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998255173, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998255333, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750060998255477, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750060998255958, "dur": 3504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998259462, "dur": 73702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998333171, "dur": 2234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750060998335454, "dur": 2483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750060998337938, "dur": 1010, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998338954, "dur": 2136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750060998341091, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750060998341179, "dur": 2189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1750060998343405, "dur": 120173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060998217754, "dur": 23584, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060998241375, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060998241548, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_E6F99946C6EBBC96.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750060998241664, "dur": 194, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_E6F99946C6EBBC96.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750060998241927, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_3EE9BB20E930FDFD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750060998242416, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750060998242584, "dur": 4417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750060998247068, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750060998247307, "dur": 3401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750060998250822, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750060998250937, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750060998251336, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750060998251574, "dur": 622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750060998252196, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060998252492, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750060998252816, "dur": 615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750060998253432, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060998253521, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060998253726, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060998253811, "dur": 626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060998254493, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750060998254676, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750060998255122, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060998255335, "dur": 4114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060998259450, "dur": 5896, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060998266459, "dur": 103, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/6000.0.50f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 6, "ts": 1750060998266563, "dur": 647, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/6000.0.50f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 6, "ts": 1750060998265347, "dur": 1900, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060998267247, "dur": 66270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060998333519, "dur": 3017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750060998336593, "dur": 2702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750060998339296, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750060998339520, "dur": 3667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1750060998343239, "dur": 120348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998218243, "dur": 23366, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998241901, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_1DB8604BF3C2B15B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750060998242516, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750060998242642, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750060998242892, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750060998243051, "dur": 191, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1750060998243458, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998243813, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998244121, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998244284, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998244481, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998244808, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998245054, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998245297, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998246007, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998246191, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998246393, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998246646, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998246839, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998247090, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998247320, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998247517, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998247761, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998247975, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998248157, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998248370, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998248567, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998248769, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998248962, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998249179, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998249378, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998249653, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998249896, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998250356, "dur": 479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998250835, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998251354, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750060998251596, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998251842, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750060998252439, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998252725, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750060998252957, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998253143, "dur": 591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750060998253806, "dur": 636, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998254442, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998254500, "dur": 842, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998255343, "dur": 4130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998259473, "dur": 73599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998333078, "dur": 2315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750060998335394, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998335667, "dur": 3229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750060998338946, "dur": 2137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1750060998341084, "dur": 1249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998342376, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998342508, "dur": 720, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750060998343261, "dur": 120319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998217775, "dur": 23589, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998241378, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998241553, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998241743, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_0713B1E0F0E8C075.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750060998241887, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998242587, "dur": 260, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1750060998243042, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1750060998243105, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1750060998243468, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998243817, "dur": 362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998244179, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998244367, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998244600, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998244833, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998245036, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998245298, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998245930, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998246149, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998246338, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998246575, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998246774, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998247007, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998247341, "dur": 568, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\OnScrollbarValueChanged.cs"}}, {"pid": 12345, "tid": 8, "ts": 1750060998247237, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998248008, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998248234, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998248458, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998249040, "dur": 525, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Utilities\\MiscHelpers.cs"}}, {"pid": 12345, "tid": 8, "ts": 1750060998248984, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998249689, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998250232, "dur": 565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998250797, "dur": 573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998251372, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750060998251597, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750060998251810, "dur": 631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750060998252442, "dur": 343, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998252793, "dur": 770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750060998253653, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998253804, "dur": 642, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998254447, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998254498, "dur": 818, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998255319, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750060998255505, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750060998255950, "dur": 3507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998259458, "dur": 73718, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998333180, "dur": 2093, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750060998335275, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998335342, "dur": 2953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750060998338296, "dur": 544, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750060998338848, "dur": 2230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750060998341114, "dur": 1983, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1750060998343147, "dur": 120455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998217817, "dur": 23558, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998241379, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_1BD15D364A91220E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750060998241558, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_B6C5AA3F81E3AFD4.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750060998241666, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_332FA14DD40CDB89.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750060998241887, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998242319, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750060998242579, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750060998242684, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750060998242861, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1750060998243221, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750060998243350, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1750060998243466, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998243811, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998244170, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998244385, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998244685, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998244889, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998245189, "dur": 648, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@fe5368074162\\Runtime\\2D\\Passes\\Utility\\RendererLighting.cs"}}, {"pid": 12345, "tid": 9, "ts": 1750060998245164, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998246021, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998246220, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998246416, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998246644, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998246837, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998247091, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998247564, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998248106, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998248426, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998248620, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998248827, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998249016, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998249225, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998249541, "dur": 699, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Views\\Merge\\Developer\\MergeOptionsDialog.cs"}}, {"pid": 12345, "tid": 9, "ts": 1750060998249417, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998250323, "dur": 464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998250868, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998251348, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750060998251592, "dur": 634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750060998252285, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750060998252887, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998253036, "dur": 666, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998253705, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998253797, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1750060998253983, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1750060998254513, "dur": 824, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998255338, "dur": 4113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998259451, "dur": 7799, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998267251, "dur": 65805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998333061, "dur": 2279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750060998335378, "dur": 2564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750060998337985, "dur": 2113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1750060998340099, "dur": 1796, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998341933, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998342193, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998342327, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998342612, "dur": 787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1750060998343423, "dur": 120167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998217871, "dur": 23513, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998241389, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_D44F1D7947A8470C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750060998241574, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_46081861FEA49A1E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750060998241738, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_55EFA22634AA8841.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750060998241892, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998242468, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998242549, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1750060998242715, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1750060998243011, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1750060998243257, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1750060998243469, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998243800, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998244056, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998244356, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998244685, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998244893, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998245172, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998245367, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998245970, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998246175, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998246385, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998246606, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998246794, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998246997, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998247206, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998247432, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998247616, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998247809, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998248005, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998248292, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998248490, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998248710, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998249216, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998249436, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998249682, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998249920, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998250423, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998250795, "dur": 545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998251341, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750060998251844, "dur": 787, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750060998252633, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998252971, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750060998253289, "dur": 948, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750060998254279, "dur": 199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998254479, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750060998254619, "dur": 922, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750060998255592, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750060998255689, "dur": 395, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998256092, "dur": 889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750060998257030, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1750060998257148, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1750060998257447, "dur": 2008, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998259456, "dur": 73591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998333052, "dur": 2206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750060998335260, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998335337, "dur": 2200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750060998337539, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998337611, "dur": 2035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750060998339647, "dur": 325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998339978, "dur": 1748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1750060998341821, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998341963, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998342147, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998342464, "dur": 346, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1750060998342814, "dur": 120768, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998217904, "dur": 23492, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998241404, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_1C3753EAD72C002A.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750060998241568, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998241719, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_89505EFF408B1938.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750060998241892, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998242517, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750060998242719, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1750060998242842, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750060998242922, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1750060998243152, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750060998243415, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17346584914308636752.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750060998243507, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998243746, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14814235663552238418.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1750060998243839, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998244252, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998244599, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998244826, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998245055, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998245347, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998245939, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998246130, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998246329, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998246525, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998246722, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998246947, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998247193, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998247433, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998247640, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998247936, "dur": 913, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Dependencies\\ReorderableList\\ReorderableListControl.cs"}}, {"pid": 12345, "tid": 11, "ts": 1750060998247831, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998249069, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998249254, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998249477, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998249682, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998249912, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998250264, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998250801, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998251344, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750060998251909, "dur": 564, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998252479, "dur": 1616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750060998254095, "dur": 323, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1750060998254475, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750060998254616, "dur": 620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750060998255314, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750060998255469, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750060998255861, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750060998255966, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750060998256550, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750060998256688, "dur": 1077, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750060998257803, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750060998257900, "dur": 882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750060998258817, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750060998258907, "dur": 470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750060998259458, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1750060998259565, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750060998259805, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1750060998260130, "dur": 202033, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750060998218096, "dur": 23418, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060998241578, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_6E196A46756374CC.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750060998241737, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_2DE11BE0CCB2F9AC.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750060998241885, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_9C6FA246188F95D9.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750060998241943, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_9CC28255281D0D3B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750060998242038, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_9CC28255281D0D3B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750060998242549, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750060998242859, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750060998243256, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750060998243448, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3064155424177982801.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750060998243518, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060998243712, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12900333509587573630.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1750060998243823, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060998244132, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060998244341, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060998244567, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060998244836, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060998245125, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060998245393, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060998245997, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060998246188, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060998246378, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060998246602, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060998246814, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060998247312, "dur": 1745, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Scalar\\ScalarMaximum.cs"}}, {"pid": 12345, "tid": 12, "ts": 1750060998247139, "dur": 1951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060998249090, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060998249301, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060998249681, "dur": 687, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\UI\\UnityPlasticGuiMessage.cs"}}, {"pid": 12345, "tid": 12, "ts": 1750060998249497, "dur": 890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060998250387, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060998250823, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060998251382, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750060998251740, "dur": 982, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060998252727, "dur": 620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750060998253348, "dur": 419, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060998253807, "dur": 646, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060998254493, "dur": 820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060998255316, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750060998255507, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1750060998255944, "dur": 3499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060998259444, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1750060998259595, "dur": 73487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060998333087, "dur": 2199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750060998335329, "dur": 2074, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750060998337405, "dur": 336, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060998337753, "dur": 2469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750060998340223, "dur": 965, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060998341193, "dur": 1903, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1750060998343097, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1750060998343206, "dur": 120366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998218015, "dur": 23601, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998241734, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_13AFD6B0BB3B8686.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750060998241896, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_136791916CB4C4E9.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750060998242696, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750060998243048, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1750060998243219, "dur": 260, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750060998243501, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998243738, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1750060998243809, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998244054, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998244321, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998244525, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998244774, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998244994, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998245262, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998245855, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998246073, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998246254, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998246458, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998246654, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998246853, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998247098, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998247297, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998247595, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998247789, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998247992, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998248224, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998248478, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998248686, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998248890, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998249108, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998249302, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998249573, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998249764, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998249961, "dur": 730, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@c132dced4a5c\\UnityEditor.TestRunner\\TestLaunchers\\PlayerLauncher.cs"}}, {"pid": 12345, "tid": 13, "ts": 1750060998249961, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998250697, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998250830, "dur": 940, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998251771, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750060998252083, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998252448, "dur": 662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750060998253189, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998253472, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998253542, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998253804, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1750060998253986, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1750060998254393, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998254443, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998254499, "dur": 839, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998255338, "dur": 4114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998259453, "dur": 73597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998333052, "dur": 2147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750060998335200, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998335339, "dur": 2135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750060998337512, "dur": 3899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1750060998341412, "dur": 334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998342228, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Searcher.Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1750060998342501, "dur": 691, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1750060998343192, "dur": 120376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998218045, "dur": 23433, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998241489, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_49F05BCF866287C3.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750060998241573, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_C5179C2B98A35602.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750060998241672, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_69A7891FFD914456.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750060998241906, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_8D701D583C70E786.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750060998242726, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1750060998242916, "dur": 295, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1750060998243305, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1750060998243442, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998243802, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998244105, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998244390, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998244700, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998244920, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998245223, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998245403, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998246020, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998246221, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998246452, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998246664, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998246882, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998247084, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998247317, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998247554, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998247745, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998247928, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998248149, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998248333, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998248559, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998248784, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998248966, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998249185, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998249391, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998249594, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998249844, "dur": 136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998249981, "dur": 85, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998250239, "dur": 550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998250820, "dur": 553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998251375, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750060998252018, "dur": 723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750060998252742, "dur": 678, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998253496, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998253801, "dur": 646, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998254494, "dur": 820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998255317, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750060998255491, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750060998255957, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750060998256073, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750060998256644, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1750060998256759, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1750060998257124, "dur": 2335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998259459, "dur": 73785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998333247, "dur": 2181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750060998335429, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998335513, "dur": 2258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750060998337773, "dur": 409, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998338191, "dur": 4112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1750060998342375, "dur": 352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1750060998342738, "dur": 120853, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998218179, "dur": 23375, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998241559, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_025D68ED9798F53F.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750060998241791, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_75FF79AAC25CE47F.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750060998241927, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_E3FF4E8988E07430.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750060998242507, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_7E6A236E3ABEDC63.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750060998242741, "dur": 216, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1750060998243019, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1750060998243245, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1750060998243400, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1750060998243465, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998243768, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1750060998243843, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998244283, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998244494, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998244781, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998245023, "dur": 838, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@e07c4c789cd8\\Editor\\GraphView\\Views\\Controller\\VFXGraphUndoCursor.cs"}}, {"pid": 12345, "tid": 15, "ts": 1750060998244998, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998246048, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998246240, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998246465, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998246660, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998246905, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998247119, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998247328, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998247627, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998247823, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998248010, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998248193, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998248393, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998248610, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998248798, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998248968, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998249570, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998249752, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998250272, "dur": 519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998250792, "dur": 542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998251337, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750060998251755, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750060998251889, "dur": 742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750060998252631, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998252722, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750060998252955, "dur": 776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750060998253797, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1750060998253968, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1750060998254438, "dur": 460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 15, "ts": 1750060998254925, "dur": 114, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998255333, "dur": 74880, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 15, "ts": 1750060998333048, "dur": 2133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750060998335183, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998335268, "dur": 2032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750060998337301, "dur": 561, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998337872, "dur": 2021, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750060998339946, "dur": 1905, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1750060998341901, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998342204, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998342483, "dur": 653, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1750060998343157, "dur": 120413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998218131, "dur": 23403, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998241542, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_9F0F250247658205.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750060998241856, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_07EF82A58738E0E0.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750060998241954, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_CF814559B1575A99.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750060998242531, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998242663, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750060998242760, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750060998242973, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750060998243109, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750060998243214, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750060998243355, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10142702499866438521.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750060998243463, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998243649, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11294775302662683298.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750060998243743, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9675442845102135732.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1750060998243800, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998244006, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998244340, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998244642, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998244846, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998245109, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998245319, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998245921, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998246129, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998246313, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998246976, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998247195, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998247378, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998247763, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998248065, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998248300, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998248480, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998248697, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998249035, "dur": 567, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Plugins\\UI\\InputSystemUIInputModuleEditor.cs"}}, {"pid": 12345, "tid": 16, "ts": 1750060998249015, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998249765, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998249966, "dur": 381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998250347, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998250803, "dur": 561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998251366, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750060998251615, "dur": 681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1750060998252297, "dur": 681, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998252987, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998253151, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998253509, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998253610, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998253800, "dur": 640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998254441, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998254503, "dur": 827, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998255331, "dur": 1701, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998257033, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750060998257176, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1750060998257693, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750060998257798, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1750060998258184, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1750060998258286, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1750060998258552, "dur": 902, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998259454, "dur": 73599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998333055, "dur": 2521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750060998335577, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998335655, "dur": 235, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750060998335894, "dur": 2319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750060998338215, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998338456, "dur": 2626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750060998341120, "dur": 1898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1750060998343019, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1750060998343201, "dur": 120365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750060998470769, "dur": 1207, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 2208, "tid": 146, "ts": 1750060998475648, "dur": 543, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 2208, "tid": 146, "ts": 1750060998476226, "dur": 7955, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 2208, "tid": 146, "ts": 1750060998473422, "dur": 10796, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}