#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using TMPro;
using UnityEngine.UI;
using Player;

namespace PlayerSystem.Editor
{
    /// <summary>
    /// Custom Editor cho PickupSystemManager
    /// Cung cấp tools setup nhanh và debug
    /// </summary>
    [CustomEditor(typeof(PickupSystemManager))]
    public class PickupSystemEditor : UnityEditor.Editor
    {
        private PickupSystemManager m_Target;
        private bool m_ShowSetupTools = true;
        private bool m_ShowDebugTools = false;
        private bool m_ShowCreateTools = false;

        private void OnEnable()
        {
            m_Target = (PickupSystemManager)target;
        }

        public override void OnInspectorGUI()
        {
            // Header
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("🎯 PICKUP SYSTEM MANAGER", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            // Default inspector
            DrawDefaultInspector();

            EditorGUILayout.Space();
            EditorGUILayout.LabelField("🛠️ TOOLS", EditorStyles.boldLabel);

            // Setup Tools
            m_ShowSetupTools = EditorGUILayout.Foldout(m_ShowSetupTools, "⚙️ Setup Tools");
            if (m_ShowSetupTools)
            {
                EditorGUILayout.BeginVertical("box");
                DrawSetupTools();
                EditorGUILayout.EndVertical();
            }

            // Debug Tools
            m_ShowDebugTools = EditorGUILayout.Foldout(m_ShowDebugTools, "🔍 Debug Tools");
            if (m_ShowDebugTools)
            {
                EditorGUILayout.BeginVertical("box");
                DrawDebugTools();
                EditorGUILayout.EndVertical();
            }

            // Create Tools
            m_ShowCreateTools = EditorGUILayout.Foldout(m_ShowCreateTools, "🎨 Create Tools");
            if (m_ShowCreateTools)
            {
                EditorGUILayout.BeginVertical("box");
                DrawCreateTools();
                EditorGUILayout.EndVertical();
            }
        }

        private void DrawSetupTools()
        {
            EditorGUILayout.LabelField("Quick Setup", EditorStyles.miniBoldLabel);

            if (GUILayout.Button("🚀 Auto Setup All Components"))
            {
                AutoSetupAll();
            }

            if (GUILayout.Button("🎨 Create Pickup UI"))
            {
                CreatePickupUI();
            }

            if (GUILayout.Button("🔧 Setup Player References"))
            {
                SetupPlayerReferences();
            }

            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Validation", EditorStyles.miniBoldLabel);

            if (GUILayout.Button("✅ Validate Setup"))
            {
                ValidateSetup();
            }
        }

        private void DrawDebugTools()
        {
            EditorGUILayout.LabelField("System Status", EditorStyles.miniBoldLabel);

            // Hiển thị trạng thái components
            GUI.enabled = false;
            EditorGUILayout.Toggle("PickupSystem", m_Target.PickupSystem != null);
            EditorGUILayout.Toggle("PickupUI", m_Target.PickupUI != null);
            EditorGUILayout.Toggle("PickupEffects", m_Target.PickupEffects != null);
            EditorGUILayout.Toggle("PlayerInventory", m_Target.PlayerInventory != null);
            GUI.enabled = true;

            EditorGUILayout.Space();

            if (Application.isPlaying)
            {
                EditorGUILayout.LabelField("Runtime Info", EditorStyles.miniBoldLabel);
                EditorGUILayout.LabelField($"Is Carrying: {m_Target.IsCarryingItems()}");
                EditorGUILayout.LabelField($"Carried Count: {m_Target.GetCarriedItemsCount()}");
                
                var currentItem = m_Target.GetCurrentDetectedItem();
                EditorGUILayout.LabelField($"Detected Item: {(currentItem?.TenHienThi ?? "None")}");
            }

            if (GUILayout.Button("📋 Log System Status"))
            {
                LogSystemStatus();
            }
        }

        private void DrawCreateTools()
        {
            EditorGUILayout.LabelField("Create Pickup Items", EditorStyles.miniBoldLabel);

            if (GUILayout.Button("📦 Create Test Pickup Item"))
            {
                CreateTestPickupItem();
            }

            if (GUILayout.Button("🎯 Setup Selected as Pickup"))
            {
                SetupSelectedAsPickup();
            }

            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Batch Operations", EditorStyles.miniBoldLabel);

            if (GUILayout.Button("🔄 Convert All Cubes to Pickups"))
            {
                ConvertCubesToPickups();
            }
        }

        #region Setup Methods
        private void AutoSetupAll()
        {
            Undo.RecordObject(m_Target, "Auto Setup Pickup System");

            // Tìm hoặc tạo PickupSystem
            if (m_Target.PickupSystem == null)
            {
                var pickupSystem = m_Target.GetComponent<PickupSystem>();
                if (pickupSystem == null)
                {
                    pickupSystem = m_Target.gameObject.AddComponent<PickupSystem>();
                }
            }

            // Tìm hoặc tạo PickupEffects
            if (m_Target.PickupEffects == null)
            {
                var pickupEffects = m_Target.GetComponent<PickupEffects>();
                if (pickupEffects == null)
                {
                    pickupEffects = m_Target.gameObject.AddComponent<PickupEffects>();
                }
            }

            // Tìm PlayerInventory
            if (m_Target.PlayerInventory == null)
            {
                var inventory = FindObjectOfType<EconomySystem.PlayerInventory>();
                if (inventory != null)
                {
                    SerializedObject so = new SerializedObject(m_Target);
                    so.FindProperty("m_PlayerInventory").objectReferenceValue = inventory;
                    so.ApplyModifiedProperties();
                }
            }

            EditorUtility.SetDirty(m_Target);
            Debug.Log("✅ Auto Setup hoàn tất!");
        }

        private void CreatePickupUI()
        {
            // Tìm Canvas
            Canvas canvas = FindObjectOfType<Canvas>();
            if (canvas == null)
            {
                // Tạo Canvas mới
                GameObject canvasObj = new GameObject("PickupCanvas");
                canvas = canvasObj.AddComponent<Canvas>();
                canvas.renderMode = RenderMode.ScreenSpaceOverlay;
                canvasObj.AddComponent<CanvasScaler>();
                canvasObj.AddComponent<GraphicRaycaster>();
            }

            // Tạo PickupUI Panel
            GameObject uiPanel = new GameObject("PickupUI");
            uiPanel.transform.SetParent(canvas.transform, false);
            
            RectTransform rectTransform = uiPanel.AddComponent<RectTransform>();
            rectTransform.anchorMin = new Vector2(0.5f, 0.8f);
            rectTransform.anchorMax = new Vector2(0.5f, 0.8f);
            rectTransform.sizeDelta = new Vector2(300, 100);

            // Thêm PickupUI component
            var pickupUI = uiPanel.AddComponent<PickupUI>();

            // Tạo Main Panel
            GameObject mainPanel = new GameObject("MainPanel");
            mainPanel.transform.SetParent(uiPanel.transform, false);
            var mainPanelRect = mainPanel.AddComponent<RectTransform>();
            mainPanelRect.anchorMin = Vector2.zero;
            mainPanelRect.anchorMax = Vector2.one;
            mainPanelRect.offsetMin = Vector2.zero;
            mainPanelRect.offsetMax = Vector2.zero;
            
            var mainPanelImage = mainPanel.AddComponent<Image>();
            mainPanelImage.color = new Color(0, 0, 0, 0.7f);

            // Tạo Text components
            CreateTextComponent(mainPanel, "TextTenVatPham", "Tên Vật Phẩm", new Vector2(0, 20));
            CreateTextComponent(mainPanel, "TextHuongDan", "Nhấn E để nhặt", new Vector2(0, -20));

            // Assign references
            SerializedObject so = new SerializedObject(pickupUI);
            so.FindProperty("m_MainPanel").objectReferenceValue = mainPanel;
            so.FindProperty("m_TextTenVatPham").objectReferenceValue = mainPanel.transform.Find("TextTenVatPham").GetComponent<TextMeshProUGUI>();
            so.FindProperty("m_TextHuongDan").objectReferenceValue = mainPanel.transform.Find("TextHuongDan").GetComponent<TextMeshProUGUI>();
            so.ApplyModifiedProperties();

            // Assign PickupUI to PickupSystemManager
            SerializedObject managerSO = new SerializedObject(m_Target);
            managerSO.FindProperty("m_PickupUI").objectReferenceValue = pickupUI;
            managerSO.ApplyModifiedProperties();

            Debug.Log("✅ Đã tạo Pickup UI!");
        }

        private GameObject CreateTextComponent(GameObject parent, string name, string text, Vector2 position)
        {
            GameObject textObj = new GameObject(name);
            textObj.transform.SetParent(parent.transform, false);
            
            RectTransform rectTransform = textObj.AddComponent<RectTransform>();
            rectTransform.anchoredPosition = position;
            rectTransform.sizeDelta = new Vector2(280, 30);
            
            TextMeshProUGUI textComponent = textObj.AddComponent<TextMeshProUGUI>();
            textComponent.text = text;
            textComponent.fontSize = 16;
            textComponent.color = Color.white;
            textComponent.alignment = TextAlignmentOptions.Center;
            
            return textObj;
        }

        private void SetupPlayerReferences()
        {
            SerializedObject so = new SerializedObject(m_Target);
            
            // Tìm PlayerInventory
            var inventory = FindObjectOfType<EconomySystem.PlayerInventory>();
            if (inventory != null)
            {
                so.FindProperty("m_PlayerInventory").objectReferenceValue = inventory;
            }

            // Tìm EconomySystemManager
            var economySystem = FindObjectOfType<EconomySystem.EconomySystemManager>();
            if (economySystem != null)
            {
                so.FindProperty("m_EconomySystem").objectReferenceValue = economySystem;
            }

            so.ApplyModifiedProperties();
            EditorUtility.SetDirty(m_Target);
            
            Debug.Log("✅ Đã setup Player references!");
        }

        private void ValidateSetup()
        {
            bool isValid = true;
            string report = "=== PICKUP SYSTEM VALIDATION ===\n";

            // Kiểm tra components
            if (m_Target.PickupSystem == null)
            {
                report += "❌ PickupSystem: Missing\n";
                isValid = false;
            }
            else
            {
                report += "✅ PickupSystem: OK\n";
            }

            if (m_Target.PickupUI == null)
            {
                report += "⚠️ PickupUI: Missing (Optional)\n";
            }
            else
            {
                report += "✅ PickupUI: OK\n";
            }

            if (m_Target.PickupEffects == null)
            {
                report += "⚠️ PickupEffects: Missing (Optional)\n";
            }
            else
            {
                report += "✅ PickupEffects: OK\n";
            }

            if (m_Target.PlayerInventory == null)
            {
                report += "⚠️ PlayerInventory: Missing (Items won't be stored)\n";
            }
            else
            {
                report += "✅ PlayerInventory: OK\n";
            }

            report += $"\nOverall Status: {(isValid ? "✅ READY" : "❌ NEEDS SETUP")}";
            Debug.Log(report);
        }
        #endregion

        #region Create Methods
        private void CreateTestPickupItem()
        {
            GameObject testItem = GameObject.CreatePrimitive(PrimitiveType.Cube);
            testItem.name = "TestPickupItem";
            testItem.transform.position = m_Target.transform.position + Vector3.forward * 2f;
            
            var pickupItem = testItem.AddComponent<PickupItem>();
            testItem.tag = "Pickupable";
            
            // Thiết lập thông tin test
            SerializedObject so = new SerializedObject(pickupItem);
            so.FindProperty("m_ItemId").stringValue = "test_item";
            so.FindProperty("m_TenHienThi").stringValue = "Test Item";
            so.FindProperty("m_MoTa").stringValue = "Vật phẩm test cho pickup system";
            so.ApplyModifiedProperties();

            Selection.activeGameObject = testItem;
            Debug.Log("✅ Đã tạo Test Pickup Item!");
        }

        private void SetupSelectedAsPickup()
        {
            if (Selection.activeGameObject == null)
            {
                Debug.LogWarning("Vui lòng chọn một GameObject trước!");
                return;
            }

            GameObject selected = Selection.activeGameObject;
            
            // Thêm PickupItem nếu chưa có
            if (selected.GetComponent<PickupItem>() == null)
            {
                selected.AddComponent<PickupItem>();
            }

            // Đảm bảo có Collider
            if (selected.GetComponent<Collider>() == null)
            {
                selected.AddComponent<BoxCollider>();
            }

            // Thiết lập tag
            selected.tag = "Pickupable";

            Debug.Log($"✅ Đã setup {selected.name} thành pickup item!");
        }

        private void ConvertCubesToPickups()
        {
            GameObject[] allObjects = FindObjectsOfType<GameObject>();
            int convertedCount = 0;

            foreach (GameObject obj in allObjects)
            {
                if (obj.name.Contains("Cube") && obj.GetComponent<PickupItem>() == null)
                {
                    obj.AddComponent<PickupItem>();
                    if (obj.GetComponent<Collider>() == null)
                    {
                        obj.AddComponent<BoxCollider>();
                    }
                    obj.tag = "Pickupable";
                    convertedCount++;
                }
            }

            Debug.Log($"✅ Đã convert {convertedCount} cubes thành pickup items!");
        }

        private void LogSystemStatus()
        {
            string status = "=== PICKUP SYSTEM STATUS ===\n";
            status += $"PickupSystem: {(m_Target.PickupSystem != null ? "✅" : "❌")}\n";
            status += $"PickupUI: {(m_Target.PickupUI != null ? "✅" : "❌")}\n";
            status += $"PickupEffects: {(m_Target.PickupEffects != null ? "✅" : "❌")}\n";
            status += $"PlayerInventory: {(m_Target.PlayerInventory != null ? "✅" : "❌")}\n";

            if (Application.isPlaying)
            {
                status += $"Is Carrying: {m_Target.IsCarryingItems()}\n";
                status += $"Carried Count: {m_Target.GetCarriedItemsCount()}\n";
                var currentItem = m_Target.GetCurrentDetectedItem();
                status += $"Current Detected: {(currentItem?.TenHienThi ?? "None")}\n";
            }

            Debug.Log(status);
        }
        #endregion
    }
}
#endif
