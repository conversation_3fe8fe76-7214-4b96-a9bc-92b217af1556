# 🚀 HƯỚNG DẪN SETUP NHANH - HỆ THỐNG PICKUP

## ✨ Tính Năng Chính
- ✅ **Ấn E để pickup** vật phẩm có tag
- ✅ **Hover text** hiện lên khi đưa chuột vào vật
- ✅ **Visual feedback** (highlight, outline, glow)
- ✅ **UI prompt** hiển thị thông tin vật phẩm
- ✅ **Bobbing animation** cho vật phẩm
- ✅ **Tích hợp với inventory** hiện có

---

## 🎯 SETUP 5 PHÚT

### Bước 1: Thêm vào Player
1. Chọn **Player GameObject** trong Hierarchy
2. **Add Component** → `PickupSystemManager`
3. Trong Inspector, nhấn **"Auto Setup All Components"**
4. Nhấn **"Create Pickup UI"**
5. Nhấn **"Validate Setup"**

### Bước 2: Tạo Vật Phẩm Test
1. Trong Inspector của PickupSystemManager
2. Nhấn **"Create Test Pickup Item"**
3. Hoặc chọn GameObject bất kỳ → nhấn **"Setup Selected as Pickup"**

### Bước 3: Test
1. **Play game**
2. **Nhìn vào vật phẩm** → thấy highlight và UI
3. **Nhấn E** để pickup
4. **Nhấn E lại** để drop

**🎉 XONG! Hệ thống hoạt động!**

---

## 🔧 CÁC SCRIPT ĐÃ TẠO

### Core Scripts
- **`PickupSystemManager.cs`** - Manager chính (dùng script này)
- **`PickupSystem.cs`** - Logic pickup
- **`PickupItem.cs`** - Component cho vật phẩm
- **`PickupUI.cs`** - Quản lý UI
- **`PickupEffects.cs`** - Hiệu ứng visual/audio

### Support Scripts
- **`PickupInventoryAdapter.cs`** - Tích hợp với inventory hiện có
- **`PickupSystemTester.cs`** - Test và debug

---

## 🎮 CÁCH SỬ DỤNG

### Trong Game
- **Nhìn vào vật phẩm** → Thấy highlight và hover text
- **Nhấn E** → Pickup vật phẩm
- **Nhấn E khi đang mang** → Drop vật phẩm

### Tạo Vật Phẩm Pickup
```csharp
// Cách 1: Sử dụng Manager (khuyến nghị)
PickupSystemManager manager = FindObjectOfType<PickupSystemManager>();
GameObject newItem = manager.TaoVatPhamPickup(
    transform.position + Vector3.forward * 2f,
    "wood", 5, "Gỗ", "Nguyên liệu chế tạo"
);

// Cách 2: Setup GameObject có sẵn
manager.ThietLapPickupChoGameObject(
    someGameObject, "stone", 3, "Đá", "Vật liệu xây dựng"
);
```

### Kiểm Tra Trạng Thái
```csharp
PickupSystemManager manager = FindObjectOfType<PickupSystemManager>();

// Kiểm tra có đang mang vật không
bool isCarrying = manager.IsCarryingItems();

// Lấy vật phẩm hiện tại
PickupItem currentItem = manager.GetCurrentDetectedItem();
PickupItem carriedItem = manager.GetCarriedItem();
```

---

## ⚙️ CÀI ĐẶT TRONG INSPECTOR

### PickupSystemManager
- **Auto Setup**: Tự động thiết lập components
- **Debug Logs**: Hiển thị log để debug

### PickupSystem
- **Khoảng Cách Tối Đa**: 3.0f (khoảng cách phát hiện)
- **Pickup Tag**: "Pickupable" (tag cho vật phẩm)
- **Phím Pickup**: E (phím để pickup)
- **Tần Suất Check**: 10 Hz (hiệu suất)

### PickupItem
- **Item ID**: ID duy nhất cho vật phẩm
- **Tên Hiển Thị**: Tên hiện trong UI
- **Số Lượng**: Số lượng vật phẩm
- **Có Bobbing**: Animation lên xuống
- **Highlight Material**: Material khi highlight

---

## 🐛 TROUBLESHOOTING

### UI Không Hiển Thị
- ✅ Kiểm tra Canvas có trong scene
- ✅ Nhấn "Create Pickup UI" trong Manager
- ✅ Kiểm tra PickupUI component

### Không Pickup Được
- ✅ Kiểm tra tag "Pickupable"
- ✅ Kiểm tra Collider trên vật phẩm
- ✅ Kiểm tra khoảng cách (mặc định 3m)
- ✅ Kiểm tra Input System

### Inventory Không Hoạt Động
- ✅ Kiểm tra PlayerInventory trong scene
- ✅ Kiểm tra EconomySystem.PlayerInventory
- ✅ Script tự động tích hợp với hệ thống hiện có

---

## 🎯 HOTKEYS TEST

Khi có **PickupSystemTester** trong scene:
- **T** - Tạo vật phẩm test
- **C** - Xóa tất cả vật phẩm
- **R** - Reset test
- **E** - Pickup/Drop (trong game)

---

## 📚 TÍNH NĂNG NÂNG CAO

### Custom Pickup Types
```csharp
public class WeaponPickup : PickupItem
{
    [SerializeField] private WeaponData weaponData;
    
    public override bool TryPickup(PlayerInventory inventory)
    {
        // Custom logic cho weapon
        return base.TryPickup(inventory);
    }
}
```

### Events Integration
```csharp
// Lắng nghe pickup events
PickupSystemManager manager = FindObjectOfType<PickupSystemManager>();
// Events sẽ được thêm trong phiên bản sau
```

---

## 🔄 NEXT STEPS

1. **Test hệ thống** với vật phẩm khác nhau
2. **Tùy chỉnh UI** theo design game
3. **Thêm sound effects** và particle effects
4. **Tích hợp với quest system** nếu có
5. **Thêm animation** cho pickup/drop

---

## 💡 TIPS

- Sử dụng **LayerMask** để tối ưu raycast
- Thiết lập **tag "Pickupable"** cho tất cả vật phẩm
- Sử dụng **PickupSystemManager** thay vì script riêng lẻ
- **Debug logs** giúp hiểu cách hệ thống hoạt động
- **Validate Setup** để kiểm tra cài đặt

**🎮 Chúc bạn có trải nghiệm tuyệt vời với hệ thống pickup!**
