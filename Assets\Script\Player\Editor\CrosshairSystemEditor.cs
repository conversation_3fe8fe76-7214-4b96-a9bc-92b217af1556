#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using UnityEngine.UI;
using TMPro;

namespace PlayerSystem.Editor
{
    /// <summary>
    /// Editor cho Crosshair System
    /// Hỗ trợ setup tự động và validation
    /// </summary>
    [CustomEditor(typeof(CrosshairController))]
    public class CrosshairControllerEditor : UnityEditor.Editor
    {
        private CrosshairController m_Target;
        
        private void OnEnable()
        {
            m_Target = (CrosshairController)target;
        }
        
        public override void OnInspectorGUI()
        {
            DrawDefaultInspector();
            
            EditorGUILayout.Space(10);
            EditorGUILayout.LabelField("🛠️ CROSSHAIR SETUP TOOLS", EditorStyles.boldLabel);
            
            EditorGUILayout.BeginVertical("box");
            
            // Auto Setup Button
            if (GUILayout.Button("🚀 Auto Setup Crosshair System", GUILayout.Height(30)))
            {
                AutoSetupCrosshairSystem();
            }
            
            EditorGUILayout.Space(5);
            
            // Create Crosshair UI Button
            if (GUILayout.Button("🎯 Create Crosshair UI", GUILayout.Height(25)))
            {
                CreateCrosshairUI();
            }
            
            // Validate Setup Button
            if (GUILayout.Button("✅ Validate Setup", GUILayout.Height(25)))
            {
                ValidateSetup();
            }
            
            EditorGUILayout.Space(5);
            
            // Test Buttons
            EditorGUILayout.LabelField("🧪 Test Controls", EditorStyles.boldLabel);
            
            if (Application.isPlaying)
            {
                EditorGUILayout.BeginHorizontal();
                
                if (GUILayout.Button("Show"))
                {
                    m_Target.HienThiCrosshair();
                }
                
                if (GUILayout.Button("Hide"))
                {
                    m_Target.AnCrosshair();
                }
                
                EditorGUILayout.EndHorizontal();
                
                EditorGUILayout.BeginHorizontal();
                
                if (GUILayout.Button("Normal"))
                {
                    m_Target.ThayDoiTrangThai(CrosshairController.CrosshairState.Normal);
                }
                
                if (GUILayout.Button("Can Interact"))
                {
                    m_Target.ThayDoiTrangThai(CrosshairController.CrosshairState.CanInteract);
                }
                
                if (GUILayout.Button("Cannot Interact"))
                {
                    m_Target.ThayDoiTrangThai(CrosshairController.CrosshairState.CannotInteract);
                }
                
                EditorGUILayout.EndHorizontal();
                
                EditorGUILayout.BeginHorizontal();
                
                if (GUILayout.Button("Cross"))
                {
                    m_Target.ThayDoiKieu(CrosshairController.CrosshairType.Cross);
                }
                
                if (GUILayout.Button("Dot"))
                {
                    m_Target.ThayDoiKieu(CrosshairController.CrosshairType.Dot);
                }
                
                if (GUILayout.Button("Circle"))
                {
                    m_Target.ThayDoiKieu(CrosshairController.CrosshairType.Circle);
                }
                
                EditorGUILayout.EndHorizontal();
            }
            else
            {
                EditorGUILayout.HelpBox("Test controls chỉ hoạt động trong Play mode", MessageType.Info);
            }
            
            EditorGUILayout.EndVertical();
        }
        
        private void AutoSetupCrosshairSystem()
        {
            Debug.Log("[CrosshairEditor] Bắt đầu Auto Setup Crosshair System...");
            
            // Tìm hoặc tạo Canvas
            Canvas canvas = FindOrCreateCanvas();
            
            // Tạo CrosshairUI nếu chưa có
            CrosshairUI crosshairUI = FindOrCreateCrosshairUI(canvas);
            
            // Gán references
            SerializedObject so = new SerializedObject(m_Target);
            
            var crosshairUIProp = so.FindProperty("m_CrosshairUI");
            if (crosshairUIProp != null)
            {
                crosshairUIProp.objectReferenceValue = crosshairUI;
            }
            
            var canvasProp = so.FindProperty("m_Canvas");
            if (canvasProp != null)
            {
                canvasProp.objectReferenceValue = canvas;
            }
            
            var cameraProp = so.FindProperty("m_PlayerCamera");
            if (cameraProp != null && cameraProp.objectReferenceValue == null)
            {
                cameraProp.objectReferenceValue = Camera.main ?? FindObjectOfType<Camera>();
            }
            
            so.ApplyModifiedProperties();
            
            Debug.Log("[CrosshairEditor] ✅ Auto Setup hoàn tất!");
            EditorUtility.DisplayDialog("Crosshair Setup", "Auto Setup Crosshair System hoàn tất!", "OK");
        }
        
        private Canvas FindOrCreateCanvas()
        {
            // Tìm Canvas hiện có
            Canvas[] canvases = FindObjectsOfType<Canvas>();
            foreach (Canvas existingCanvas in canvases)
            {
                if (existingCanvas.renderMode == RenderMode.ScreenSpaceOverlay)
                {
                    return existingCanvas;
                }
            }

            // Tạo Canvas mới
            GameObject canvasObj = new GameObject("CrosshairCanvas");
            Canvas newCanvas = canvasObj.AddComponent<Canvas>();
            newCanvas.renderMode = RenderMode.ScreenSpaceOverlay;
            newCanvas.sortingOrder = 1000;

            canvasObj.AddComponent<CanvasScaler>();
            canvasObj.AddComponent<GraphicRaycaster>();

            Debug.Log("[CrosshairEditor] Đã tạo Canvas mới");
            return newCanvas;
        }
        
        private CrosshairUI FindOrCreateCrosshairUI(Canvas canvas)
        {
            // Tìm CrosshairUI hiện có
            CrosshairUI existingUI = FindObjectOfType<CrosshairUI>();
            if (existingUI != null)
            {
                return existingUI;
            }
            
            // Tạo CrosshairUI mới
            return CreateCrosshairUIStructure(canvas);
        }
        
        private void CreateCrosshairUI()
        {
            Canvas canvas = FindOrCreateCanvas();
            CrosshairUI crosshairUI = CreateCrosshairUIStructure(canvas);
            
            // Gán vào CrosshairController
            SerializedObject so = new SerializedObject(m_Target);
            var crosshairUIProp = so.FindProperty("m_CrosshairUI");
            if (crosshairUIProp != null)
            {
                crosshairUIProp.objectReferenceValue = crosshairUI;
            }
            so.ApplyModifiedProperties();
            
            Debug.Log("[CrosshairEditor] ✅ Đã tạo Crosshair UI!");
            EditorUtility.DisplayDialog("Crosshair UI", "Đã tạo Crosshair UI thành công!", "OK");
        }
        
        private CrosshairUI CreateCrosshairUIStructure(Canvas canvas)
        {
            // Tạo CrosshairUI GameObject
            GameObject crosshairUIObj = new GameObject("CrosshairUI");
            crosshairUIObj.transform.SetParent(canvas.transform, false);
            
            RectTransform uiRect = crosshairUIObj.AddComponent<RectTransform>();
            uiRect.anchorMin = new Vector2(0.5f, 0.5f);
            uiRect.anchorMax = new Vector2(0.5f, 0.5f);
            uiRect.anchoredPosition = Vector2.zero;
            uiRect.sizeDelta = new Vector2(50, 50);
            
            CrosshairUI crosshairUI = crosshairUIObj.AddComponent<CrosshairUI>();
            
            // Tạo Container
            GameObject container = new GameObject("CrosshairContainer");
            container.transform.SetParent(crosshairUIObj.transform, false);
            
            RectTransform containerRect = container.AddComponent<RectTransform>();
            containerRect.anchorMin = Vector2.zero;
            containerRect.anchorMax = Vector2.one;
            containerRect.offsetMin = Vector2.zero;
            containerRect.offsetMax = Vector2.zero;
            
            // Tạo Cross Crosshair
            GameObject crossObj = CreateCrossCrosshair(container.transform);
            
            // Tạo Dot Crosshair
            GameObject dotObj = CreateDotCrosshair(container.transform);
            
            // Tạo Circle Crosshair
            GameObject circleObj = CreateCircleCrosshair(container.transform);
            
            // Gán references vào CrosshairUI
            SerializedObject so = new SerializedObject(crosshairUI);
            
            so.FindProperty("m_CrosshairContainer").objectReferenceValue = container;
            so.FindProperty("m_CrosshairCross").objectReferenceValue = crossObj;
            so.FindProperty("m_CrosshairDot").objectReferenceValue = dotObj;
            so.FindProperty("m_CrosshairCircle").objectReferenceValue = circleObj;
            
            // Gán Cross components
            Transform crossTransform = crossObj.transform;
            so.FindProperty("m_CrossHorizontal").objectReferenceValue = crossTransform.Find("Horizontal")?.GetComponent<Image>();
            so.FindProperty("m_CrossVertical").objectReferenceValue = crossTransform.Find("Vertical")?.GetComponent<Image>();
            
            // Gán Dot components
            so.FindProperty("m_DotCenter").objectReferenceValue = dotObj.transform.Find("Center")?.GetComponent<Image>();
            
            // Gán Circle components
            Transform circleTransform = circleObj.transform;
            so.FindProperty("m_CircleOuter").objectReferenceValue = circleTransform.Find("Outer")?.GetComponent<Image>();
            so.FindProperty("m_CircleInner").objectReferenceValue = circleTransform.Find("Inner")?.GetComponent<Image>();
            
            so.ApplyModifiedProperties();
            
            Debug.Log("[CrosshairEditor] Đã tạo Crosshair UI structure");
            return crosshairUI;
        }
        
        private GameObject CreateCrossCrosshair(Transform parent)
        {
            GameObject crossObj = new GameObject("CrosshairCross");
            crossObj.transform.SetParent(parent, false);
            
            // Horizontal bar
            GameObject horizontal = new GameObject("Horizontal");
            horizontal.transform.SetParent(crossObj.transform, false);
            Image hImage = horizontal.AddComponent<Image>();
            hImage.color = Color.white;
            
            RectTransform hRect = horizontal.GetComponent<RectTransform>();
            hRect.sizeDelta = new Vector2(20, 2);
            
            // Vertical bar
            GameObject vertical = new GameObject("Vertical");
            vertical.transform.SetParent(crossObj.transform, false);
            Image vImage = vertical.AddComponent<Image>();
            vImage.color = Color.white;
            
            RectTransform vRect = vertical.GetComponent<RectTransform>();
            vRect.sizeDelta = new Vector2(2, 20);
            
            return crossObj;
        }
        
        private GameObject CreateDotCrosshair(Transform parent)
        {
            GameObject dotObj = new GameObject("CrosshairDot");
            dotObj.transform.SetParent(parent, false);
            dotObj.SetActive(false);
            
            GameObject center = new GameObject("Center");
            center.transform.SetParent(dotObj.transform, false);
            Image centerImage = center.AddComponent<Image>();
            centerImage.color = Color.white;
            
            RectTransform centerRect = center.GetComponent<RectTransform>();
            centerRect.sizeDelta = new Vector2(4, 4);
            
            return dotObj;
        }
        
        private GameObject CreateCircleCrosshair(Transform parent)
        {
            GameObject circleObj = new GameObject("CrosshairCircle");
            circleObj.transform.SetParent(parent, false);
            circleObj.SetActive(false);
            
            // Outer circle
            GameObject outer = new GameObject("Outer");
            outer.transform.SetParent(circleObj.transform, false);
            Image outerImage = outer.AddComponent<Image>();
            outerImage.color = Color.white;
            
            RectTransform outerRect = outer.GetComponent<RectTransform>();
            outerRect.sizeDelta = new Vector2(20, 20);
            
            // Inner dot
            GameObject inner = new GameObject("Inner");
            inner.transform.SetParent(circleObj.transform, false);
            Image innerImage = inner.AddComponent<Image>();
            innerImage.color = Color.white;
            
            RectTransform innerRect = inner.GetComponent<RectTransform>();
            innerRect.sizeDelta = new Vector2(2, 2);
            
            return circleObj;
        }
        
        private void ValidateSetup()
        {
            bool isValid = true;
            string issues = "";
            
            // Kiểm tra CrosshairUI
            SerializedObject so = new SerializedObject(m_Target);
            var crosshairUIProp = so.FindProperty("m_CrosshairUI");
            if (crosshairUIProp.objectReferenceValue == null)
            {
                isValid = false;
                issues += "- Chưa gán CrosshairUI\n";
            }
            
            // Kiểm tra Canvas
            var canvasProp = so.FindProperty("m_Canvas");
            if (canvasProp.objectReferenceValue == null)
            {
                isValid = false;
                issues += "- Chưa gán Canvas\n";
            }
            
            // Kiểm tra Camera
            var cameraProp = so.FindProperty("m_PlayerCamera");
            if (cameraProp.objectReferenceValue == null)
            {
                isValid = false;
                issues += "- Chưa gán Player Camera\n";
            }
            
            if (isValid)
            {
                EditorUtility.DisplayDialog("Validation", "✅ Setup hợp lệ! Crosshair System sẵn sàng sử dụng.", "OK");
            }
            else
            {
                EditorUtility.DisplayDialog("Validation", $"❌ Phát hiện vấn đề:\n{issues}\nVui lòng chạy Auto Setup để khắc phục.", "OK");
            }
        }
    }


}
#endif
