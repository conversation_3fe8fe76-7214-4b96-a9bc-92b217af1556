using UnityEngine;

namespace Player
{
    /// <summary>
    /// Script test cho hệ thống pickup
    /// Tạo các vật phẩm test và kiểm tra chức năng
    /// </summary>
    public class PickupSystemTester : MonoBehaviour
    {
        #region Serialized Fields
        [Header("🧪 Test Settings")]
        [SerializeField] private int m_SoLuongVatPhamTest = 5;
        [SerializeField] private float m_KhoangCachTaoVat = 2f;
        [SerializeField] private bool m_TuDongTaoVatPham = true;

        [Header("🎯 Test Items")]
        [SerializeField] private string[] m_TenVatPham = { "Gỗ", "Đ<PERSON>", "Táo", "Kiế<PERSON>", "Khiên" };
        [SerializeField] private string[] m_ItemIds = { "wood", "stone", "apple", "sword", "shield" };

        [Header("🔧 References")]
        [SerializeField] private PickupSystemManager m_PickupManager;
        #endregion

        #region Unity Lifecycle
        private void Start()
        {
            // Tìm PickupSystemManager
            if (m_PickupManager == null)
            {
                m_PickupManager = FindObjectOfType<PickupSystemManager>();
            }

            if (m_TuDongTaoVatPham)
            {
                TaoVatPhamTest();
            }
        }

        private void Update()
        {
            // Test hotkeys
            if (Input.GetKeyDown(KeyCode.T))
            {
                TaoVatPhamTest();
            }

            if (Input.GetKeyDown(KeyCode.C))
            {
                XoaTatCaVatPham();
            }

            if (Input.GetKeyDown(KeyCode.R))
            {
                ResetTest();
            }
        }
        #endregion

        #region Test Methods
        /// <summary>
        /// Tạo các vật phẩm test xung quanh player
        /// </summary>
        [ContextMenu("Tạo Vật Phẩm Test")]
        public void TaoVatPhamTest()
        {
            if (m_PickupManager == null)
            {
                Debug.LogError("Không tìm thấy PickupSystemManager!");
                return;
            }

            Vector3 playerPos = transform.position;
            
            for (int i = 0; i < m_SoLuongVatPhamTest; i++)
            {
                // Tính vị trí ngẫu nhiên xung quanh player
                float angle = (360f / m_SoLuongVatPhamTest) * i;
                Vector3 direction = new Vector3(
                    Mathf.Cos(angle * Mathf.Deg2Rad),
                    0,
                    Mathf.Sin(angle * Mathf.Deg2Rad)
                );
                
                Vector3 position = playerPos + direction * m_KhoangCachTaoVat;
                position.y = playerPos.y + 0.5f; // Nâng lên một chút

                // Lấy thông tin vật phẩm
                string tenVatPham = i < m_TenVatPham.Length ? m_TenVatPham[i] : $"Vật Phẩm {i + 1}";
                string itemId = i < m_ItemIds.Length ? m_ItemIds[i] : $"item_{i + 1}";
                int soLuong = Random.Range(1, 6);

                // Tạo vật phẩm
                GameObject newItem = m_PickupManager.TaoVatPhamPickup(
                    position, 
                    itemId, 
                    soLuong, 
                    tenVatPham, 
                    $"Một {tenVatPham.ToLower()} có thể nhặt"
                );

                // Thêm màu ngẫu nhiên
                Renderer renderer = newItem.GetComponent<Renderer>();
                if (renderer != null)
                {
                    renderer.material.color = Random.ColorHSV();
                }
            }

            Debug.Log($"✅ Đã tạo {m_SoLuongVatPhamTest} vật phẩm test!");
        }

        /// <summary>
        /// Xóa tất cả vật phẩm pickup
        /// </summary>
        [ContextMenu("Xóa Tất Cả Vật Phẩm")]
        public void XoaTatCaVatPham()
        {
            PickupItem[] allPickupItems = FindObjectsOfType<PickupItem>();
            int count = 0;

            foreach (PickupItem item in allPickupItems)
            {
                if (item != null)
                {
                    DestroyImmediate(item.gameObject);
                    count++;
                }
            }

            Debug.Log($"✅ Đã xóa {count} vật phẩm!");
        }

        /// <summary>
        /// Reset test - xóa và tạo lại
        /// </summary>
        [ContextMenu("Reset Test")]
        public void ResetTest()
        {
            XoaTatCaVatPham();
            TaoVatPhamTest();
            Debug.Log("🔄 Đã reset test!");
        }

        /// <summary>
        /// Test force pickup tất cả vật phẩm
        /// </summary>
        [ContextMenu("Force Pickup Tất Cả")]
        public void ForcePickupTatCa()
        {
            if (m_PickupManager == null) return;

            PickupItem[] allPickupItems = FindObjectsOfType<PickupItem>();
            int count = 0;

            foreach (PickupItem item in allPickupItems)
            {
                if (item != null && item.CoTheNhat)
                {
                    bool success = m_PickupManager.ForcePickupItem(item);
                    if (success) count++;
                }
            }

            Debug.Log($"✅ Đã force pickup {count} vật phẩm!");
        }

        /// <summary>
        /// Tạo vật phẩm tại vị trí chuột
        /// </summary>
        public void TaoVatPhamTaiChuot()
        {
            if (m_PickupManager == null) return;

            Camera cam = Camera.main ?? FindObjectOfType<Camera>();
            if (cam == null) return;

            Ray ray = cam.ScreenPointToRay(Input.mousePosition);
            if (Physics.Raycast(ray, out RaycastHit hit))
            {
                Vector3 position = hit.point + Vector3.up * 0.5f;
                
                string tenVatPham = m_TenVatPham[Random.Range(0, m_TenVatPham.Length)];
                string itemId = m_ItemIds[Random.Range(0, m_ItemIds.Length)];
                int soLuong = Random.Range(1, 4);

                m_PickupManager.TaoVatPhamPickup(
                    position, 
                    itemId, 
                    soLuong, 
                    tenVatPham, 
                    $"Một {tenVatPham.ToLower()} được tạo tại vị trí chuột"
                );

                Debug.Log($"✅ Đã tạo {tenVatPham} tại vị trí chuột!");
            }
        }
        #endregion

        #region Info Display
        private void OnGUI()
        {
            if (!Application.isPlaying) return;

            GUILayout.BeginArea(new Rect(10, 10, 300, 200));
            GUILayout.Label("🧪 PICKUP SYSTEM TESTER", GUI.skin.box);
            
            GUILayout.Label($"Hotkeys:");
            GUILayout.Label("T - Tạo vật phẩm test");
            GUILayout.Label("C - Xóa tất cả vật phẩm");
            GUILayout.Label("R - Reset test");
            GUILayout.Label("E - Pickup/Drop (trong game)");

            GUILayout.Space(10);

            if (GUILayout.Button("Tạo Vật Phẩm Test"))
            {
                TaoVatPhamTest();
            }

            if (GUILayout.Button("Xóa Tất Cả"))
            {
                XoaTatCaVatPham();
            }

            if (GUILayout.Button("Force Pickup Tất Cả"))
            {
                ForcePickupTatCa();
            }

            GUILayout.Space(10);

            // Hiển thị thông tin hệ thống
            if (m_PickupManager != null)
            {
                GUILayout.Label($"Đang mang: {(m_PickupManager.IsCarryingItems() ? "Có" : "Không")}");
                GUILayout.Label($"Số vật phẩm: {m_PickupManager.GetCarriedItemsCount()}");
                
                PickupItem currentItem = m_PickupManager.GetCurrentDetectedItem();
                GUILayout.Label($"Phát hiện: {(currentItem != null ? currentItem.TenHienThi : "Không")}");
            }

            GUILayout.EndArea();
        }
        #endregion

        #region Utility
        /// <summary>
        /// Thiết lập số lượng vật phẩm test
        /// </summary>
        public void ThietLapSoLuongTest(int soLuong)
        {
            m_SoLuongVatPhamTest = Mathf.Clamp(soLuong, 1, 20);
        }

        /// <summary>
        /// Thiết lập khoảng cách tạo vật
        /// </summary>
        public void ThietLapKhoangCach(float khoangCach)
        {
            m_KhoangCachTaoVat = Mathf.Clamp(khoangCach, 1f, 10f);
        }
        #endregion

        #region Editor Helpers
#if UNITY_EDITOR
        [ContextMenu("Validate Pickup System")]
        private void ValidatePickupSystem()
        {
            if (m_PickupManager == null)
            {
                Debug.LogError("❌ Không tìm thấy PickupSystemManager!");
                return;
            }

            bool isValid = m_PickupManager.ValidateSetup();
            if (isValid)
            {
                Debug.Log("✅ Pickup System hợp lệ!");
            }
            else
            {
                Debug.LogError("❌ Pickup System có vấn đề!");
            }
        }
#endif
        #endregion
    }
}
