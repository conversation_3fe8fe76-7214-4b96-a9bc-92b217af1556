using UnityEngine;
using EconomySystem;

namespace Player
{
    /// <summary>
    /// Manager tổng thể cho hệ thống pickup
    /// Tự động thiết lập và quản lý các component
    /// Cung cấp API đơn giản cho việc sử dụng
    /// </summary>
    public class PickupSystemManager : MonoBehaviour
    {
        #region Serialized Fields
        [Header("🎮 System Components")]
        [SerializeField] private PickupSystem m_PickupSystem;
        [SerializeField] private PickupUI m_PickupUI;
        [SerializeField] private PickupEffects m_PickupEffects;

        [Header("🔧 External References")]
        [SerializeField] private PlayerInventory m_PlayerInventory;
        [SerializeField] private EconomySystemManager m_EconomySystemManager;

        [Header("⚙️ Auto Setup")]
        [SerializeField] private bool m_TuDongThietLap = true;
        [SerializeField] private bool m_TuDongTimReferences = true;

        [Header("🐛 Debug")]
        [SerializeField] private bool m_EnableDebugLogs = true;
        #endregion

        #region Private Fields
        private bool m_IsInitialized;
        #endregion

        #region Properties
        public PickupSystem PickupSystem => m_PickupSystem;
        public PickupUI PickupUI => m_PickupUI;
        public PickupEffects PickupEffects => m_PickupEffects;
        public PlayerInventory PlayerInventory => m_PlayerInventory;
        public EconomySystemManager EconomySystemManager => m_EconomySystemManager;
        public bool IsInitialized => m_IsInitialized;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            if (m_TuDongThietLap)
            {
                TuDongThietLapTatCa();
            }
        }

        private void Start()
        {
            KhoiTaoHeThong();
        }
        #endregion

        #region Auto Setup
        /// <summary>
        /// Tự động thiết lập tất cả components
        /// </summary>
        [ContextMenu("Auto Setup All Components")]
        public void TuDongThietLapTatCa()
        {
            Log("Bắt đầu auto setup...");

            // Tìm hoặc tạo PickupSystem
            if (m_PickupSystem == null)
            {
                m_PickupSystem = GetComponent<PickupSystem>();
                if (m_PickupSystem == null)
                {
                    m_PickupSystem = gameObject.AddComponent<PickupSystem>();
                    Log("Đã thêm PickupSystem component");
                }
            }

            // Tìm PickupUI
            if (m_PickupUI == null && m_TuDongTimReferences)
            {
                m_PickupUI = FindObjectOfType<PickupUI>();
                if (m_PickupUI != null)
                {
                    Log("Đã tìm thấy PickupUI");
                }
            }

            // Tìm hoặc tạo PickupEffects
            if (m_PickupEffects == null)
            {
                m_PickupEffects = GetComponent<PickupEffects>();
                if (m_PickupEffects == null)
                {
                    m_PickupEffects = gameObject.AddComponent<PickupEffects>();
                    Log("Đã thêm PickupEffects component");
                }
            }

            // Tìm external references
            if (m_TuDongTimReferences)
            {
                TimExternalReferences();
            }

            Log("Auto setup hoàn thành!");
        }

        private void TimExternalReferences()
        {
            // Tìm PlayerInventory
            if (m_PlayerInventory == null)
            {
                m_PlayerInventory = FindObjectOfType<PlayerInventory>();
                if (m_PlayerInventory != null)
                {
                    Log("Đã tìm thấy PlayerInventory");
                }
            }

            // Tìm EconomySystemManager
            if (m_EconomySystemManager == null)
            {
                m_EconomySystemManager = FindObjectOfType<EconomySystemManager>();
                if (m_EconomySystemManager != null)
                {
                    Log("Đã tìm thấy EconomySystemManager");
                }
            }
        }

        /// <summary>
        /// Kiểm tra và validate setup
        /// </summary>
        [ContextMenu("Validate Setup")]
        public bool ValidateSetup()
        {
            bool isValid = true;
            
            Log("Đang kiểm tra setup...");

            // Kiểm tra core components
            if (m_PickupSystem == null)
            {
                LogLoi("Thiếu PickupSystem component!");
                isValid = false;
            }

            if (m_PickupUI == null)
            {
                LogCanhBao("Thiếu PickupUI - UI sẽ không hoạt động");
            }

            if (m_PlayerInventory == null)
            {
                LogCanhBao("Thiếu PlayerInventory - vật phẩm sẽ không được thêm vào inventory");
            }

            // Kiểm tra camera
            Camera playerCamera = Camera.main ?? FindObjectOfType<Camera>();
            if (playerCamera == null)
            {
                LogLoi("Không tìm thấy Camera!");
                isValid = false;
            }

            if (isValid)
            {
                Log("✅ Setup hợp lệ!");
            }
            else
            {
                LogLoi("❌ Setup có vấn đề!");
            }

            return isValid;
        }
        #endregion

        #region System Management
        private void KhoiTaoHeThong()
        {
            if (m_IsInitialized) return;

            Log("Khởi tạo Pickup System Manager...");

            // Validate setup
            if (!ValidateSetup())
            {
                LogLoi("Setup không hợp lệ, hệ thống có thể không hoạt động đúng!");
            }

            m_IsInitialized = true;
            Log("Pickup System Manager đã sẵn sàng!");
        }
        #endregion

        #region Public API
        /// <summary>
        /// Kiểm tra có đang mang vật phẩm không
        /// </summary>
        public bool IsCarryingItems()
        {
            return m_PickupSystem != null && m_PickupSystem.IsCarryingItems;
        }

        /// <summary>
        /// Lấy số lượng vật phẩm đang mang
        /// </summary>
        public int GetCarriedItemsCount()
        {
            return m_PickupSystem != null ? m_PickupSystem.CarriedItemsCount : 0;
        }

        /// <summary>
        /// Lấy vật phẩm hiện tại đang được phát hiện
        /// </summary>
        public PickupItem GetCurrentDetectedItem()
        {
            return m_PickupSystem != null ? m_PickupSystem.CurrentDetectedItem : null;
        }

        /// <summary>
        /// Lấy vật phẩm đang mang
        /// </summary>
        public PickupItem GetCarriedItem()
        {
            return m_PickupSystem != null ? m_PickupSystem.CarriedItem : null;
        }

        /// <summary>
        /// Tạo vật phẩm pickup tại vị trí
        /// </summary>
        public GameObject TaoVatPhamPickup(Vector3 position, string itemId, int soLuong, string tenHienThi, string moTa = "")
        {
            // Tạo GameObject
            GameObject newItem = GameObject.CreatePrimitive(PrimitiveType.Cube);
            newItem.name = $"Pickup_{tenHienThi}";
            newItem.transform.position = position;
            newItem.tag = "Pickupable";

            // Thêm PickupItem component
            PickupItem pickupItem = newItem.AddComponent<PickupItem>();
            pickupItem.ThietLapVatPham(itemId, soLuong, tenHienThi, moTa);

            Log($"Đã tạo vật phẩm pickup: {tenHienThi} tại {position}");
            return newItem;
        }

        /// <summary>
        /// Thiết lập GameObject thành pickup item
        /// </summary>
        public PickupItem ThietLapPickupChoGameObject(GameObject obj, string itemId, int soLuong, string tenHienThi, string moTa = "")
        {
            if (obj == null) return null;

            // Đảm bảo có Collider
            if (obj.GetComponent<Collider>() == null)
            {
                obj.AddComponent<BoxCollider>();
            }

            // Thiết lập tag
            obj.tag = "Pickupable";

            // Thêm hoặc lấy PickupItem component
            PickupItem pickupItem = obj.GetComponent<PickupItem>();
            if (pickupItem == null)
            {
                pickupItem = obj.AddComponent<PickupItem>();
            }

            // Thiết lập thông tin
            pickupItem.ThietLapVatPham(itemId, soLuong, tenHienThi, moTa);

            Log($"Đã thiết lập {obj.name} thành pickup item: {tenHienThi}");
            return pickupItem;
        }

        /// <summary>
        /// Force pickup một vật phẩm
        /// </summary>
        public bool ForcePickupItem(PickupItem item)
        {
            if (item == null) return false;

            bool success = item.TryPickup(m_PlayerInventory);
            if (success)
            {
                Log($"Force pickup thành công: {item.TenHienThi}");
            }
            else
            {
                LogLoi($"Force pickup thất bại: {item.TenHienThi}");
            }

            return success;
        }

        /// <summary>
        /// Hiển thị UI pickup
        /// </summary>
        public void HienThiPickupUI(PickupItem item)
        {
            if (m_PickupUI != null)
            {
                m_PickupUI.HienThiUI(item);
            }
        }

        /// <summary>
        /// Ẩn UI pickup
        /// </summary>
        public void AnPickupUI()
        {
            if (m_PickupUI != null)
            {
                m_PickupUI.AnUI();
            }
        }
        #endregion

        #region Utility
        private void Log(string message)
        {
            if (m_EnableDebugLogs)
                Debug.Log($"[PickupSystemManager] {message}");
        }

        private void LogCanhBao(string message)
        {
            if (m_EnableDebugLogs)
                Debug.LogWarning($"[PickupSystemManager] {message}");
        }

        private void LogLoi(string message)
        {
            if (m_EnableDebugLogs)
                Debug.LogError($"[PickupSystemManager] {message}");
        }
        #endregion

        #region Editor Helpers
#if UNITY_EDITOR
        [ContextMenu("Create Test Pickup Item")]
        private void CreateTestPickupItem()
        {
            Vector3 position = transform.position + transform.forward * 2f;
            TaoVatPhamPickup(position, "test_item", 1, "Test Item", "Vật phẩm test cho pickup system");
        }

        [ContextMenu("Setup Selected as Pickup")]
        private void SetupSelectedAsPickup()
        {
            if (UnityEditor.Selection.activeGameObject != null)
            {
                GameObject selected = UnityEditor.Selection.activeGameObject;
                ThietLapPickupChoGameObject(selected, "item_" + selected.name.ToLower(), 1, selected.name, "Vật phẩm có thể nhặt");
            }
            else
            {
                LogCanhBao("Vui lòng chọn một GameObject trước!");
            }
        }
#endif
        #endregion
    }
}
