# 🎯 HƯỚNG DẪN CÀI ĐẶT HỆ THỐNG PICKUP

## 📋 Tổng Quan

Hệ thống Pickup hoàn chỉnh cho phép người chơi nhặt và thả các vật thể trong game với đầy đủ tính năng:
- ✅ Phát hiện vật thể bằng raycast từ camera
- ✅ Tương tác bằng phím E
- ✅ **Hover text hiển thị khi di chuột vào vật**
- ✅ Visual feedback (highlight, outline, glow)
- ✅ UI prompt hiển thị thông tin vật phẩm
- ✅ Tích hợp với PlayerInventory
- ✅ Particle effects và sound effects
- ✅ Cấu hình đầy đủ trong Inspector
- ✅ Bobbing animation cho vật phẩm
- ✅ Screen shake effects

---

## 🚀 Cài Đặt Nhanh (5 Phút)

### Bước 1: Thêm PickupSystemManager vào Player
1. Chọn **Player GameObject** trong Hierarchy
2. Add Component → **PickupSystemManager**
3. Trong Inspector, nhấn **"Auto Setup All Components"**
4. Nhấn **"Create Pickup UI"** để tạo UI tự động
5. Nhấn **"Validate Setup"** để kiểm tra

### Bước 2: Tạo Vật Phẩm Test
1. Trong Inspector của PickupSystemManager, nhấn **"Create Test Pickup Item"**
2. Hoặc chọn GameObject bất kỳ → nhấn **"Setup Selected as Pickup"**

### Bước 3: Test Hệ Thống
1. **Chạy game**
2. **Nhìn vào vật phẩm** → thấy highlight và UI prompt
3. **Nhấn E** để nhặt vật phẩm
4. **Nhấn E lần nữa** để thả vật phẩm

**🎉 Xong! Hệ thống đã hoạt động với hover text!**

### Phương Pháp 2: Setup Thủ Công
#### Bước 1: Thêm Scripts vào Player
1. Chọn Player GameObject trong Hierarchy
2. Add Component → **PickupSystem**
3. Add Component → **PickupEffects** (tùy chọn)
4. Add Component → **PickupSystemManager** (khuyến nghị)

#### Bước 2: Thiết Lập UI
1. Tạo Canvas cho Pickup UI (nếu chưa có)
2. Tạo Panel con với tên "PickupPrompt"
3. Thêm TextMeshPro components:
   - `TextTenVatPham` - hiển thị tên vật phẩm
   - `TextHuongDan` - hiển thị hướng dẫn
4. Add Component → **PickupUI** vào Canvas
5. Kéo thả các UI elements vào PickupUI script

#### Bước 3: Thiết Lập Vật Phẩm
1. Chọn GameObject muốn làm vật phẩm
2. Add Component → **PickupItem**
3. Đảm bảo có Collider
4. Thiết lập tag "Pickupable" (hoặc tag tùy chỉnh)

---

## 🔧 Cài Đặt Chi Tiết

### A. Thiết Lập PickupSystemManager (Khuyến Nghị)

#### 1. System Components
```
🎮 PickupSystem: Tự động tìm hoặc tạo
🎮 PickupUI: Tự động tìm
🎮 PickupEffects: Tự động tìm hoặc tạo
```

#### 2. External References
```
🔧 PlayerInventory: Tự động tìm
🔧 EconomySystemManager: Tự động tìm
```

#### 3. Auto Setup
```
⚙️ Tự Động Thiết Lập: ✅
⚙️ Tự Động Tìm References: ✅
```

#### 4. Editor Tools
- **Auto Setup All**: Thiết lập tự động toàn bộ
- **Create Pickup UI**: Tạo UI tự động
- **Validate Setup**: Kiểm tra cài đặt
- **Create Test Item**: Tạo vật phẩm test
- **Setup Selected as Pickup**: Biến GameObject thành pickup

### B. Thiết Lập PickupSystem

#### 1. Cài Đặt Detection
```
🎯 Khoảng Cách Tối Đa: 3.0f
🎯 Layer Mask: Everything (-1)
🎯 Pickup Tag: "Pickupable"
🎯 Tần Suất Check: 10 Hz
```

#### 2. Cài Đặt Input
```
🎮 Phím Pickup: E
🎮 Sử Dụng Input System: ✅
```

#### 3. Cài Đặt Carrying
```
📦 Carry Point: Tự động tạo hoặc assign Transform
📦 Khoảng Cách Mang: 2.0f
📦 Tốc Độ Smooth: 10.0f
📦 Có Thể Mang Nhiều: ❌ (khuyến nghị)
```

#### 4. UI References
```
🎨 Pickup Canvas: Canvas chứa UI
🎨 Prompt Panel: Panel hiển thị prompt
🎨 Text Tên Vật Phẩm: TextMeshPro
🎨 Text Hướng Dẫn: TextMeshPro
🎨 Text Carrying Status: TextMeshPro
```

#### 5. System References
```
🔧 Player Inventory: Tự động tìm hoặc assign
🔧 Input Handler: Tự động tìm hoặc assign
🔧 Player Camera: Tự động tìm hoặc assign
```

### B. Thiết Lập PickupItem

#### 1. Thông Tin Vật Phẩm
```
🎯 Item ID: "wood", "stone", "apple"...
🎯 Số Lượng: 1
🎯 Tên Hiển Thị: "Gỗ", "Đá", "Táo"...
🎯 Mô Tả: "Một miếng gỗ có thể dùng để chế tạo"
```

#### 2. Cài Đặt Pickup
```
🎮 Pickup Tag: "Pickupable"
🎮 Có Thể Nhặt: ✅
🎮 Tự Động Thêm Vào Inventory: ✅
🎮 Xóa Sau Khi Nhặt: ✅
```

#### 3. Hiệu Ứng Visual
```
🎨 Highlight Material: Material có outline/glow
🎨 Outline Color: Yellow
🎨 Outline Width: 0.1f
🎨 Có Bobbing: ✅
🎨 Tốc Độ Bobbing: 1.0f
🎨 Độ Cao Bobbing: 0.1f
```

#### 4. Hiệu Ứng Âm Thanh
```
🔊 Sound Highlight: Âm thanh khi hover
🔊 Sound Pickup: Âm thanh khi nhặt
🔊 Sound Cannot Pickup: Âm thanh lỗi
```

#### 5. Particle Effects
```
✨ Effect Highlight: Particle khi highlight
✨ Effect Pickup: Particle khi nhặt
```

### C. Thiết Lập PickupUI

#### 1. UI Components
```
🎨 Main Panel: Panel chính
🎨 Text Tên Vật Phẩm: Hiển thị tên
🎨 Text Số Lượng: Hiển thị số lượng
🎨 Text Mô Tả: Hiển thị mô tả
🎨 Text Hướng Dẫn: Hiển thị phím
🎨 Icon Vật Phẩm: Image icon (tùy chọn)
🎨 Progress Bar: Thanh tiến trình (tùy chọn)
```

#### 2. Crosshair
```
🎯 Crosshair Normal: Crosshair bình thường
🎯 Crosshair Pickup: Crosshair khi có thể pickup
🎯 Crosshair Carrying: Crosshair khi đang mang
```

#### 3. Animation Settings
```
🎨 Tốc Độ Fade: 5.0f
🎨 Có Scale Animation: ✅
🎨 Scale Ban Đầu: 0.8f
🎨 Thời Gian Scale: 0.3f
```

#### 4. Visual Effects
```
🎨 Màu Text Bình Thường: White
🎨 Màu Text Highlight: Yellow
🎨 Màu Text Không Thể Pickup: Red
```

### D. Thiết Lập PickupEffects (Tùy Chọn)

#### 1. Audio Effects
```
🎵 Sound Highlight: Âm thanh hover
🎵 Sound Pickup Success: Âm thanh thành công
🎵 Sound Pickup Fail: Âm thanh thất bại
🎵 Sound Drop: Âm thanh thả
🎵 Sound Volume: 1.0f
```

#### 2. Particle Effects
```
✨ Effect Pickup Prefab: Prefab particle pickup
✨ Effect Drop Prefab: Prefab particle drop
✨ Effect Highlight Prefab: Prefab particle highlight
✨ Effect Lifetime: 3.0f
```

#### 3. Screen Effects
```
📺 Có Screen Flash: ✅
📺 Màu Screen Flash: White (Alpha 0.1)
📺 Thời Gian Screen Flash: 0.2f
📺 Screen Flash Image: UI Image fullscreen
```

#### 4. Highlight Effects
```
🎯 Outline Material: Material có outline shader
🎯 Màu Outline: Yellow
🎯 Độ Dày Outline: 0.02f
🎯 Có Glow Effect: ✅
🎯 Cường Độ Glow: 2.0f
```

#### 5. Animation Effects
```
🌊 Có Bobbing: ✅
🌊 Tốc Độ Bobbing: 2.0f
🌊 Độ Cao Bobbing: 0.1f
🌊 Có Rotation: ✅
🌊 Tốc Độ Rotation: 30.0f
```

---

## 🎮 Cách Sử Dụng

### Trong Game
1. **Nhặt vật phẩm**: Nhìn vào vật phẩm → Nhấn E
2. **Thả vật phẩm**: Khi đang mang vật → Nhấn E
3. **Xem thông tin**: Hover chuột lên vật phẩm

### Trong Code

#### Sử Dụng PickupSystemManager (Khuyến Nghị)
```csharp
// Lấy reference
PickupSystemManager manager = FindObjectOfType<PickupSystemManager>();

// Kiểm tra trạng thái
bool isCarrying = manager.IsCarryingItems();
int itemCount = manager.GetCarriedItemsCount();
PickupItem currentItem = manager.GetCurrentDetectedItem();

// Tạo vật phẩm pickup
GameObject newItem = manager.TaoVatPhamPickup(
    transform.position + Vector3.forward * 2f,
    "wood", 5, "Gỗ", "Nguyên liệu chế tạo"
);

// Thiết lập GameObject thành pickup
PickupItem pickupItem = manager.ThietLapPickupChoGameObject(
    someGameObject, "stone", 3, "Đá", "Vật liệu xây dựng"
);

// Force pickup
bool success = manager.ForcePickupItem(pickupItem);
```

#### Sử Dụng Trực Tiếp
```csharp
// Lấy reference
PickupSystem pickupSystem = FindObjectOfType<PickupSystem>();

// Kiểm tra trạng thái
bool isCarrying = pickupSystem.IsCarryingItems;
int itemCount = pickupSystem.CarriedItemsCount;

// Thiết lập vật phẩm
PickupItem item = someObject.GetComponent<PickupItem>();
item.ThietLapVatPham("wood", 5, "Gỗ", "Nguyên liệu chế tạo");

// Highlight thủ công
item.Highlight(true);

// Pickup thủ công
PlayerInventory inventory = FindObjectOfType<PlayerInventory>();
bool success = item.TryPickup(inventory);
```

---

## 🔍 Troubleshooting

### Vấn Đề Thường Gặp

#### 1. Không Detect Được Vật Phẩm
- ✅ Kiểm tra tag "Pickupable"
- ✅ Kiểm tra Layer Mask
- ✅ Kiểm tra khoảng cách
- ✅ Đảm bảo có Collider

#### 2. UI Không Hiển Thị
- ✅ Kiểm tra Canvas reference
- ✅ Kiểm tra UI components
- ✅ Kiểm tra Canvas Group alpha

#### 3. Không Có Âm Thanh
- ✅ Kiểm tra AudioSource
- ✅ Kiểm tra AudioClip assignments
- ✅ Kiểm tra volume settings

#### 4. Hiệu Ứng Không Hoạt Động
- ✅ Kiểm tra Particle System prefabs
- ✅ Kiểm tra Material assignments
- ✅ Kiểm tra shader properties

#### 5. Input Không Hoạt Động
- ✅ Kiểm tra PlayerInputHandler
- ✅ Kiểm tra Input System setup
- ✅ Kiểm tra key bindings

---

## 📝 Ghi Chú

### Performance Tips
- Sử dụng object pooling cho particle effects
- Throttle detection frequency (10Hz khuyến nghị)
- Sử dụng LayerMask để giới hạn raycast

### Customization
- Tạo custom materials cho highlight effects
- Tạo custom particle effects
- Tạo custom sound effects
- Tạo custom UI themes

### Integration
- Tích hợp với Quest System
- Tích hợp với Crafting System
- Tích hợp với Save/Load System
- Tích hợp với Multiplayer

---

## 🎯 Kết Luận

Hệ thống Pickup đã được thiết kế để:
- ✅ Dễ sử dụng và cấu hình
- ✅ Tích hợp tốt với hệ thống hiện có
- ✅ Hiệu suất tối ưu
- ✅ Có thể mở rộng và tùy chỉnh
- ✅ Tuân thủ coding conventions Unity

Để được hỗ trợ thêm, vui lòng tham khảo documentation hoặc liên hệ team phát triển.
