using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections;

namespace Player
{
    /// <summary>
    /// Quản lý UI cho hệ thống pickup
    /// Hiển thị thông tin vật phẩm và hướng dẫn
    /// Hỗ trợ animation và crosshair integration
    /// </summary>
    public class PickupUI : MonoBehaviour
    {
        #region Serialized Fields
        [Header("🎨 UI Components")]
        [SerializeField] private GameObject m_MainPanel;
        [SerializeField] private TextMeshProUGUI m_TextTenVatPham;
        [SerializeField] private TextMeshProUGUI m_TextSoLuong;
        [SerializeField] private TextMeshProUGUI m_TextMoTa;
        [SerializeField] private TextMeshProUGUI m_TextHuongDan;
        [SerializeField] private Image m_IconVatPham;
        [SerializeField] private Slider m_ProgressBar;

        [Header("🎯 Crosshair")]
        [SerializeField] private Image m_CrosshairNormal;
        [SerializeField] private Image m_CrosshairPickup;
        [SerializeField] private Image m_CrosshairCarrying;

        [Header("🎬 Animation Settings")]
        [SerializeField] private float m_TocDoFade = 5.0f;
        [SerializeField] private bool m_CoScaleAnimation = true;
        [SerializeField] private float m_ScaleBanDau = 0.8f;
        [SerializeField] private float m_ThoiGianScale = 0.3f;

        [Header("🎨 Style Settings")]
        [SerializeField] private Color m_MauTextBinhThuong = Color.white;
        [SerializeField] private Color m_MauTextHighlight = Color.yellow;
        [SerializeField] private Color m_MauTextLoi = Color.red;

        [Header("🐛 Debug")]
        [SerializeField] private bool m_EnableDebugLogs = true;
        #endregion

        #region Private Fields
        private CanvasGroup m_CanvasGroup;
        private bool m_UIVisible;
        private Coroutine m_AnimationCoroutine;
        private PickupItem m_CurrentItem;
        #endregion

        #region Properties
        public bool UIVisible => m_UIVisible;
        public PickupItem CurrentItem => m_CurrentItem;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            KhoiTaoComponents();
        }

        private void Start()
        {
            ThietLapUI();
        }
        #endregion

        #region Initialization
        private void KhoiTaoComponents()
        {
            // Lấy hoặc tạo CanvasGroup
            m_CanvasGroup = GetComponent<CanvasGroup>();
            if (m_CanvasGroup == null)
            {
                m_CanvasGroup = gameObject.AddComponent<CanvasGroup>();
            }

            // Tự động tìm components nếu chưa assign
            if (m_MainPanel == null)
                m_MainPanel = transform.GetChild(0)?.gameObject;

            if (m_TextTenVatPham == null)
                m_TextTenVatPham = GetComponentInChildren<TextMeshProUGUI>();
        }

        private void ThietLapUI()
        {
            // Ẩn UI ban đầu
            AnUI();

            // Thiết lập crosshair ban đầu
            ThietLapCrosshair(CrosshairState.Normal);

            Log("Pickup UI đã được thiết lập");
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Hiển thị UI với thông tin vật phẩm
        /// </summary>
        public void HienThiUI(PickupItem item)
        {
            if (item == null) return;

            m_CurrentItem = item;
            CapNhatThongTinVatPham(item);
            HienThiUI();
        }

        /// <summary>
        /// Hiển thị UI
        /// </summary>
        public void HienThiUI()
        {
            if (m_UIVisible) return;

            m_UIVisible = true;
            
            if (m_MainPanel != null)
                m_MainPanel.SetActive(true);

            // Animation fade in
            if (m_AnimationCoroutine != null)
                StopCoroutine(m_AnimationCoroutine);
            
            m_AnimationCoroutine = StartCoroutine(AnimationFadeIn());

            Log("Hiển thị Pickup UI");
        }

        /// <summary>
        /// Ẩn UI
        /// </summary>
        public void AnUI()
        {
            if (!m_UIVisible) return;

            m_UIVisible = false;
            m_CurrentItem = null;

            // Animation fade out
            if (m_AnimationCoroutine != null)
                StopCoroutine(m_AnimationCoroutine);
            
            m_AnimationCoroutine = StartCoroutine(AnimationFadeOut());

            Log("Ẩn Pickup UI");
        }

        /// <summary>
        /// Cập nhật thông tin vật phẩm
        /// </summary>
        public void CapNhatThongTinVatPham(PickupItem item)
        {
            if (item == null) return;

            // Cập nhật text
            if (m_TextTenVatPham != null)
                m_TextTenVatPham.text = item.TenHienThi;

            if (m_TextSoLuong != null)
                m_TextSoLuong.text = $"x{item.SoLuong}";

            if (m_TextMoTa != null)
                m_TextMoTa.text = item.MoTa;

            if (m_TextHuongDan != null)
                m_TextHuongDan.text = "Nhấn [E] để nhặt";

            // Cập nhật icon
            if (m_IconVatPham != null && item.Icon != null)
            {
                m_IconVatPham.sprite = item.Icon;
                m_IconVatPham.gameObject.SetActive(true);
            }
            else if (m_IconVatPham != null)
            {
                m_IconVatPham.gameObject.SetActive(false);
            }

            Log($"Cập nhật UI cho: {item.TenHienThi}");
        }

        /// <summary>
        /// Thiết lập crosshair
        /// </summary>
        public void ThietLapCrosshair(CrosshairState state)
        {
            // Ẩn tất cả crosshair
            if (m_CrosshairNormal != null) m_CrosshairNormal.gameObject.SetActive(false);
            if (m_CrosshairPickup != null) m_CrosshairPickup.gameObject.SetActive(false);
            if (m_CrosshairCarrying != null) m_CrosshairCarrying.gameObject.SetActive(false);

            // Hiển thị crosshair phù hợp
            switch (state)
            {
                case CrosshairState.Normal:
                    if (m_CrosshairNormal != null) m_CrosshairNormal.gameObject.SetActive(true);
                    break;
                case CrosshairState.CanPickup:
                    if (m_CrosshairPickup != null) m_CrosshairPickup.gameObject.SetActive(true);
                    break;
                case CrosshairState.Carrying:
                    if (m_CrosshairCarrying != null) m_CrosshairCarrying.gameObject.SetActive(true);
                    break;
            }
        }

        /// <summary>
        /// Cập nhật progress bar
        /// </summary>
        public void CapNhatProgressBar(float progress)
        {
            if (m_ProgressBar != null)
            {
                m_ProgressBar.value = progress;
                m_ProgressBar.gameObject.SetActive(progress > 0f && progress < 1f);
            }
        }

        /// <summary>
        /// Hiển thị thông báo lỗi
        /// </summary>
        public void HienThiThongBaoLoi(string message)
        {
            if (m_TextHuongDan != null)
            {
                m_TextHuongDan.text = message;
                m_TextHuongDan.color = m_MauTextLoi;
                
                // Reset màu sau 2 giây
                StartCoroutine(ResetTextColor());
            }
        }
        #endregion

        #region Animation
        private IEnumerator AnimationFadeIn()
        {
            if (m_CanvasGroup == null) yield break;

            // Scale animation
            if (m_CoScaleAnimation && m_MainPanel != null)
            {
                m_MainPanel.transform.localScale = Vector3.one * m_ScaleBanDau;

                float scaleElapsedTime = 0f;
                while (scaleElapsedTime < m_ThoiGianScale)
                {
                    scaleElapsedTime += Time.deltaTime;
                    float progress = scaleElapsedTime / m_ThoiGianScale;
                    float scale = Mathf.Lerp(m_ScaleBanDau, 1f, progress);
                    m_MainPanel.transform.localScale = Vector3.one * scale;
                    yield return null;
                }

                m_MainPanel.transform.localScale = Vector3.one;
            }

            // Fade in
            float startAlpha = m_CanvasGroup.alpha;
            float fadeElapsedTime = 0f;
            float fadeTime = 1f / m_TocDoFade;

            while (fadeElapsedTime < fadeTime)
            {
                fadeElapsedTime += Time.deltaTime;
                float progress = fadeElapsedTime / fadeTime;
                m_CanvasGroup.alpha = Mathf.Lerp(startAlpha, 1f, progress);
                yield return null;
            }

            m_CanvasGroup.alpha = 1f;
        }

        private IEnumerator AnimationFadeOut()
        {
            if (m_CanvasGroup == null) yield break;

            float startAlpha = m_CanvasGroup.alpha;
            float elapsedTime = 0f;
            float fadeTime = 1f / m_TocDoFade;

            while (elapsedTime < fadeTime)
            {
                elapsedTime += Time.deltaTime;
                float progress = elapsedTime / fadeTime;
                m_CanvasGroup.alpha = Mathf.Lerp(startAlpha, 0f, progress);
                yield return null;
            }

            m_CanvasGroup.alpha = 0f;
            
            if (m_MainPanel != null)
                m_MainPanel.SetActive(false);
        }

        private IEnumerator ResetTextColor()
        {
            yield return new WaitForSeconds(2f);
            
            if (m_TextHuongDan != null)
            {
                m_TextHuongDan.color = m_MauTextBinhThuong;
                if (m_CurrentItem != null)
                {
                    m_TextHuongDan.text = "Nhấn [E] để nhặt";
                }
            }
        }
        #endregion

        #region Utility
        private void Log(string message)
        {
            if (m_EnableDebugLogs)
                Debug.Log($"[PickupUI] {message}");
        }
        #endregion

        #region Enums
        public enum CrosshairState
        {
            Normal,
            CanPickup,
            Carrying
        }
        #endregion

        #region Editor Helpers
#if UNITY_EDITOR
        [ContextMenu("Test Show UI")]
        private void TestShowUI()
        {
            HienThiUI();
        }

        [ContextMenu("Test Hide UI")]
        private void TestHideUI()
        {
            AnUI();
        }
#endif
        #endregion
    }
}
