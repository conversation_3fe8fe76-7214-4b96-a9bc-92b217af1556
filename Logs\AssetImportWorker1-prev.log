Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.50f1 (f1ef1dca8bff) revision 15855389'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-06-13T07:09:05Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.50f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
E:/2025/Boder/My boder 3
-logFile
Logs/AssetImportWorker1.log
-srvPort
65181
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/My boder 3
E:/2025/Boder/My boder 3
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [24868]  Target information:

Player connection [24868]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2293557679 [EditorId] 2293557679 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [24868] Host joined multi-casting on [***********:54997]...
Player connection [24868] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 129.72 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.50f1 (f1ef1dca8bff)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.50f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/My boder 3/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.50f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56876
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.50f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002148 seconds.
- Loaded All Assemblies, in  3.075 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.392 seconds
Domain Reload Profiling: 3467ms
	BeginReloadAssembly (588ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (442ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (49ms)
	LoadAllAssembliesAndSetupDomain (1986ms)
		LoadAssemblies (587ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1983ms)
			TypeCache.Refresh (1981ms)
				TypeCache.ScanAssembly (1288ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (393ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (346ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (27ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (68ms)
			ProcessInitializeOnLoadAttributes (173ms)
			ProcessInitializeOnLoadMethodAttributes (72ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.931 seconds
Refreshing native plugins compatible for Editor in 0.66 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.469 seconds
Domain Reload Profiling: 4399ms
	BeginReloadAssembly (174ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (2679ms)
		LoadAssemblies (2286ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (490ms)
			TypeCache.Refresh (408ms)
				TypeCache.ScanAssembly (297ms)
			BuildScriptInfoCaches (68ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1470ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (566ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (112ms)
			ProcessInitializeOnLoadAttributes (387ms)
			ProcessInitializeOnLoadMethodAttributes (60ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 0.81 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 225 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6629 unused Assets / (7.7 MB). Loaded Objects now: 7303.
Memory consumption went from 171.3 MB to 163.6 MB.
Total: 14.878600 ms (FindLiveObjects: 0.800400 ms CreateObjectMapping: 0.984100 ms MarkObjects: 7.142600 ms  DeleteObjects: 5.949900 ms)

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.600 seconds
Refreshing native plugins compatible for Editor in 0.63 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.753 seconds
Domain Reload Profiling: 1356ms
	BeginReloadAssembly (186ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (47ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (351ms)
		LoadAssemblies (278ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (161ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (140ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (754ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (525ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (108ms)
			ProcessInitializeOnLoadAttributes (355ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 1.05 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 43 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6624 unused Assets / (7.5 MB). Loaded Objects now: 7319.
Memory consumption went from 155.1 MB to 147.6 MB.
Total: 10.946000 ms (FindLiveObjects: 0.795800 ms CreateObjectMapping: 0.976200 ms MarkObjects: 5.053500 ms  DeleteObjects: 4.119000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.641 seconds
Refreshing native plugins compatible for Editor in 0.62 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.672 seconds
Domain Reload Profiling: 1317ms
	BeginReloadAssembly (185ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (391ms)
		LoadAssemblies (291ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (184ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (163ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (673ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (513ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (112ms)
			ProcessInitializeOnLoadAttributes (332ms)
			ProcessInitializeOnLoadMethodAttributes (60ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 0.81 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 32 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6624 unused Assets / (7.6 MB). Loaded Objects now: 7321.
Memory consumption went from 153.3 MB to 145.7 MB.
Total: 11.914200 ms (FindLiveObjects: 0.913700 ms CreateObjectMapping: 0.853200 ms MarkObjects: 5.589400 ms  DeleteObjects: 4.556400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.68 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 32 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6617 unused Assets / (7.9 MB). Loaded Objects now: 7321.
Memory consumption went from 153.4 MB to 145.6 MB.
Total: 31.285800 ms (FindLiveObjects: 11.850300 ms CreateObjectMapping: 1.345100 ms MarkObjects: 10.543500 ms  DeleteObjects: 7.544800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.630 seconds
Refreshing native plugins compatible for Editor in 0.61 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.681 seconds
Domain Reload Profiling: 1315ms
	BeginReloadAssembly (189ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (376ms)
		LoadAssemblies (281ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (179ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (156ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (682ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (522ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (119ms)
			ProcessInitializeOnLoadAttributes (332ms)
			ProcessInitializeOnLoadMethodAttributes (60ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 0.66 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 32 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6624 unused Assets / (7.4 MB). Loaded Objects now: 7323.
Memory consumption went from 153.3 MB to 145.9 MB.
Total: 9.958000 ms (FindLiveObjects: 0.763200 ms CreateObjectMapping: 0.850100 ms MarkObjects: 4.692800 ms  DeleteObjects: 3.651300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.583 seconds
Refreshing native plugins compatible for Editor in 0.57 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.658 seconds
Domain Reload Profiling: 1244ms
	BeginReloadAssembly (176ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (46ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (349ms)
		LoadAssemblies (265ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (161ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (141ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (658ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (509ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (109ms)
			ProcessInitializeOnLoadAttributes (331ms)
			ProcessInitializeOnLoadMethodAttributes (59ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 0.67 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 32 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6624 unused Assets / (7.5 MB). Loaded Objects now: 7325.
Memory consumption went from 153.3 MB to 145.8 MB.
Total: 10.862800 ms (FindLiveObjects: 0.885500 ms CreateObjectMapping: 0.862500 ms MarkObjects: 4.827800 ms  DeleteObjects: 4.285900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.622 seconds
Refreshing native plugins compatible for Editor in 0.55 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.620 seconds
Domain Reload Profiling: 1245ms
	BeginReloadAssembly (181ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (44ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (381ms)
		LoadAssemblies (301ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (163ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (145ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (620ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (478ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (109ms)
			ProcessInitializeOnLoadAttributes (311ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 0.74 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 32 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6624 unused Assets / (7.6 MB). Loaded Objects now: 7327.
Memory consumption went from 153.4 MB to 145.8 MB.
Total: 11.794700 ms (FindLiveObjects: 0.867800 ms CreateObjectMapping: 1.139100 ms MarkObjects: 4.840300 ms  DeleteObjects: 4.946300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.632 seconds
Refreshing native plugins compatible for Editor in 0.61 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.637 seconds
Domain Reload Profiling: 1273ms
	BeginReloadAssembly (177ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (44ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (394ms)
		LoadAssemblies (305ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (167ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (146ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (637ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (489ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (107ms)
			ProcessInitializeOnLoadAttributes (314ms)
			ProcessInitializeOnLoadMethodAttributes (58ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 0.89 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 32 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6624 unused Assets / (7.8 MB). Loaded Objects now: 7329.
Memory consumption went from 153.4 MB to 145.6 MB.
Total: 11.278800 ms (FindLiveObjects: 0.785200 ms CreateObjectMapping: 1.140000 ms MarkObjects: 4.989400 ms  DeleteObjects: 4.363000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.602 seconds
Refreshing native plugins compatible for Editor in 0.66 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.660 seconds
Domain Reload Profiling: 1265ms
	BeginReloadAssembly (184ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (48ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (352ms)
		LoadAssemblies (274ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (163ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (143ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (660ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (511ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (112ms)
			ProcessInitializeOnLoadAttributes (332ms)
			ProcessInitializeOnLoadMethodAttributes (58ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 0.97 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 32 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6624 unused Assets / (7.2 MB). Loaded Objects now: 7331.
Memory consumption went from 153.5 MB to 146.2 MB.
Total: 10.988500 ms (FindLiveObjects: 0.770600 ms CreateObjectMapping: 0.924700 ms MarkObjects: 5.247000 ms  DeleteObjects: 4.044200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.151 seconds
Refreshing native plugins compatible for Editor in 1.10 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.667 seconds
Domain Reload Profiling: 1821ms
	BeginReloadAssembly (332ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (78ms)
	RebuildCommonClasses (80ms)
	RebuildNativeTypeToScriptingClass (35ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (670ms)
		LoadAssemblies (511ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (308ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (275ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (667ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (496ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (112ms)
			ProcessInitializeOnLoadAttributes (323ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 0.80 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 32 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6624 unused Assets / (7.9 MB). Loaded Objects now: 7333.
Memory consumption went from 153.5 MB to 145.6 MB.
Total: 10.507100 ms (FindLiveObjects: 0.762100 ms CreateObjectMapping: 0.827000 ms MarkObjects: 4.750700 ms  DeleteObjects: 4.166300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.989 seconds
Refreshing native plugins compatible for Editor in 0.63 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.730 seconds
Domain Reload Profiling: 1721ms
	BeginReloadAssembly (341ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (73ms)
	RebuildCommonClasses (56ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (535ms)
		LoadAssemblies (533ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (175ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (155ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (731ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (561ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (121ms)
			ProcessInitializeOnLoadAttributes (371ms)
			ProcessInitializeOnLoadMethodAttributes (59ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 1.00 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 32 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6624 unused Assets / (8.3 MB). Loaded Objects now: 7335.
Memory consumption went from 153.5 MB to 145.2 MB.
Total: 12.932300 ms (FindLiveObjects: 0.771800 ms CreateObjectMapping: 0.996300 ms MarkObjects: 5.341200 ms  DeleteObjects: 5.821500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.598 seconds
Refreshing native plugins compatible for Editor in 0.60 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.638 seconds
Domain Reload Profiling: 1240ms
	BeginReloadAssembly (185ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (53ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (354ms)
		LoadAssemblies (271ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (161ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (142ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (639ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (497ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (108ms)
			ProcessInitializeOnLoadAttributes (328ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 0.76 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 32 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6624 unused Assets / (7.8 MB). Loaded Objects now: 7337.
Memory consumption went from 153.6 MB to 145.7 MB.
Total: 12.217000 ms (FindLiveObjects: 0.798600 ms CreateObjectMapping: 1.145900 ms MarkObjects: 5.505100 ms  DeleteObjects: 4.766100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.615 seconds
Refreshing native plugins compatible for Editor in 0.62 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.653 seconds
Domain Reload Profiling: 1271ms
	BeginReloadAssembly (185ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (370ms)
		LoadAssemblies (272ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (177ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (157ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (653ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (506ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (113ms)
			ProcessInitializeOnLoadAttributes (324ms)
			ProcessInitializeOnLoadMethodAttributes (60ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 0.96 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 32 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6624 unused Assets / (7.5 MB). Loaded Objects now: 7339.
Memory consumption went from 153.6 MB to 146.0 MB.
Total: 13.446300 ms (FindLiveObjects: 0.880700 ms CreateObjectMapping: 1.188600 ms MarkObjects: 6.727500 ms  DeleteObjects: 4.648300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.697 seconds
Refreshing native plugins compatible for Editor in 0.62 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.670 seconds
Domain Reload Profiling: 1371ms
	BeginReloadAssembly (197ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (56ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (437ms)
		LoadAssemblies (332ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (192ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (169ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (670ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (518ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (116ms)
			ProcessInitializeOnLoadAttributes (346ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 0.68 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 32 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6627 unused Assets / (7.4 MB). Loaded Objects now: 7344.
Memory consumption went from 153.6 MB to 146.2 MB.
Total: 10.489800 ms (FindLiveObjects: 0.756300 ms CreateObjectMapping: 0.833100 ms MarkObjects: 4.858000 ms  DeleteObjects: 4.041300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.601 seconds
Refreshing native plugins compatible for Editor in 0.69 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.616 seconds
Domain Reload Profiling: 1220ms
	BeginReloadAssembly (186ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (48ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (357ms)
		LoadAssemblies (281ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (162ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (143ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (616ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (478ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (104ms)
			ProcessInitializeOnLoadAttributes (310ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 0.80 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 32 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6627 unused Assets / (7.7 MB). Loaded Objects now: 7346.
Memory consumption went from 153.7 MB to 146.0 MB.
Total: 12.530700 ms (FindLiveObjects: 0.807500 ms CreateObjectMapping: 1.011900 ms MarkObjects: 5.396600 ms  DeleteObjects: 5.313600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.646 seconds
Refreshing native plugins compatible for Editor in 0.60 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.672 seconds
Domain Reload Profiling: 1321ms
	BeginReloadAssembly (194ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (52ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (382ms)
		LoadAssemblies (285ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (178ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (151ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (672ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (518ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (115ms)
			ProcessInitializeOnLoadAttributes (343ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 0.78 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 32 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6633 unused Assets / (7.7 MB). Loaded Objects now: 7354.
Memory consumption went from 153.7 MB to 146.0 MB.
Total: 11.623000 ms (FindLiveObjects: 0.815500 ms CreateObjectMapping: 0.938700 ms MarkObjects: 5.428800 ms  DeleteObjects: 4.438300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.48 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 32 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6630 unused Assets / (8.2 MB). Loaded Objects now: 7358.
Memory consumption went from 153.9 MB to 145.7 MB.
Total: 18.925100 ms (FindLiveObjects: 1.062900 ms CreateObjectMapping: 1.516600 ms MarkObjects: 9.451400 ms  DeleteObjects: 6.892500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.59 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 32 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6630 unused Assets / (7.4 MB). Loaded Objects now: 7358.
Memory consumption went from 153.9 MB to 146.6 MB.
Total: 18.754300 ms (FindLiveObjects: 1.239600 ms CreateObjectMapping: 1.700800 ms MarkObjects: 9.693200 ms  DeleteObjects: 6.118800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.606 seconds
Refreshing native plugins compatible for Editor in 0.56 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.665 seconds
Domain Reload Profiling: 1275ms
	BeginReloadAssembly (184ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (46ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (361ms)
		LoadAssemblies (278ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (167ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (143ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (666ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (501ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (110ms)
			ProcessInitializeOnLoadAttributes (327ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 0.75 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 32 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6637 unused Assets / (7.5 MB). Loaded Objects now: 7360.
Memory consumption went from 153.8 MB to 146.4 MB.
Total: 9.855900 ms (FindLiveObjects: 0.828600 ms CreateObjectMapping: 0.855800 ms MarkObjects: 4.452600 ms  DeleteObjects: 3.717800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.598 seconds
Refreshing native plugins compatible for Editor in 0.63 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.643 seconds
Domain Reload Profiling: 1244ms
	BeginReloadAssembly (185ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (354ms)
		LoadAssemblies (258ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (174ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (155ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (643ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (493ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (113ms)
			ProcessInitializeOnLoadAttributes (314ms)
			ProcessInitializeOnLoadMethodAttributes (59ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 0.90 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 32 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6637 unused Assets / (7.5 MB). Loaded Objects now: 7362.
Memory consumption went from 153.8 MB to 146.3 MB.
Total: 12.631800 ms (FindLiveObjects: 0.896500 ms CreateObjectMapping: 1.006200 ms MarkObjects: 6.494300 ms  DeleteObjects: 4.233900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.583 seconds
Refreshing native plugins compatible for Editor in 0.61 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.626 seconds
Domain Reload Profiling: 1212ms
	BeginReloadAssembly (179ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (46ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (344ms)
		LoadAssemblies (264ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (161ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (141ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (626ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (478ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (109ms)
			ProcessInitializeOnLoadAttributes (309ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 0.80 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 32 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6637 unused Assets / (7.9 MB). Loaded Objects now: 7364.
Memory consumption went from 153.9 MB to 146.0 MB.
Total: 12.076400 ms (FindLiveObjects: 0.815600 ms CreateObjectMapping: 0.997000 ms MarkObjects: 5.612100 ms  DeleteObjects: 4.650400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.599 seconds
Refreshing native plugins compatible for Editor in 0.62 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.633 seconds
Domain Reload Profiling: 1235ms
	BeginReloadAssembly (186ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (47ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (354ms)
		LoadAssemblies (269ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (168ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (143ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (634ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (482ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (105ms)
			ProcessInitializeOnLoadAttributes (319ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 1.15 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 32 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6637 unused Assets / (7.5 MB). Loaded Objects now: 7366.
Memory consumption went from 153.9 MB to 146.4 MB.
Total: 12.248100 ms (FindLiveObjects: 0.780200 ms CreateObjectMapping: 0.880800 ms MarkObjects: 6.232600 ms  DeleteObjects: 4.353400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 0.90 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 32 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6631 unused Assets / (7.1 MB). Loaded Objects now: 7367.
Memory consumption went from 154.1 MB to 147.0 MB.
Total: 11.580000 ms (FindLiveObjects: 0.854100 ms CreateObjectMapping: 0.938200 ms MarkObjects: 5.151600 ms  DeleteObjects: 4.635100 ms)

Prepare: number of updated asset objects reloaded= 0
